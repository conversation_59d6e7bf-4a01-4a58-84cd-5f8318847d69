#!/usr/bin/env python3
"""
测试SpecificState事件处理器集成
"""

import asyncio
import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_specificstate_event():
    """测试SpecificState事件处理器集成"""
    print("🚀 测试SpecificState事件处理器集成...")
    
    try:
        from api.main import SpecificAPI
        from api.schemas import ChatRequest
        
        # 初始化API
        api = SpecificAPI()
        
        # 创建包含事件回调的请求
        request = ChatRequest(
            message="你好",
            session_id="test_specific_001",
            task_id="task_12345",
            sandbox_id="sandbox_67890",
            event_webhook="https://xuanji-dev.chehejia.com/webhook",
            user_id="test_user"
        )
        
        print("📤 发送包含事件回调的请求:")
        print(f"  Message: {request.message}")
        print(f"  Session ID: {request.session_id}")
        print(f"  Task ID: {request.task_id}")
        print(f"  Event Webhook: {request.event_webhook}")
        
        # 调用API，这应该会获取完整的SpecificState并发送事件
        response = await api.chat(request)
        
        print(f"\n📥 收到响应:")
        print(f"  Session ID: {response.session_id}")
        print(f"  Status: {response.status}")
        print(f"  Response: {response.response}")
        print(f"  Task ID: {response.task_id}")
        print(f"  Event Webhook: {response.event_webhook}")
        print(f"  Metadata: {response.metadata}")
        
        # 测试后续对话
        print("\n🔄 测试后续对话...")
        follow_up_request = ChatRequest(
            message="我想分析京东的市场情况",
            session_id="test_specific_001",  # 使用相同的session_id
            task_id="task_12345",
            sandbox_id="sandbox_67890",
            event_webhook="https://xuanji-dev.chehejia.com/webhook",
            user_id="test_user"
        )
        
        follow_up_response = await api.chat(follow_up_request)
        
        print(f"📥 后续响应:")
        print(f"  Status: {follow_up_response.status}")
        print(f"  Response: {follow_up_response.response[:100]}...")
        print(f"  Task ID: {follow_up_response.task_id}")
        
        print("\n✅ SpecificState事件处理器集成测试完成!")
        print("💡 注意：助手消息事件应该已经发送到配置的webhook地址")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_state_access_methods():
    """测试状态访问方法"""
    print("\n🔧 测试状态访问方法...")
    
    try:
        from models.state import SpecificState, WorkflowStatus
        from event.manus_event_handler import get_context
        
        # 创建模拟的SpecificState对象
        class MockSpecificState:
            def __init__(self):
                self.session_id = "test_session_001"
                self.task_id = "test_task_001"
                self.sandbox_id = "test_sandbox_001"
                self.event_webhook = "https://test.webhook.com"
                self.extensions = {"test": "value"}
                self.workflow_status = WorkflowStatus.INITIALIZING
                self.human_approval_required = False
                self.intent_clarified = True
                self.intent_approved = False
                self.plan_approved = False
                self.messages = []
        
        mock_state = MockSpecificState()
        
        print("📤 测试SpecificState对象访问:")
        
        # 测试get_context函数
        context = get_context(mock_state)
        print(f"  get_context结果: {context}")
        
        # 测试getattr访问
        session_id = getattr(mock_state, "session_id", "None")
        task_id = getattr(mock_state, "task_id", "None")
        workflow_status = getattr(mock_state, "workflow_status", WorkflowStatus.INITIALIZING)
        
        print(f"  getattr访问结果:")
        print(f"    session_id: {session_id}")
        print(f"    task_id: {task_id}")
        print(f"    workflow_status: {workflow_status}")
        
        # 测试字典格式的状态
        dict_state = {
            "session_id": "test_session_002",
            "task_id": "test_task_002",
            "sandbox_id": "test_sandbox_002",
            "event_webhook": "https://test2.webhook.com",
            "extensions": {"test2": "value2"},
            "workflow_status": WorkflowStatus.PLANNING,
            "human_approval_required": True,
            "intent_clarified": False,
            "intent_approved": True,
            "plan_approved": False,
            "messages": []
        }
        
        print("📤 测试字典状态访问:")
        
        # 测试get_context函数
        context = get_context(dict_state)
        print(f"  get_context结果: {context}")
        
        # 测试get访问
        session_id = dict_state.get("session_id", "None")
        task_id = dict_state.get("task_id", "None")
        workflow_status = dict_state.get("workflow_status", WorkflowStatus.INITIALIZING)
        
        print(f"  get访问结果:")
        print(f"    session_id: {session_id}")
        print(f"    task_id: {task_id}")
        print(f"    workflow_status: {workflow_status}")
        
        print("✅ 状态访问方法测试完成!")
        
    except Exception as e:
        print(f"❌ 状态访问测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_event_handler_with_state():
    """测试事件处理器与状态对象"""
    print("\n🔧 测试事件处理器与状态对象...")
    
    try:
        from event.manus_event_handler import event_handler
        from models.state import WorkflowStatus
        
        # 创建模拟的SpecificState对象
        class MockSpecificState:
            def __init__(self):
                self.session_id = "test_event_001"
                self.task_id = "task_event_001"
                self.sandbox_id = "sandbox_event_001"
                self.event_webhook = "https://xuanji-dev.chehejia.com/webhook"
                self.extensions = {"test": "value"}
        
        mock_state = MockSpecificState()
        
        print("📤 发送事件（使用SpecificState对象）:")
        
        # 测试发送助手消息
        event_handler.send_agent_message(
            state=mock_state,
            content="这是一条使用SpecificState对象的测试助手消息"
        )
        print("  ✅ 助手消息事件已发送（SpecificState对象）")
        
        # 测试发送实时状态
        event_handler.send_live_status(
            text="使用SpecificState对象的实时状态更新",
            state=mock_state
        )
        print("  ✅ 实时状态事件已发送（SpecificState对象）")
        
        # 测试字典格式
        dict_state = {
            "session_id": "test_event_002",
            "task_id": "task_event_002",
            "sandbox_id": "sandbox_event_002",
            "event_webhook": "https://xuanji-dev.chehejia.com/webhook",
            "extensions": {"test": "value"}
        }
        
        print("📤 发送事件（使用字典状态）:")
        
        # 测试发送助手消息
        event_handler.send_agent_message(
            state=dict_state,
            content="这是一条使用字典状态的测试助手消息"
        )
        print("  ✅ 助手消息事件已发送（字典状态）")
        
        print("✅ 事件处理器与状态对象测试完成!")
        
    except Exception as e:
        print(f"❌ 事件处理器测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_state_access_methods()
    test_event_handler_with_state()
    asyncio.run(test_specificstate_event())
