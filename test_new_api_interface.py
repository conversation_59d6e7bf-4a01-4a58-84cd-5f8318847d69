#!/usr/bin/env python3
"""
测试新的API接口参数
"""

import asyncio
import sys
import os
import json

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_new_api_interface():
    """测试新的API接口"""
    print("🚀 测试新的API接口...")
    
    try:
        from api.main import SpecificAPI
        from api.schemas import ChatRequest, ServiceToken, Extensions
        
        # 初始化API
        api = SpecificAPI()
        
        # 创建服务token
        service_token = ServiceToken(
            service_id="test_service_001",
            access_token="test_access_token_12345"
        )
        
        # 创建扩展字段
        extensions = Extensions(
            tokens=[service_token],
            additional_fields={
                "custom_field_1": "custom_value_1",
                "custom_field_2": {"nested": "value"}
            }
        )
        
        # 创建新格式的请求
        request = ChatRequest(
            message="你好，我想分析一下京东的市场情况",
            session_id="test_new_api_001",
            task_id="task_12345",
            sandbox_id="sandbox_67890",
            event_webhook="https://xuanji-dev.chehejia.com/webhook",
            extensions=extensions,
            user_id="test_user"  # 向后兼容字段
        )
        
        print("📤 发送新格式请求:")
        print(f"  Message: {request.message}")
        print(f"  Session ID: {request.session_id}")
        print(f"  Task ID: {request.task_id}")
        print(f"  Sandbox ID: {request.sandbox_id}")
        print(f"  Event Webhook: {request.event_webhook}")
        print(f"  Extensions: {request.extensions}")
        
        # 调用API
        response = await api.chat(request)
        
        print(f"\n📥 收到响应:")
        print(f"  Session ID: {response.session_id}")
        print(f"  Task ID: {response.task_id}")
        print(f"  Sandbox ID: {response.sandbox_id}")
        print(f"  Event Webhook: {response.event_webhook}")
        print(f"  Extensions: {response.extensions}")
        print(f"  Status: {response.status}")
        print(f"  Requires Feedback: {response.requires_feedback}")
        print(f"  Response: {response.response}")
        print(f"  Metadata: {response.metadata}")
        
        # 测试JSON序列化
        print(f"\n🔄 测试JSON序列化:")
        response_json = response.model_dump()
        print(f"  JSON: {json.dumps(response_json, ensure_ascii=False, indent=2)}")
        
        # 测试后续对话（不包含扩展字段）
        print("\n🔄 测试后续对话...")
        follow_up_request = ChatRequest(
            message="请详细分析一下",
            session_id="test_new_api_001",  # 使用相同的session_id
            task_id="task_12345",  # 保持task_id
            user_id="test_user"
        )
        
        follow_up_response = await api.chat(follow_up_request)
        
        print(f"📥 后续响应:")
        print(f"  Session ID: {follow_up_response.session_id}")
        print(f"  Task ID: {follow_up_response.task_id}")
        print(f"  Response: {follow_up_response.response}")
        
        print("\n✅ 新API接口测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n🔄 测试向后兼容性...")
    
    try:
        from api.main import SpecificAPI
        from api.schemas import ChatRequest
        
        # 初始化API
        api = SpecificAPI()
        
        # 创建旧格式的请求（只包含原有字段）
        old_request = ChatRequest(
            message="你好",
            session_id="test_old_api_001",
            user_id="test_user"
        )
        
        print(f"📤 发送旧格式请求: {old_request.message}")
        
        # 调用API
        response = await api.chat(old_request)
        
        print(f"📥 收到响应: {response.response}")
        print(f"  新字段为空: task_id={response.task_id}, sandbox_id={response.sandbox_id}")
        
        print("✅ 向后兼容性测试通过!")
        
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_new_api_interface())
    asyncio.run(test_backward_compatibility())
