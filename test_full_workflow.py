#!/usr/bin/env python3
"""
测试完整工作流的流式API
"""

import asyncio
import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_full_workflow():
    """测试完整工作流"""
    print("🚀 测试完整工作流...")
    
    try:
        from api.main import SpecificAPI
        from api.schemas import ChatRequest
        
        # 初始化API
        api = SpecificAPI()
        session_id = "test_full_001"
        
        # 1. 测试问候
        print("\n1. 测试问候...")
        request = ChatRequest(
            message="你好",
            session_id=session_id,
            user_id="test_user"
        )
        
        response = await api.chat(request)
        print(f"问候响应: {response.response}")
        print(f"状态: {response.status}")
        
        # 2. 测试任务请求
        print("\n2. 测试任务请求...")
        request = ChatRequest(
            message="分析京东外卖业务的市场竞争情况",
            session_id=session_id,
            user_id="test_user"
        )
        
        response = await api.chat(request)
        print(f"任务响应: {response.response[:200]}...")
        print(f"状态: {response.status}")
        print(f"需要反馈: {response.requires_feedback}")
        
        # 3. 如果需要澄清，提供澄清信息
        if response.requires_feedback and "澄清" in response.status:
            print("\n3. 提供澄清信息...")
            request = ChatRequest(
                message="我想分析京东外卖在2025年与美团、饿了么的竞争态势，重点关注市场份额和用户增长",
                session_id=session_id,
                user_id="test_user"
            )
            
            response = await api.chat(request)
            print(f"澄清响应: {response.response[:200]}...")
            print(f"状态: {response.status}")
            print(f"需要反馈: {response.requires_feedback}")
        
        # 4. 如果需要确认，提供确认
        if response.requires_feedback:
            print("\n4. 确认计划...")
            request = ChatRequest(
                message="好的，请按照这个计划执行",
                session_id=session_id,
                user_id="test_user"
            )
            
            response = await api.chat(request)
            print(f"确认响应: {response.response[:200]}...")
            print(f"状态: {response.status}")
        
        print("\n✅ 完整工作流测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_full_workflow())
