#!/usr/bin/env python3
"""
测试async ExecutionAgent
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


async def test_async_execution():
    """测试async ExecutionAgent"""
    print("🚀 测试async ExecutionAgent...")
    
    try:
        from src.agents.execution import ExecutionAgent
        from src.models.state import WorkflowStatus
        
        # 创建模拟LLM
        class MockLLM:
            async def ainvoke(self, messages):
                class MockResponse:
                    content = """### 步骤执行报告

**执行内容**：分析京东外卖品牌数据收集需求

**工具使用**：使用了React Agent和MCP工具进行智能执行

**执行过程**：
1. React Agent分析了任务需求
2. 自动选择了合适的工具
3. 执行了数据收集操作
4. 验证了结果质量

**执行结果**：成功完成了步骤目标，收集了相关数据

**下一步准备**：为下一步分析做好了准备"""
                return MockResponse()
            
            def bind_tools(self, tools):
                return self
        
        # 创建ExecutionAgent
        mock_llm = MockLLM()
        agent = ExecutionAgent(llm=mock_llm, enable_mcp=True)
        
        print(f"✅ Agent创建成功")
        print(f"🔧 MCP启用: {agent.enable_mcp}")
        print(f"💪 Agent能力: {len(agent.get_capabilities())} 项")
        
        # 创建测试状态
        test_state = {
            "session_id": "test_async_001",
            "user_input": "请分析京东外卖品牌表现",
            "task_plan": {
                "title": "京东外卖品牌分析",
                "steps": [
                    {
                        "title": "数据收集",
                        "description": "收集京东外卖相关数据"
                    },
                    {
                        "title": "数据分析",
                        "description": "分析收集到的数据"
                    },
                    {
                        "title": "报告生成",
                        "description": "生成分析报告"
                    }
                ]
            },
            "workflow_status": WorkflowStatus.EXECUTING,
            "messages": []
        }
        
        print(f"📝 测试计划: {test_state['task_plan']['title']}")
        print(f"📊 步骤数量: {len(test_state['task_plan']['steps'])}")
        
        # 测试async execute
        print("\n⚡ 开始async执行...")
        result = await agent.execute(test_state)
        
        print(f"✅ async执行完成，路由到: {result.goto}")
        
        # 检查结果
        if "execution_results" in result.update:
            results = result.update["execution_results"]
            print(f"📈 执行统计:")
            print(f"  - 总步骤: {len(results)}")
            print(f"  - 成功步骤: {len([r for r in results if not r.get('error', False)])}")
            
            # 显示每个步骤的结果
            for i, step_result in enumerate(results):
                status = "✅" if not step_result.get('error', False) else "❌"
                print(f"  {status} 步骤 {i+1}: {step_result['step_title']}")
        
        if "execution_report" in result.update:
            report = result.update["execution_report"]
            print(f"📝 生成了执行报告 ({len(report)} 字符)")
            print(f"📄 报告预览:")
            print(report[:200] + "..." if len(report) > 200 else report)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_single_step():
    """测试单步async执行"""
    print("\n🎯 测试单步async执行...")
    
    try:
        from src.agents.execution import ExecutionAgent
        
        # 创建模拟LLM
        class MockLLM:
            async def ainvoke(self, messages):
                class MockResponse:
                    content = """### 步骤1执行报告

**执行内容**：数据收集任务

**工具使用**：使用了文件系统工具和React Agent

**执行过程**：
1. React Agent分析了任务
2. 选择了文件系统工具
3. 执行了数据搜索
4. 整理了结果

**执行结果**：成功收集了目标数据

**下一步准备**：数据已准备好进行分析"""
                return MockResponse()
            
            def bind_tools(self, tools):
                return self
        
        # 创建Agent
        mock_llm = MockLLM()
        agent = ExecutionAgent(llm=mock_llm, enable_mcp=True)
        
        # 测试单步执行
        step_result = await agent.execute_step(
            user_input="请分析京东外卖品牌表现",
            plan_title="京东外卖品牌分析",
            current_step=0,
            step_details={
                "title": "数据收集",
                "description": "收集京东外卖相关数据和用户反馈"
            }
        )
        
        print(f"✅ 单步async执行成功")
        print(f"📝 结果长度: {len(step_result)} 字符")
        print(f"📄 结果预览: {step_result[:150]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 单步测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("🧪 Async ExecutionAgent测试")
    print("=" * 50)
    
    # 检查环境
    print("🔍 检查环境...")
    
    # 检查MCP可用性
    try:
        from langchain_mcp_adapters.client import MultiServerMCPClient
        print("✅ MCP适配器可用")
        mcp_available = True
    except ImportError:
        print("❌ MCP适配器不可用")
        mcp_available = False
    
    # 检查API密钥
    if os.getenv("OPENAI_API_KEY"):
        print("✅ OpenAI API密钥已设置")
    else:
        print("⚠️ OpenAI API密钥未设置，将使用模拟LLM")
    
    print()
    
    # 运行测试
    tests = [
        ("单步async执行", test_single_step),
        ("完整async执行", test_async_execution),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"🧪 运行测试: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
        print()
    
    # 汇总结果
    print("📊 测试结果:")
    passed = 0
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总结: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！Async ExecutionAgent工作正常。")
    else:
        print("⚠️ 部分测试失败，请检查配置。")
    
    print("\n💡 关键改进:")
    print("1. ✅ execute方法现在是async，无需asyncio.run")
    print("2. ✅ 直接await execute_step，代码更简洁")
    print("3. ✅ React Agent处理每个步骤")
    print("4. ✅ MCP工具自动集成")
    print("5. ✅ 保持向后兼容性")


if __name__ == "__main__":
    asyncio.run(main())
