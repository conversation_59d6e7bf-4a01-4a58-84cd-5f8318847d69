#!/usr/bin/env python3
"""
测试事件处理器集成
"""

import asyncio
import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_event_integration():
    """测试事件处理器集成"""
    print("🚀 测试事件处理器集成...")
    
    try:
        from api.main import SpecificAPI
        from api.schemas import ChatRequest, ServiceToken, Extensions
        
        # 初始化API
        api = SpecificAPI()
        
        # 创建服务token
        service_token = ServiceToken(
            service_id="test_service_001",
            access_token="test_access_token_12345"
        )
        
        # 创建扩展字段
        extensions = Extensions(
            tokens=[service_token],
            additional_fields={
                "custom_field_1": "custom_value_1"
            }
        )
        
        # 创建包含事件回调的请求
        request = ChatRequest(
            message="你好，我想分析一下京东的市场情况",
            session_id="test_event_001",
            task_id="task_12345",
            sandbox_id="sandbox_67890",
            event_webhook="https://xuanji-dev.chehejia.com/webhook",
            extensions=extensions,
            user_id="test_user"
        )
        
        print("📤 发送包含事件回调的请求:")
        print(f"  Message: {request.message}")
        print(f"  Session ID: {request.session_id}")
        print(f"  Task ID: {request.task_id}")
        print(f"  Sandbox ID: {request.sandbox_id}")
        print(f"  Event Webhook: {request.event_webhook}")
        
        # 调用API，这应该会触发事件发送
        response = await api.chat(request)
        
        print(f"\n📥 收到响应:")
        print(f"  Session ID: {response.session_id}")
        print(f"  Status: {response.status}")
        print(f"  Response: {response.response}")
        print(f"  Event Webhook: {response.event_webhook}")
        
        # 测试后续对话，应该也会触发事件
        print("\n🔄 测试后续对话...")
        follow_up_request = ChatRequest(
            message="请详细分析一下京东在电商领域的竞争优势",
            session_id="test_event_001",
            task_id="task_12345",
            sandbox_id="sandbox_67890",
            event_webhook="https://xuanji-dev.chehejia.com/webhook",
            user_id="test_user"
        )
        
        follow_up_response = await api.chat(follow_up_request)
        
        print(f"📥 后续响应:")
        print(f"  Status: {follow_up_response.status}")
        print(f"  Response: {follow_up_response.response[:100]}...")
        
        print("\n✅ 事件处理器集成测试完成!")
        print("💡 注意：事件已发送到配置的webhook地址，请检查第三方平台是否收到事件")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

async def test_event_handler_directly():
    """直接测试事件处理器"""
    print("\n🔧 直接测试事件处理器...")
    
    try:
        from event.manus_event_handler import event_handler
        from src.models.state import SpecificState, WorkflowStatus
        
        # 创建测试状态
        test_state = {
            "session_id": "test_direct_001",
            "task_id": "task_direct_001",
            "sandbox_id": "sandbox_direct_001",
            "event_webhook": "https://xuanji-dev.chehejia.com/webhook",
            "extensions": {"test": "value"},
            "workflow_status": WorkflowStatus.INITIALIZING,
            "messages": []
        }
        
        print("📤 发送测试事件:")
        
        # 测试发送实时状态
        event_handler.send_live_status(
            text="测试实时状态更新",
            state=test_state
        )
        print("  ✅ 实时状态事件已发送")
        
        # 测试发送助手消息
        event_handler.send_agent_message(
            state=test_state,
            content="这是一条测试助手消息"
        )
        print("  ✅ 助手消息事件已发送")
        
        # 测试发送代理状态
        event_handler.send_agent_status(
            status="RUNNING",
            brief="代理正在运行",
            description="测试代理状态更新",
            state=test_state
        )
        print("  ✅ 代理状态事件已发送")
        
        print("✅ 事件处理器直接测试完成!")
        
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_event_integration())
    asyncio.run(test_event_handler_directly())
