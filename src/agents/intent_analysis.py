"""
意图分析Agent - 专门负责用户意图分析和澄清
"""

from typing import List, Optional, Literal, Dict, Any
from langchain_core.messages import BaseMessage, HumanMessage
from langchain_core.language_models import BaseChatModel
from langgraph.types import Command
from langgraph.config import get_stream_writer
from langgraph.types import StreamWriter
import logging

from .base import BaseAgent
from src.models.state import SpecificState, UserIntentAnalysis, IntentClarificationResult, WorkflowStatus
from langchain_core.messages import SystemMessage


class IntentAnalysisAgent(BaseAgent):
    """
    意图分析Agent
    
    职责：
    1. 分析用户意图（同意/补充/拒绝）
    2. 判断用户需求是否清晰
    3. 生成澄清问题
    """

    def __init__(self, llm: BaseChatModel):
        """
        初始化意图分析Agent

        Args:
            llm: 语言模型实例
        """
        system_prompt = """
        你是专业的用户意图分析专家，负责分析品牌舆情事件相关的用户需求。

        你的核心能力：
        1. 准确识别用户的真实意图（同意、补充信息、拒绝）
        2. 判断用户需求的完整性和清晰度
        3. 生成有针对性的澄清问题

        工作原则：
        - 专注于品牌舆情分析领域
        - 确保必填信息完整（品牌名称、传播事件名称）
        - 提供友好、专业的交互体验
        """

        super().__init__(
            llm=llm,
            role_name="IntentAnalysisAgent",
            system_prompt=system_prompt
        )

    def build_intent_analysis_prompt(
            self,
            current_stage: str,
            latest_user_response: str
    ) -> SystemMessage:
        """
        构建用户意图分析提示词

        Args:
            current_stage: 当前阶段
            latest_user_response: 最新用户回复

        Returns:
            系统消息
        """
        prompt = f"""
        你正在分析用户在{current_stage}的意图。

        用户最新回复："{latest_user_response}"

        请分析用户的真实意图，判断用户是：
        1. 同意/确认当前阶段的结果（agreement）
        2. 提供补充信息（supplement）
        3. 拒绝/要求重新开始（rejection）

        分析要点：
        - 同意关键词：同意、确认、好的、可以、继续、开始执行、yes、ok、没问题
        - 拒绝关键词：不同意、不行、重新、no、不对
        - 补充信息：提供新的具体信息、澄清细节、回答问题

        返回JSON格式分析结果：
        {{
            "intent_type": "agreement|supplement|rejection",
            "next_action": "continue|retry",
            "extracted_info": "提取的补充信息（仅supplement类型需要）"
        }}
        """

        return SystemMessage(content=prompt)

    def build_clarification_prompt(self, user_input: str) -> SystemMessage:
        """
        构建意图澄清提示词

        Args:
            user_input: 用户输入

        Returns:
            系统消息
        """
        prompt = f"""
                你是专业的舆情分析助手，需要判断用户的舆情分析需求是否足够清晰，并生成相应的回复消息。

                ## 用户输入
                "{user_input}"

                ## 分析任务
                判断用户需求是否包含足够的信息来进行舆情分析。

                ## 必需信息检查
                **核心要求：明确的分析事件**
                - 必须包含具体的品牌、产品、人物或话题名称
                - 需要明确或能推断出分析维度（如声量、情感、传播、口碑等）
                - 事件 = 具体对象 + 分析维度

                **清晰事件示例：**
                ✓ 星环OS传播情况、理想汽车舆情分析、新车i8声量分析
                ✓ 理想L9口碑趋势、想哥舆论评价、佳伟饿了舆情分析
                ✓ 理想MAGA vs 腾势D9消费者偏好变化、三大品牌负面观点及传播
                ✓ 理想汽车购车体验负面反馈、星环OS技术讨论热度及意见领袖

                **模糊描述示例：**
                ✗ 舆情分析、网络声量、品牌传播、市场反响
                ✗ 某个产品、这个品牌、我们公a司、竞品情况

                ## 可选信息（有默认值）
                - **时间范围**：如未指定或者指定了模糊时间，默认为"近一个月"
                  - 明确时间：近一周、近两月、近两月、本周vs上周、近7天
                  - 模糊时间：近期、最近、当前

                - **平台范围**：如未指定，默认为"全平台"
                  - 具体平台：抖音、微博、小红书、汽车之家论坛
                  - 平台组合：头部三平台（如抖音/微博/小红书）、主流社交平台
                  - 特定渠道：用户社群、汽车垂直论坛、媒体
                  - 全覆盖：全平台、全网

                ## 判断逻辑
                **intent_clear = true**：包含明确的分析对象（品牌/产品/人物）且能推断分析维度
                **intent_clear = false**：缺少具体分析对象，只有模糊描述

                ## 回复消息生成

                ### 如果 intent_clear = true（需求清晰）
                生成确认消息，使用以下格式：
                ```
                分析到您的真实需求为：
                - 查询事件：[完整事件描述，包含对象+分析维度]
                - 时间范围：[提取的时间或默认"近一个月"]
                - 平台范围：[提取的平台或默认"全平台"]

                请确认这个理解是否正确，我将据此为您制定详细的分析计划。
                ```

                ### 如果 intent_clear = false（需求不清晰）
                生成针对性的澄清问题：
                ```
                为了为您提供精准的分析服务，我需要了解几个关键信息：
                [根据缺失信息生成1-2个具体问题]

                请您详细回复这些问题，这将帮助我为您制定最佳的分析方案。
                ```

                ## 澄清问题模板
                - 缺少分析对象：请告诉我您想分析哪个具体的品牌、产品、人物或事件？
                - 需要明确分析维度：请说明您希望重点关注哪些方面的分析（如情感倾向、传播声量、用户画像等）？

                ## 输出格式
                **重要：必须严格按照以下JSON格式返回结果**

                返回JSON格式分析结果：
                {{
                    "intent_clear": boolean,
                    "clarification_questions": ["具体问题1", "具体问题2"],
                    "clarification_result": {{
                        "event": "事件描述",
                        "platform": "平台范围",
                        "time_range": "时间范围"
                    }},
                    "summary": "对用户需求的当前理解总结",
                    "response_message": "直接发送给用户的完整回复消息"
                }}

                **字段说明：**
                - intent_clear: 布尔值，true表示需求清晰，false表示需要澄清
                - clarification_questions: 字符串数组，仅在intent_clear为false时填入具体问题
                - clarification_result: 对象，结构化的澄清结果数据（仅在intent_clear为true时填入）
                  - event: 字符串，完整事件描述，如"理想汽车L9汽车的网络声量"、"星环OS传播情况"
                  - platform: 字符串，平台范围，如"全平台"、"头部三平台（如抖音/微博/小红书）"
                  - time_range: 字符串，时间范围，如"近一个月"、"近期"
                - summary: 字符串，对当前用户需求的理解总结
                - response_message: 字符串，直接发送给用户的完整消息内容

                ## 参考示例

                **示例1：近期关于理想汽车L9汽车的网络声量如何**
                {{
                    "intent_clear": true,
                    "clarification_questions": [],
                    "clarification_result": {{
                        "event": "理想汽车L9汽车的网络声量",
                        "platform": "全平台",
                        "time_range": "近期"
                    }},
                    "summary": "用户想要了解理想汽车L9汽车近期的网络声量情况",
                    "response_message": "分析到您的真实意图为：\\n- 查询事件：理想汽车L9汽车的网络声量\\n- 时间范围：近一个月\\n- 平台范围：全平台\\n\\n请确认这个理解是否正确，我将据此为您制定详细的分析计划。"
                }}

                **示例2：最近两个月内，网络上关于想哥的舆论评价怎么样，按周粒度给出**
                {{
                    "intent_clear": true,
                    "clarification_questions": [],
                    "clarification_result": {{
                        "event": "想哥舆论评价",
                        "platform": "全平台",
                        "time_range": "最近两个月"
                    }},
                    "summary": "用户想要了解想哥在最近两个月内的舆论评价，需要按周粒度展示",
                    "response_message": "分析到您的真实意图为：\\n- 查询事件：想哥舆论评价\\n- 时间范围：最近两个月\\n- 平台范围：全平台\\n\\n请确认这个理解是否正确，我将据此为您制定详细的分析计划。"
                }}

                **示例3：对比理想MAGA和腾势D9，近两月消费者偏好变化是什么趋势？**
                {{
                    "intent_clear": true,
                    "clarification_questions": [],
                    "clarification_result": {{
                        "event": "理想MAGA vs 腾势D9消费者偏好变化",
                        "platform": "全平台",
                        "time_range": "近两月"
                    }},
                    "summary": "用户想要对比分析理想MAGA和腾势D9在近两月的消费者偏好变化趋势",
                    "response_message": "分析到您的真实意图为：\\n- 查询事件：理想MAGA vs 腾势D9消费者偏好变化\\n- 时间范围：近两月\\n- 平台范围：全平台\\n\\n请确认这个理解是否正确，我将据此为您制定详细的分析计划。"
                }}

                **示例4：帮我分析一下舆情**
                {{
                    "intent_clear": false,
                    "clarification_questions": ["请告诉我您想分析哪个具体的品牌、产品、人物或事件？"],
                    "clarification_result": null,
                    "summary": "用户想要进行舆情分析，但缺少具体的分析对象",
                    "response_message": "为了为您提供精准的分析服务，我需要了解几个关键信息：\\n1. 请告诉我您想分析哪个具体的品牌、产品、人物或事件？\\n\\n请您详细回复这些问题，这将帮助我为您制定最佳的分析方案。"
                }}

                请严格按照以上逻辑和格式进行分析和回复。
                """

        return SystemMessage(content=prompt)

    def execute(self, state: SpecificState, writer: StreamWriter) -> Command[
        Literal["intent_clarification", "planning", "__end__"]]:
        """
        意图澄清Agent的主要调用方法 - 自主路由模式
        处理业务逻辑并决定下一步路由

        Args:
            state: 当前状态

        Returns:
            路由命令
        """
        session_id = state.get('session_id', 'unknown')
        clarification_round = state.get("clarification_round", 0)
        self.logger.info(f"Start doing intent clarification (round {clarification_round + 1}), session_id:{session_id}")

        try:
            # 发送任务开始的Agent消息
            if clarification_round == 0:
                writer({"agent_message": "正在分析您的需求，确保完全理解您的需求。"})

            # 发送实时状态消息
            writer({"live_status_message": "正在分析用户意图..."})
            # 获取基本信息
            latest_user_response = self._get_latest_user_response(state)
            context_messages = state.get("messages", [])

            # 如果有用户回复，先分析用户意图
            if clarification_round > 0 and latest_user_response and not state.get("intent_approved", False):

                # 发送分析用户回复的状态消息
                writer({"live_status_message": "正在分析您的回复..."})

                intent_analysis = self.analyze_user_intent(
                    user_response=latest_user_response,
                    current_stage="意图澄清阶段",
                    context_messages=context_messages
                )

                # 根据意图分析结果处理
                if intent_analysis.intent_type == "agreement":
                    # 发送用户确认的消息
                    writer({"agent_message": "意图已确认，将为您制定详细的执行计划"})
                    self.logger.info(
                        f"Completed intent clarification, result: user approved requirements, session_id:{session_id}")
                    return Command(
                        goto="planning",
                        update={
                            "intent_clarified": True,
                            "intent_approved": True,  # 用户确认审批通过
                            "workflow_status": WorkflowStatus.PLANNING,  # 审批通过，进入计划阶段
                            "messages": [HumanMessage(content="感谢您的确认！我现在开始为您制定执行计划。")]
                        }
                    )

                elif intent_analysis.intent_type == "supplement":
                    # 发送补充信息处理的消息
                    writer({"agent_message": "感谢您提供的补充信息！将重新分析您的完整需求。"})

                    self.logger.info(f"User provided supplementary info, re-analyzing intent, session_id:{session_id}")
                    # 更新用户输入，包含补充信息
                    original_request = state.get("user_input", "")
                    updated_request = f"{original_request}\n补充信息：{intent_analysis.extracted_info or latest_user_response}"

                    # 发送重新分析的状态消息
                    writer({"live_status_message": "正在重新分析完整需求..."})

                    # 重新进行意图澄清分析
                    clarification_result = self.is_intent_clear(
                        user_input=updated_request,
                        context_messages=context_messages
                    )
                    # 发送分析完成的消息
                    writer({"agent_message": clarification_result.response_message})

                    # 使用公共方法处理澄清结果
                    return self._process_clarification_result_with_command(
                        writer=writer,
                        clarification_result=clarification_result,
                        clarification_round=clarification_round,
                        session_id=session_id,
                        updated_user_input=updated_request,
                    )

            # 发送首次分析的状态消息

            # 进行意图澄清分析
            clarification_result = self.is_intent_clear(
                user_input=state.get("user_input", ""),
                context_messages=context_messages
            )

            # 发送分析完成的消息
            writer({"agent_message": clarification_result.response_message})

            # 使用公共方法处理澄清结果
            return self._process_clarification_result_with_command(
                writer=writer,
                clarification_result=clarification_result,
                clarification_round=clarification_round,
                session_id=session_id
            )

        except Exception as e:
            self.logger.error(f"Failed intent clarification, error: {str(e)}, session_id:{session_id}")
            return Command(
                goto="__end__",
                update={
                    "error_info": {"message": str(e), "node": "intent_clarification"},
                    "workflow_status": WorkflowStatus.FAILED
                }
            )

    def _get_latest_user_response(self, state: SpecificState) -> Optional[str]:
        """获取最新的用户回复"""
        messages = state.get("messages", [])
        for msg in reversed(messages):
            if isinstance(msg, HumanMessage):
                return msg.content
        return None

    def _process_clarification_result(
            self,
            clarification_result: IntentClarificationResult,
            clarification_round: int,
            session_id: str,
            updated_user_input: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        处理意图澄清结果的公共方法

        Args:
            clarification_result: 澄清结果
            clarification_round: 澄清轮次
            session_id: 会话ID
            updated_user_input: 更新后的用户输入（用于补充信息场景）

        Returns:
            状态更新字典
        """
        result = {}

        # 如果有更新的用户输入，添加到结果中
        if updated_user_input:
            result["user_input"] = updated_user_input

        if clarification_result.intent_clear:
            # 意图澄清完成，但需要人工审批确认
            self.logger.info(
                f"Completed intent clarification, result: intent is clear, waiting for approval, session_id:{session_id}")

            result.update({
                "intent_clarified": True,
                "intent_summary": clarification_result.summary,
                "workflow_status": WorkflowStatus.CLARIFYING_INTENT,  # 保持在澄清阶段等待审批
                "messages": [HumanMessage(content=clarification_result.response_message)]
            })
        else:
            # 需要进一步澄清，设置人工确认标志
            self.logger.info(
                f"Completed intent clarification, result: need further clarification, session_id:{session_id}")

            result.update({
                "workflow_status": WorkflowStatus.CLARIFYING_INTENT,  # 保持在澄清阶段
                "messages": [HumanMessage(content=clarification_result.response_message)],
                "clarification_round": clarification_round + 1
            })

        return result

    def _process_clarification_result_with_command(
            self,
            writer: StreamWriter,
            clarification_result: IntentClarificationResult,
            clarification_round: int,
            session_id: str,
            updated_user_input: Optional[str] = None
    ) -> Command[Literal["intent_clarification", "__end__"]]:
        """
        处理意图澄清结果并返回Command

        Args:
            clarification_result: 澄清结果
            clarification_round: 澄清轮次
            session_id: 会话ID
            updated_user_input: 更新后的用户输入（用于补充信息场景）

        Returns:
            路由命令
        """
        update_data = {}

        # 如果有更新的用户输入，添加到结果中
        if updated_user_input:
            update_data["user_input"] = updated_user_input

        if clarification_result.intent_clear:
            # 意图澄清完成，但需要人工审批确认
            self.logger.info(
                f"Completed intent clarification, result: intent is clear, waiting for approval, session_id:{session_id}")

            # 保存结构化的澄清结果到state
            clarification_data = None
            if clarification_result.clarification_result:
                clarification_data = clarification_result.clarification_result.model_dump()

            update_data.update({
                "intent_clarified": True,
                "intent_summary": clarification_result.summary,
                "clarification_result": clarification_data,  # 保存结构化澄清结果
                "workflow_status": WorkflowStatus.CLARIFYING_INTENT,  # 保持在澄清阶段等待审批
                "messages": [HumanMessage(content=clarification_result.response_message)],
                "clarification_round": clarification_round + 1
            })

            # 发送人工审批消息
            writer({"human_feedback_message": "请输入'同意'或提供反馈信息以重新分析需求."})

            # 需要人工审批，路由到__end__等待用户输入
            return Command(goto="__end__", update=update_data)
        else:
            # 需要进一步澄清，设置人工确认标志
            self.logger.info(
                f"Completed intent clarification, result: need further clarification, session_id:{session_id}")

            update_data.update({
                "workflow_status": WorkflowStatus.CLARIFYING_INTENT,  # 保持在澄清阶段
                "messages": [HumanMessage(content=clarification_result.response_message)],
                "clarification_round": clarification_round + 1
            })

            # 发送人工审批消息
            writer({"human_feedback_message": "请提供反馈信息以重新分析需求."})

            # 需要进一步澄清，路由到__end__等待用户输入
            return Command(goto="__end__", update=update_data)

    def analyze_user_intent(
            self,
            user_response: str,
            current_stage: str,
            context_messages: Optional[List[BaseMessage]] = None
    ) -> UserIntentAnalysis:
        """
        分析用户意图
        
        Args:
            user_response: 用户回复
            current_stage: 当前阶段（意图澄清阶段/计划确认阶段）
            context_messages: 上下文消息
            
        Returns:
            用户意图分析结果
        """
        try:
            self.logger.info(f"Analyzing user intent in {current_stage}")

            # 构建意图分析提示词
            intent_prompt = self.build_intent_analysis_prompt(
                current_stage=current_stage,
                latest_user_response=user_response
            )

            # 使用结构化输出分析意图
            result = self.invoke_with_structured_output(
                output_schema=UserIntentAnalysis,
                user_input="",  # 用户输入已经在prompt中
                context_messages=[intent_prompt] + (context_messages or [])
            )

            self.logger.info(f"User intent analyzed: {result.intent_type}")

            return result

        except Exception as e:
            self.logger.error(f"Error analyzing user intent: {e}")
            # 返回降级结果
            return self._fallback_intent_analysis(user_response)

    def is_intent_clear(
            self,
            user_input: str,
            context_messages: Optional[List[BaseMessage]] = None
    ) -> IntentClarificationResult:
        """
        判断用户意图是否清晰

        Args:
            user_input: 用户输入
            context_messages: 上下文消息

        Returns:
            意图澄清结果
        """
        try:
            self.logger.info("Checking if user intent is clear")

            # 构建澄清分析提示词
            clarification_prompt = self.build_clarification_prompt(user_input=user_input)

            # 使用结构化输出分析澄清需求
            result = self.invoke_with_structured_output(
                output_schema=IntentClarificationResult,
                user_input="",  # 用户输入已经在prompt中
                context_messages=[clarification_prompt] + (context_messages or [])
            )

            self.logger.info(f"Intent clarity checked: {result.intent_clear}")

            return result

        except Exception as e:
            self.logger.error(f"Error checking intent clarity: {e}")
            # 返回降级结果
            return IntentClarificationResult(
                intent_clear=False,
                clarification_questions=["请提供更详细的需求描述"],
                summary="系统暂时无法分析您的需求，请提供更多信息",
                response_message="为了为您提供精准的分析服务，请告诉我您想分析哪个品牌的什么事件或产品？"
            )

    def _fallback_intent_analysis(self, user_response: str) -> UserIntentAnalysis:
        """
        降级的意图分析 - 基于关键词判断
        
        Args:
            user_response: 用户回复
            
        Returns:
            意图分析结果
        """
        response_lower = user_response.lower().strip()

        # 同意关键词
        agreement_keywords = ["同意", "确认", "好的", "可以", "继续", "开始执行", "yes", "ok", "没问题"]
        # 拒绝关键词
        rejection_keywords = ["不同意", "不行", "重新", "no", "不对"]

        if any(keyword in response_lower for keyword in agreement_keywords):
            return UserIntentAnalysis(
                intent_type="agreement",
                next_action="continue"
            )
        elif any(keyword in response_lower for keyword in rejection_keywords):
            return UserIntentAnalysis(
                intent_type="rejection",
                next_action="retry"
            )
        else:
            # 其他情况视为补充信息
            return UserIntentAnalysis(
                intent_type="supplement",
                next_action="retry",
                extracted_info=user_response
            )

    def get_capabilities(self) -> List[str]:
        """获取Agent能力列表"""
        return [
            "用户意图分析（同意/补充/拒绝）",
            "需求清晰度判断",
            "澄清问题生成",
            "品牌舆情领域专业分析",
            "多轮对话意图理解"
        ]

    def validate_input(self, user_input: str) -> bool:
        """验证输入"""
        if not super().validate_input(user_input):
            return False

        # 检查输入长度
        if len(user_input.strip()) < 2:
            return False

        return True
