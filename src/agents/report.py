"""
Report Agent - Specialized in generating final reports
"""

from typing import List, Optional, Dict, Any, Literal

from agentops_event_sdk_python import ChatAttachment
from langchain_core.messages import BaseMessage, HumanMessage
from langchain_core.language_models import BaseChatModel
from langgraph.types import Command
from langgraph.types import StreamWriter
import logging
import httpx
import json
import tempfile
import os
from datetime import datetime

from .base import BaseAgent
from src.models.state import SpecificState, WorkflowStatus
from langchain_core.messages import SystemMessage


class ReportAgent(BaseAgent):
    """
    Report Agent

    Responsibilities:
    1. Generate final analysis reports based on execution results
    2. Integrate all execution results
    3. Provide professional summaries and recommendations
    """

    def __init__(self, llm: BaseChatModel, base_url: str = "https://console-playground.fed.chehejia.com",
                 upload_url: str = "https://xuanji-backend-service-dev.inner.chj.cloud/api/v1/ois/upload",
                 timeout: int = 30):
        """
        Initialize Report Agent

        Args:
            llm: Language model instance
            base_url: Report service base URL
            upload_url: File upload service URL
            timeout: Request timeout in seconds
        """
        system_prompt = """
        You are a professional brand sentiment analysis report expert, specialized in generating final analysis reports.

        Your core capabilities:
        1. Integrate all data and results from the execution process
        2. Call external report services to generate professional reports
        3. Provide valuable insights and recommendations

        Report principles:
        - Clear structure, rigorous logic
        - Accurate data, deep analysis
        - Clear conclusions, actionable recommendations
        - Accurate terminology, concise expression

        Professional domains: Brand sentiment analysis, data analysis, business insights, strategic recommendations
        """

        super().__init__(
            llm=llm,
            role_name="ReportAgent",
            system_prompt=system_prompt
        )

        # Report service configuration
        self.base_url = base_url
        self.upload_url = upload_url
        self.timeout = timeout

    def parse_dsl_summary(self, report_dsl: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse AI summary and key data from DSL

        Args:
            report_dsl: Report DSL data structure

        Returns:
            Parsed summary data
        """
        try:
            summary_data = {
                "ai_summary": "",
                "key_metrics": [],
                "viewpoints": [],
                "charts_info": []
            }

            for section_key, section in report_dsl.items():
                if not isinstance(section, dict):
                    continue

                section_title = section.get("title", "")
                section_description = section.get("description", "")
                section_content = section.get("content", [])

                # Extract AI summary
                if "AI总结" in section_title or "总结" in section_title:
                    summary_data["ai_summary"] = section_description
                    self.logger.info(f"Found AI summary in section: {section_title}")

                # Extract key metrics
                for content_item in section_content:
                    if content_item.get("type") == "descriptions":
                        data_items = content_item.get("data", [])
                        for item in data_items:
                            if isinstance(item, dict) and "label" in item and "value" in item:
                                summary_data["key_metrics"].append({
                                    "label": item["label"],
                                    "value": item["value"]
                                })

                    # Extract viewpoint information
                    elif content_item.get("type") == "table":
                        table_data = content_item.get("data", [])
                        for row in table_data:
                            if isinstance(row, dict) and "viewpoint" in row:
                                summary_data["viewpoints"].append({
                                    "viewpoint": row.get("viewpoint", ""),
                                    "explain": row.get("explain", ""),
                                    "positive": row.get("positive", ""),
                                    "negative": row.get("negative", "")
                                })

                    # Extract chart information
                    elif content_item.get("type") in ["pie", "bar", "bar:negative", "table"]:
                        chart_info = {
                            "type": content_item.get("type"),
                            "title": content_item.get("title") or content_item.get("option", {}).get("title", {}).get(
                                "text", section_title)
                        }
                        summary_data["charts_info"].append(chart_info)

            self.logger.info(f"Parsed DSL summary: AI summary length={len(summary_data['ai_summary'])}, "
                             f"metrics={len(summary_data['key_metrics'])}, "
                             f"viewpoints={len(summary_data['viewpoints'])}")

            return summary_data

        except Exception as e:
            self.logger.error(f"Error parsing DSL summary: {e}", exc_info=True)
            return {
                "ai_summary": "",
                "key_metrics": [],
                "viewpoints": [],
                "charts_info": []
            }

    def generate_text_summary(self, summary_data: Dict[str, Any]) -> str:
        """
        基于解析的DSL数据生成文本总结

        Args:
            summary_data: 解析后的总结数据

        Returns:
            格式化的文本总结
        """
        try:
            text_summary = "# 品牌舆情分析报告\n\n"

            # AI总结部分
            if summary_data["ai_summary"]:
                text_summary += "## AI智能总结\n\n"
                # 处理换行符，使格式更清晰
                ai_summary = summary_data["ai_summary"].replace("\\n", "\n")
                text_summary += f"{ai_summary}\n\n"

            # 关键指标部分
            if summary_data["key_metrics"]:
                text_summary += "## 关键指标\n\n"
                for metric in summary_data["key_metrics"]:
                    text_summary += f"- **{metric['label']}**: {metric['value']}\n"
                text_summary += "\n"

            # 观点分析部分
            if summary_data["viewpoints"]:
                text_summary += "## 观点分析\n\n"
                for i, viewpoint in enumerate(summary_data["viewpoints"], 1):
                    text_summary += f"### {i}. {viewpoint['viewpoint']}\n\n"
                    if viewpoint['explain']:
                        text_summary += f"**解释说明**: {viewpoint['explain']}\n\n"
                    if viewpoint['positive']:
                        text_summary += f"**正向观点**:\n{viewpoint['positive']}\n\n"
                    if viewpoint['negative']:
                        text_summary += f"**负向观点**:\n{viewpoint['negative']}\n\n"

            # 图表信息部分
            if summary_data["charts_info"]:
                text_summary += "## 可视化图表\n\n"
                for chart in summary_data["charts_info"]:
                    text_summary += f"- **{chart['title']}** ({chart['type']})\n"
                text_summary += "\n"

            text_summary += "---\n\n*本报告基于DSL数据结构自动生成，包含完整的HTML可视化图表。*"

            return text_summary

        except Exception as e:
            self.logger.error(f"Error generating text summary: {e}")
            return "# 品牌舆情分析报告\n\n报告生成过程中出现错误，请查看HTML版本获取完整内容。"

    async def generate_report(
            self,
            report_dsl: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        调用外部报告服务生成报告

        Args:
            report_dsl: 报表DSL数据结构
            access_token: 访问令牌

        Returns:
            报告生成结果
        """
        try:
            self.logger.info("Starting report generation")

            # 构建请求URL
            url = f"{self.base_url}/__ui/report"

            # 构建请求头
            headers = {
                "Content-Type": "application/json",
                "Authorization": "Bearer api-key:eyJhbGciOiJIUzI1NiJ9.eyJzIjoidWkiLCJpYXQiOjE3NDk2MzI5NDgsImlzcyI6IjdONVg5MHNkOUtuZGlDS1Q1VjlKWGwifQ.yKQahvhNPXjGCfmFHEJVl9iMWqGodm_QJ8GHjKxUuHI",
            }

            # 发送请求
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.post(
                    url=url,
                    headers=headers,
                    json=report_dsl
                )

                # 检查响应状态
                if response.status_code == 200:
                    # 接口返回HTML格式的报表
                    html_content = response.text
                    self.logger.info("Report generated successfully")
                    return {
                        "success": True,
                        "html_content": html_content,
                        "content_type": "text/html",
                        "message": "报告生成成功"
                    }
                else:
                    self.logger.error(f"Report generation failed: {response.status_code} - {response.text}")
                    return {
                        "success": False,
                        "error": f"HTTP {response.status_code}: {response.text}",
                        "message": "报告生成失败"
                    }

        except httpx.TimeoutException:
            self.logger.error("Report generation timeout")
            return {
                "success": False,
                "error": "Request timeout",
                "message": "报告生成超时"
            }
        except Exception as e:
            self.logger.error(f"Report generation error: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"报告生成失败：{str(e)}"
            }

    async def upload_html_to_s3(self, html_content: str, filename: Optional[str] = None) -> Dict[str, Any]:
        """
        将HTML内容上传到S3存储

        Args:
            html_content: HTML内容
            filename: 文件名（可选，默认自动生成）

        Returns:
            上传结果
        """
        try:
            self.logger.info("Starting HTML upload to S3")

            # 生成文件名
            if not filename:
                timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
                filename = f"report-{timestamp}.html"

            # 创建临时文件
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as temp_file:
                temp_file.write(html_content)
                temp_file_path = temp_file.name

            try:
                # 准备上传文件
                with open(temp_file_path, 'rb') as file:
                    files = {
                        'file': (filename, file, 'text/html')
                    }

                    # 发送上传请求
                    async with httpx.AsyncClient(timeout=self.timeout) as client:
                        response = await client.post(
                            url=self.upload_url,
                            files=files
                        )

                        # 检查响应状态
                        if response.status_code == 200:
                            result = response.json()
                            self.logger.info(f"HTML uploaded successfully: {result}")
                            return {
                                "success": True,
                                "upload_result": result,
                                "filename": filename,
                                "message": "HTML报告上传成功"
                            }
                        else:
                            self.logger.error(f"HTML upload failed: {response.status_code} - {response.text}")
                            return {
                                "success": False,
                                "error": f"HTTP {response.status_code}: {response.text}",
                                "message": "HTML报告上传失败"
                            }

            finally:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)

        except httpx.TimeoutException:
            self.logger.error("HTML upload timeout")
            return {
                "success": False,
                "error": "Request timeout",
                "message": "HTML报告上传超时"
            }
        except Exception as e:
            self.logger.error(f"HTML upload error: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"HTML报告上传失败：{str(e)}"
            }

    def create_brand_analysis_dsl(
            self,
            brand_name: str = "京东",
            event_id: str = "100395410300708206",
            analysis_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        创建品牌分析报告DSL

        Args:
            brand_name: 品牌名称
            event_id: 事件ID
            analysis_data: 分析数据

        Returns:
            品牌分析报告DSL
        """
        if not analysis_data:
            analysis_data = {
                "sentiment_score": 75,
                "mention_count": 1250,
                "positive_ratio": 0.65,
                "negative_ratio": 0.20,
                "neutral_ratio": 0.15
            }

        return {
            "01": {
                "type": "section",
                "title": "品牌舆情分析概览",
                "description": f"{brand_name}品牌舆情监测分析报告",
                "content": [
                    {
                        "type": "descriptions",
                        "data": [
                            {"id": 1, "label": "品牌名称", "value": brand_name},
                            {"id": 2, "label": "分析事件ID", "value": event_id},
                            {"id": 3, "label": "舆情评分", "value": f"{analysis_data.get('sentiment_score', 0)}分"},
                            {"id": 4, "label": "提及次数", "value": f"{analysis_data.get('mention_count', 0)}次"}
                        ]
                    }
                ]
            },
            "02": {
                "type": "section",
                "title": "情感分析分布",
                "description": "品牌相关内容的情感倾向分析",
                "content": [
                    {
                        "type": "chart",
                        "chart_type": "pie",
                        "data": [
                            {"label": "正面", "value": analysis_data.get('positive_ratio', 0) * 100,
                             "color": "#52c41a"},
                            {"label": "负面", "value": analysis_data.get('negative_ratio', 0) * 100,
                             "color": "#ff4d4f"},
                            {"label": "中性", "value": analysis_data.get('neutral_ratio', 0) * 100, "color": "#faad14"}
                        ]
                    }
                ]
            },
            "03": {
                "type": "section",
                "title": "关键指标",
                "description": "品牌舆情关键性能指标",
                "content": [
                    {
                        "type": "metrics",
                        "data": [
                            {"label": "舆情健康度", "value": f"{analysis_data.get('sentiment_score', 0)}%",
                             "trend": "up"},
                            {"label": "传播声量", "value": f"{analysis_data.get('mention_count', 0)}", "trend": "up"},
                            {"label": "正面占比", "value": f"{analysis_data.get('positive_ratio', 0) * 100:.1f}%",
                             "trend": "stable"},
                            {"label": "负面占比", "value": f"{analysis_data.get('negative_ratio', 0) * 100:.1f}%",
                             "trend": "down"}
                        ]
                    }
                ]
            }
        }

    async def execute(self, state: SpecificState, writer: StreamWriter) -> Command[Literal["__end__"]]:
        """
        生成最终报告 - 直接调用外部报告服务

        Args:
            state: 当前状态
            writer: 流写入器

        Returns:
            命令对象
        """
        session_id = state.get('session_id', 'unknown')

        try:
            self.logger.info("Starting final report generation", session_id)

            # Send start message
            writer({"agent_message": "正在生成品牌舆情分析报告..."})

            # Get report DSL data
            report_dsl_data = state.get("report_dsl_data")
            report_dsl_status = state.get("report_dsl_status")
            report_dsl_message = state.get("report_dsl_message")

            # Check DSL status
            if report_dsl_status == "FAILED":
                error_message = f"报表DSL生成失败：{report_dsl_message or '未知错误'}"
                self.logger.error(f"Report DSL generation failed: {report_dsl_message}", session_id)

                # Send error message
                writer({"agent_message": error_message})

                return Command(
                    goto="__end__",
                    update={
                        "workflow_status": WorkflowStatus.FAILED,
                        "error_info": {"message": error_message, "node": "report"}
                    }
                )

            # Use default data if no DSL data provided
            if not report_dsl_data:
                self.logger.info("No report DSL data provided, using default brand analysis DSL", session_id)
                report_dsl_data = self.create_brand_analysis_dsl()

            # Call external report service to generate report
            writer({"live_status_message": "正在调用外部报告服务..."})
            result = await self.generate_report(report_dsl_data)

            if result["success"]:
                # Report generation successful, get HTML content
                html_content = result["html_content"]

                # Upload HTML to S3
                writer({"live_status_message": "正在上传HTML报告到云存储..."})
                upload_result = await self.upload_html_to_s3(
                    html_content,
                    filename=f"report-{session_id}-{datetime.now().strftime('%Y%m%d-%H%M%S')}.html"
                )

                # Parse AI summary and key data from DSL
                writer({"live_status_message": "正在解析报告中的AI总结和关键数据..."})
                summary_data = self.parse_dsl_summary(report_dsl_data)

                # Generate structured text summary based on DSL data
                final_report = self.generate_text_summary(summary_data)

                # 准备完成消息
                ai_summary_preview = summary_data["ai_summary"][:100] + "..." if len(
                    summary_data["ai_summary"]) > 100 else summary_data["ai_summary"]

                if upload_result["success"]:
                    upload_info = upload_result.get("upload_result", {})
                    file_name = upload_result.get("filename", "")
                    file_key = upload_info.get("fileKey", "")
                    completion_message = f"""品牌舆情分析报告已生成完成！

 AI总结预览: {ai_summary_preview}



下方完整报告文件包含可视化图表和详细分析，请查收。"""
                    writer({"agent_message": completion_message})
                    writer(
                        {"agent_message_with_file":
                            {
                                "content": "报告文件",
                                "attachments": [ChatAttachment(
                                    file_key=file_key,
                                    local_path=file_key,
                                    type="file",
                                    filename=file_name,
                                    content_type="text/html",
                                    content_length=len(html_content),
                                )]
                            }
                        }
                    )
                    
                else:
                    completion_message = f"""品牌舆情分析报告已生成完成！

📊 AI总结预览: {ai_summary_preview}

⚠️ HTML报告上传失败: {upload_result.get('message', '')}
📄 HTML内容已保存在响应中，可手动下载。

完整报告包含HTML可视化图表和详细分析。"""


                self.logger.info(
                    f"Final report generated successfully with DSL parsing and S3 upload, session_id:{session_id}")
                writer({"human_feedback_message": "还有其他可以帮助您的吗？"})

                return Command(
                    goto="__end__",
                    update={
                        "workflow_status": WorkflowStatus.INITIALIZING,
                        "final_report": final_report,
                        "html_report": html_content,
                        "report_dsl": report_dsl,
                        "summary_data": summary_data,  # 保存解析后的结构化数据
                        "upload_result": upload_result,  # 保存上传结果
                        "messages": [HumanMessage(content=final_report)]
                    }
                )
            else:
                # 报告生成失败
                error_message = result.get("message", "报告生成失败")
                self.logger.error(f"Report generation failed: {result.get('error')}, session_id:{session_id}")

                # 发送错误消息
                writer({"agent_message": f"报告生成失败：{error_message}"})

                return Command(
                    goto="__end__",
                    update={
                        "workflow_status": WorkflowStatus.FAILED,
                        "error_info": {"message": error_message, "node": "report"}
                    }
                )

        except Exception as e:
            self.logger.error(f"Failed to generate report: {e}", session_id, exc_info=True)

            # Send error message
            writer({"agent_message": f"报告生成失败：{str(e)}"})

            return Command(
                goto="__end__",
                update={
                    "workflow_status": WorkflowStatus.FAILED,
                    "error_info": {"message": f"报告生成失败：{str(e)}", "node": "report"}
                }
            )

    def get_capabilities(self) -> List[str]:
        """Get Agent capability list"""
        return [
            "Final report generation",
            "Data integration and analysis",
            "Professional insight extraction",
            "Recommendation formulation",
            "Brand sentiment professional analysis"
        ]
