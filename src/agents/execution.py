"""
执行Agent - 专门负责任务执行
"""

import datetime
import jwt
from typing import Literal
from langchain_core.messages import HumanMessage
from langchain_core.language_models import BaseChatModel
from langgraph.types import Command
from langgraph.types import StreamWriter

from .base import BaseAgent
from src.models.state import SpecificState, WorkflowStatus, ExecutionPlan, PlanStep

# JWT配置常量（需与验证函数保持一致）
SECRET_KEY = "brand_event"  # 必须与验证函数相同
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60  # Token有效期（48小时）


def create_access_token(user_id: str) -> str:
    """
    生成JWT Token（与verify_token_and_user配套）

    参数:
        user_id: 用户唯一标识，将作为'sub'存入Token

    返回:
        str: 签名的JWT Token
    """
    # 设置过期时间
    expire = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
        minutes=ACCESS_TOKEN_EXPIRE_MINUTES
    )

    # 构建Payload（必须包含sub字段以匹配验证逻辑）
    to_encode = {
        "sub": user_id,  # 用户ID（subject标准字段）
        "exp": expire,  # 过期时间
        "iat": datetime.datetime.now(datetime.timezone.utc),  # 签发时间
        "type": "access"  # 可选的Token类型标识
    }

    # 生成Token
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


class ExecutionAgent(BaseAgent):
    """
    执行Agent
    
    职责：
    1. 按计划执行任务
    2. 报告执行进展
    3. 处理执行过程中的问题
    """

    def __init__(self, llm: BaseChatModel):
        """
        初始化执行Agent

        Args:
            llm: 语言模型实例
        """
        system_prompt = """
        你是专业的任务执行专家，专门负责品牌舆情分析任务的具体执行。

        你的核心能力：
        1. 按照制定的计划逐步执行任务
        2. 提供详细的执行过程报告
        3. 识别和处理执行过程中的问题

        执行原则：
        - 严格按照计划步骤执行
        - 提供清晰的进展报告
        - 遇到问题及时反馈
        - 确保执行质量和效率

        专业领域：品牌舆情分析、数据收集、内容分析、报告生成
        """

        super().__init__(
            llm=llm,
            role_name="ExecutionAgent",
            system_prompt=system_prompt
        )

    async def _get_agent(self, state: SpecificState):
        """获取执行agent，每次使用时获取"""
        try:
            # 尝试创建React Agent with MCP tools
            from langchain_mcp_adapters.client import MultiServerMCPClient
            from langgraph.prebuilt import create_react_agent

            # 从状态中获取用户信息
            user_id = state.get('user_id', '<EMAIL>')  # 默认用户ID
            session_id = state.get('session_id')
            task_id = state.get('task_id')
            sandbox_id = state.get('sandbox_id')

            # 动态生成JWT token
            dynamic_token = create_access_token(user_id)
            self.logger.info(f"Generated dynamic JWT token for user: {user_id}")

            # MCP服务器配置
            server_configs = {
                "brand_event": {
                    "transport": "streamable_http",
                    "url": "http://172.21.65.95:5003/mcp/marketing",
                    "enabled_tools": [],
                    "add_to_agents": [
                        "researcher",
                        "coder"
                    ],
                    "headers": {
                        "sessionId": session_id,
                        "taskId": task_id,
                        "sandbox_id": sandbox_id,
                        "account": user_id,
                        "sign": dynamic_token,
                    }
                }
            }

            # 创建MCP客户端
            mcp_client = MultiServerMCPClient(server_configs)

            # 获取MCP工具
            tools = await mcp_client.get_tools()

            if tools:
                # 创建React agent with MCP tools
                agent = create_react_agent(self.llm, tools)
                self.logger.info(f"Created React agent with {len(tools)} MCP tools")
                return agent
            else:
                # 没有工具时使用普通LLM
                self.logger.warning("No MCP tools available, using plain LLM")
                return self.llm

        except Exception as e:
            self.logger.error(f"Failed to setup React agent: {e}")
            # Fallback到普通LLM
            return self.llm

    async def execute_step(
            self,
            step_index: int,
            plan_step: PlanStep,
            writer: StreamWriter,
            state: SpecificState
    ) -> str:
        """
        执行单个步骤

        Args:
            step_index: 步骤索引
            plan_step: 计划步骤对象
            writer: 流写入器
            state: 状态对象

        Returns:
            执行结果报告
        """
        try:
            self.logger.info(f"Executing step {step_index}: {plan_step.title}")

            # 发送步骤开始前的Agent消息
            writer({"agent_message": f"接下来我将执行第{step_index + 1}个任务：{plan_step.title}"})

            # 获取执行agent
            agent = await self._get_agent(state)

            # 直接执行步骤
            try:
                step_result = await self._execute_step(plan_step, agent, state)
            except Exception as e:
                self.logger.error(f"Step {plan_step.title} failed: {e}")
                step_result = f"❌ {plan_step.title}: 执行失败 - {str(e)}"

            # 发送步骤完成后的Agent消息
            writer({"agent_message": f"第{step_index + 1}个任务已完成：{step_result}"})

            return step_result

        except Exception as e:
            self.logger.error(f"Error executing step {step_index}: {e}")
            return f"执行步骤 {step_index + 1} 时出现错误：{str(e)}"

    async def _execute_step(self, plan_step: PlanStep, agent, state: SpecificState) -> str:
        """
        执行步骤内容

        Args:
            plan_step: 计划步骤对象
            agent: React Agent或LLM
            state: 状态对象

        Returns:
            步骤执行结果
        """
        try:
            # 构建简化的agent输入
            agent_input = {
                "messages": [
                    HumanMessage(
                        content=f"#步骤: {plan_step.title}\n#描述: {plan_step.description}"
                    )
                ]
            }

            # 打印请求数据
            self.logger.info("=" * 60)
            self.logger.info(f"🚀 执行步骤请求数据: {plan_step.title}")
            for m in agent_input["messages"]:
                m.pretty_print()
            self.logger.info("=" * 60)

            # 使用agent执行步骤
            response = await agent.ainvoke(agent_input)

            # 打印返回数据
            self.logger.info("=" * 60)
            self.logger.info(f"📥 执行步骤返回数据:")

            if isinstance(response, dict) and "messages" in response:
                for m in response["messages"]:
                    m.pretty_print()
                result_content = response["messages"][-1].content
                self.logger.info("=" * 60)
                return result_content
            else:
                self.logger.info(f"直接返回: {str(response)[:200]}...")
                self.logger.info("=" * 60)
                return str(response)

        except Exception as e:
            self.logger.error(f"Error executing step {plan_step.title}: {e}")
            raise e

    def validate_execution_plan(self, execution_plan: ExecutionPlan) -> bool:
        """验证执行计划"""
        return (execution_plan and
                execution_plan.title and
                execution_plan.steps and
                len(execution_plan.steps) > 0)

    async def execute(self, state: SpecificState, writer: StreamWriter) -> Command[Literal["summary", "__end__"]]:
        """
        执行Agent的主要调用方法 - Supervisor模式
        专注于业务逻辑，返回状态更新字典

        Args:
            state: 当前状态

        Returns:
            状态更新字典
        """
        session_id = state.get('session_id', 'unknown')
        task_plan_dict = state.get("task_plan", {})

        try:
            # 将字典转换为ExecutionPlan结构体
            if not task_plan_dict:
                self.logger.error(f"Failed task execution, error: no execution plan provided, session_id:{session_id}")
                return Command(
                    goto="__end__",
                    update={
                        "error_info": {"message": "未提供执行计划", "node": "execution"},
                        "workflow_status": WorkflowStatus.FAILED
                    }
                )

            execution_plan = ExecutionPlan(**task_plan_dict)

            # 验证执行计划
            if not self.validate_execution_plan(execution_plan):
                self.logger.error(f"Failed task execution, error: invalid execution plan, session_id:{session_id}")
                return Command(
                    goto="__end__",
                    update={
                        "error_info": {"message": "执行计划无效", "node": "execution"},
                        "workflow_status": WorkflowStatus.FAILED
                    }
                )

            total_steps = len(execution_plan.steps)
            self.logger.info(f"Start doing task execution ({total_steps} steps), session_id:{session_id}")

            # 遍历并执行所有步骤
            step_reports = []
            for step_index, plan_step in enumerate(execution_plan.steps):
                if plan_step.skip_execute:
                    self.logger.info(f"Skipping step {step_index + 1}: {plan_step.title}")
                    continue
                # 发送实时状态消息 - 开始执行
                writer({"live_status_message": f"开始执行{plan_step.title}..."})
                try:
                    step_report = await self.execute_step(step_index, plan_step, writer, state)
                    step_reports.append(step_report)
                except Exception as e:
                    self.logger.error(f"Step {step_index + 1} failed: {e}")
                    step_reports.append(f"### 步骤{step_index + 1}：{plan_step.title}\n❌ 执行失败：{str(e)}")

            # 生成执行报告
            execution_report = f"## {execution_plan.title} - 执行报告\n\n" + "\n\n".join(step_reports)

            # 发送完成消息
            writer({"agent_message": "计划已执行完成"})

            writer({"agent_status_waiting": "任务后台执行中，执行完可在页面查看报告，耗时较长，请稍等..."})
            self.logger.info(f"Execution completed, session_id:{session_id}")

            return Command(
                goto="__end__",
                update={
                    "workflow_status": WorkflowStatus.REPORT,
                    "execution_report": execution_report,
                    "messages": [HumanMessage(content=execution_report)]
                }
            )

        except Exception as e:
            self.logger.error(f"Execution failed: {e}, session_id:{session_id}")
            return Command(goto="__end__", update={"workflow_status": WorkflowStatus.FAILED})
