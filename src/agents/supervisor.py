"""
Supervisor Agent - 基于LLM的动态路由协调器
"""

from typing import Literal, TypedDict
from langchain_core.messages import HumanMessage
from langchain_core.language_models import BaseChatModel
from langgraph.types import Command
from langgraph.types import StreamWriter
from pydantic import BaseModel, Field

from .base import BaseAgent
from src.models import SpecificState, WorkflowStatus


class UserMessageAnalysis(BaseModel):
    """用户消息分析结果"""
    message_type: Literal["greeting", "task", "supplement", "agreement", "rejection", "detailed_description", "short_reply"] = Field(
        description="消息类型：greeting(问候), task(任务请求), supplement(补充信息), agreement(确认同意), rejection(拒绝否定), detailed_description(详细描述), short_reply(简短回复)"
    )
    user_intent: str = Field(description="用户真实意图分析")
    extracted_info: str = Field(default="", description="从用户消息中提取的关键信息")


class RouterDecisionWithAnalysis(BaseModel):
    """路由决策的结构化输出 - 包含用户消息分析"""
    message_analysis: UserMessageAnalysis = Field(description="用户消息分析结果")
    next: Literal["intent_clarification", "planning", "execution", "summary", "report", "__end__"] = Field(
        description="下一步路由决策"
    )
    reason: str = Field(description="路由决策理由")
    response_message: str = Field(default="", description="需要返回给用户的消息（仅问候时使用）")
    workflow_status: str = Field(description="更新后的工作流状态")


class RouterDecision(TypedDict):
    """路由决策的结构化输出（保持向后兼容）"""
    next: Literal["intent_clarification", "planning", "execution", "summary", "report", "__end__"]
    reason: str


class SupervisorAgent(BaseAgent):
    """
    Supervisor Agent - 智能路由分发器

    职责：
    1. 基于LLM分析用户消息类型（问候/任务/补充信息）
    2. 根据消息类型和workflow_status进行智能路由
    3. 保持路由逻辑简单清晰
    4. 支持多轮对话的状态驱动路由
    """

    def __init__(self, llm: BaseChatModel):
        """
        初始化Supervisor Agent

        Args:
            llm: 语言模型实例
        """
        system_prompt = """
        你是一个智能的工作流路由器，负责分析用户消息并进行路由决策。

        你的职责：
        1. 分析用户消息类型：问候、任务请求、任务相关补充信息
        2. 根据消息类型和当前工作流状态进行路由决策
        3. 保持路由逻辑简单清晰

        路由决策规则：
        1. 问候消息 → __end__ (直接返回友好回复)
        2. 任务相关消息 → 根据workflow_status路由：
           - INITIALIZING/未设置 → intent_clarification
           - CLARIFYING_INTENT → intent_clarification
           - PLANNING → planning
           - EXECUTING → execution
           - SUMMARIZING → summary
           - REPORT → report

        请准确分析用户消息类型，并根据当前状态做出正确的路由决策。
        """

        super().__init__(
            llm=llm,
            role_name="SupervisorAgent",
            system_prompt=system_prompt
        )
    def execute(self, state: SpecificState, writer: StreamWriter) -> Command[
        Literal["intent_clarification", "planning", "execution", "summary", "report", "__end__"]]:
        """
        Supervisor的主要路由方法 - 基于LLM的智能路由

        Args:
            state: 当前状态
            writer: 流式消息写入器

        Returns:
            路由命令
        """
        session_id = state.get('session_id', 'unknown')
        self.logger.info(f"Start doing workflow supervision, session_id:{session_id}")

        # 发送监督开始的消息
        writer({"live_status_message": "正在分析当前状态和用户消息..."})

        try:
            # 使用LLM进行智能路由决策
            result = self._llm_based_routing(state, session_id, writer)
            route_reason = result.update.get('reason', 'N/A')

            self.logger.info(f"Completed workflow supervision, result: route to {result.goto}, reason: {route_reason}, session_id:{session_id}")
            return result

        except Exception as e:
            self.logger.error(f"Failed workflow supervision, error: {str(e)}, session_id:{session_id}")

            # 出错时默认路由到意图澄清
            return Command(goto="__end__")

    def _llm_based_routing(self, state: SpecificState, session_id: str, writer: StreamWriter) -> Command:
        """基于LLM的智能路由决策"""
        self.logger.info(f"Starting LLM-based routing analysis, session_id:{session_id}")

        # 发送分析开始的消息
        writer({"live_status_message": "正在进行智能路由分析..."})

        try:
            # 构建路由决策提示词
            routing_prompt = self.build_routing_prompt(state)

            # 发送LLM分析的消息
            writer({"live_status_message": "正在分析用户消息类型和意图..."})

            # 使用结构化输出获取路由决策
            messages = [HumanMessage(content=routing_prompt)]
            response = self.llm.with_structured_output(RouterDecisionWithAnalysis).invoke(messages)

            # 记录分析结果
            self.logger.info(f"User message analysis: type={response.message_analysis.message_type}, session_id:{session_id}")
            self.logger.info(f"LLM routing decision: {response.next}, reason: {response.reason}, session_id:{session_id}")


            # 直接根据LLM返回的结构化结果构建Command
            update_data = {
                "reason": response.reason,
                "workflow_status": response.workflow_status
            }

            # 如果是问候消息，添加回复消息
            if response.message_analysis.message_type == "greeting" and response.next=='__end__' and response.response_message:
                writer({"agent_message": response.response_message})
                writer({"human_feedback_message": "请输入具体任务需求，我将为您提供帮助。"})

            return Command(goto=response.next, update=update_data)

        except Exception as e:
            self.logger.error(f"Failed LLM-based routing, error: {str(e)}, session_id:{session_id}")

            # 发送错误分析消息
            writer({"live_status_message": "路由分析遇到问题，使用默认策略"})

            # 出错时默认路由到意图澄清
            return Command(goto="intent_clarification", update={"workflow_status": "clarifying_intent"})

    def build_routing_prompt(self, state: SpecificState) -> str:
        """
        构建路由决策提示词

        Args:
            state: 当前状态

        Returns:
            路由决策提示词
        """
        # 获取状态信息
        current_status = state.get("workflow_status", WorkflowStatus.INITIALIZING)

        # 获取最新的用户消息
        messages = state.get("messages", [])
        latest_user_message = ""

        for msg in reversed(messages):
            if hasattr(msg, 'content') and str(type(msg)).find('Human') != -1:
                latest_user_message = msg.content
                break

        return f"""
        请分析用户消息并决策下一步路由。

        ## 第一步：用户消息分析

        请先分析用户的最新消息 {latest_user_message}，判断消息类型：

        **消息类型分类：**
        - **greeting（问候）**：比如 你好、您好、hi、hello、嗨等问候语
        - **task（任务请求）**：用户提出的具体任务需求，比如"我想分析XX品牌的舆情"
        - **supplement（补充信息）**：针对当前任务的补充、修改、确认等信息

        ## 第二步：路由决策

        当前工作流状态: {current_status}

        **路由决策规则：**

        1. **问候消息处理**：
           - 如果消息类型是 greeting → 选择 __end__

        2. **任务相关消息处理**：
           - 根据当前workflow_status进行路由：
             * INITIALIZING/未设置 → intent_clarification
             * CLARIFYING_INTENT → intent_clarification
             * PLANNING → planning
             * EXECUTING → execution
             * SUMMARIZING → summary
             * REPORT → report

        请根据用户消息分析和当前workflow_status，做出正确的路由决策。

        **输出要求：**
        必须返回以下JSON格式的结构化结果：

        ```json
        {{
            "message_analysis": {{
                "message_type": "greeting|task|supplement",
                "user_intent": "用户真实意图分析",
                "extracted_info": "从用户消息中提取的关键信息"
            }},
            "next": "intent_clarification|planning|execution|summary|__end__",
            "reason": "路由决策理由",
            "response_message": "需要返回给用户的消息（仅问候时填写）",
            "workflow_status": "更新后的工作流状态"
        }}
        ```

        **字段说明：**
        - message_type: 必须是指定的消息类型之一
        - next: 下一步路由目标
        - reason: 简短的决策理由
        - response_message: 仅当message_type为"greeting"时需要填写友好的问候回复
        - workflow_status: 根据路由决策设置相应的状态值
        """


