"""
规划Agent - 专门负责任务规划和计划管理
"""

from typing import List, Optional, Dict, Any, Literal
from langchain_core.messages import BaseMessage, HumanMessage
from langchain_core.language_models import BaseChatModel
from langgraph.types import Command
from langgraph.types import StreamWriter
from pydantic import BaseModel

from .base import BaseAgent
from src.models.state import SpecificState, ExecutionPlan, UserIntentAnalysis, WorkflowStatus
from langchain_core.messages import SystemMessage


class PlanningAgent(BaseAgent):
    """
    规划Agent
    
    职责：
    1. 创建执行计划
    2. 分析用户对计划的反馈
    3. 优化和调整计划
    """

    def __init__(self, llm: BaseChatModel):
        """
        初始化规划Agent

        Args:
            llm: 语言模型实例
        """
        system_prompt = """
        你是专业的任务规划专家，专门负责品牌舆情分析任务的执行计划制定。

        你的核心能力：
        1. 制定详细、可执行的任务计划
        2. 分析用户对计划的反馈意见
        3. 根据反馈优化和调整计划

        规划原则：
        - 阶段逻辑清晰，循序渐进
        - 任务具体可操作，避免抽象描述
        - 时间估算合理，考虑实际执行难度
        - 成果明确，便于验收和评估

        专业领域：品牌舆情分析、网络传播监测、数据收集与分析
        """

        super().__init__(
            llm=llm,
            role_name="PlanningAgent",
            system_prompt=system_prompt
        )

    def build_planning_prompt(
            self,
            user_input: str,
            previous_plan: Optional[Dict[str, Any]] = None
    ) -> SystemMessage:
        """
        构建规划提示词

        Args:
            user_input: 用户需求
            previous_plan: 之前的计划

        Returns:
            系统消息
        """
        # 格式化历史计划
        previous_plan_str = "无"
        if previous_plan:
            previous_plan_str = self._format_previous_plan(previous_plan)

        prompt = f"""
你是专业的品牌舆情分析任务规划专家。请根据用户需求制定详细的执行计划。

## 用户需求
{user_input}

## 历史计划
{previous_plan_str}

## 规划要求
请按照以下固定流程制定执行计划：

## 执行流程
1. **品牌舆情数据收集阶段**：必须根据用户真实需求，调用品牌MCP Tool获取舆情数据，需要如实传递用户需求。

2. **报告生成阶段**：调用报表服务Tool，获取完整的品牌舆情分析报告；执行计划时需要忽略这一步。

## 返回格式：
请返回JSON格式的执行计划，格式如下：
 {{
    "title": "xxx舆情分析计划",
    "steps": [
         {{
            "title": "收集舆情数据",
            "description": "收集[事件]在[时间范围][平台范围]的舆情数据",
            "skip_execute": false
         }},
         {{
            "title": "生成可视化报告",
            "description": "生成完整的品牌舆情分析报告",
            "skip_execute": true
         }}
    ]
 }}
        """

        return SystemMessage(content=prompt)

    def build_planning_intent_analysis_prompt(self, latest_feedback: str) -> SystemMessage:
        """
        构建规划阶段用户意图分析提示词

        Args:
            latest_feedback: 最新反馈

        Returns:
            系统消息
        """
        prompt = f"""
        你正在分析用户对执行计划的反馈意图。

        用户反馈："{latest_feedback}"

        请分析用户的真实意图，判断用户是：
        1. 同意/确认当前计划（agreement）
        2. 提供修改建议或补充要求（supplement）
        3. 拒绝/要求重新制定计划（rejection）

        分析要点：
        - 同意关键词：同意、确认、好的、可以、继续、开始执行、按计划进行
        - 拒绝关键词：不同意、不行、重新制定、重新规划、不合适
        - 修改建议：具体的调整要求、时间调整、步骤修改、增加/删除内容

        返回JSON格式分析结果：
        {{
            "intent_type": "agreement|supplement|rejection",
            "next_action": "continue|retry",
            "extracted_info": "提取的修改建议（仅supplement类型需要）"
        }}
        """

        return SystemMessage(content=prompt)

    def create_plan(
            self,
            user_input: str,
            previous_plan: Optional[Dict[str, Any]] = None,
            context_messages: Optional[List[BaseMessage]] = None
    ) -> ExecutionPlan:
        """
        创建执行计划

        Args:
            user_input: 用户输入需求
            previous_plan: 历史计划（字典格式）
            context_messages: 上下文消息

        Returns:
            执行计划
        """
        try:
            self.logger.info(f"Creating execution plan for user input: {user_input[:50]}...")

            # 构建规划提示词
            planning_prompt = self.build_planning_prompt(
                user_input=user_input,
                previous_plan=previous_plan
            )

            # 使用结构化输出生成计划
            plan = self.invoke_with_structured_output(
                output_schema=ExecutionPlan,
                user_input="",  # 用户输入已经在prompt中
                context_messages=[planning_prompt] + (context_messages or [])
            )

            self.logger.info(f"Execution plan created: {plan.title}")

            return plan

        except Exception as e:
            self.logger.error(f"Error creating execution plan: {e}")
            # 返回降级计划
            return self._create_fallback_plan(user_input)

    def analyze_plan_feedback(
            self,
            feedback: str,
            context_messages: Optional[List[BaseMessage]] = None
    ) -> UserIntentAnalysis | BaseModel:
        """
        分析用户对计划的反馈
        
        Args:
            feedback: 用户反馈
            context_messages: 上下文消息
            
        Returns:
            用户意图分析结果
        """
        try:
            self.logger.info("Analyzing plan feedback")

            # 构建规划意图分析提示词
            intent_prompt = self.build_planning_intent_analysis_prompt(latest_feedback=feedback)

            # 使用结构化输出分析反馈
            result = self.invoke_with_structured_output(
                output_schema=UserIntentAnalysis,
                user_input="",  # 用户输入已经在prompt中
                context_messages=[intent_prompt] + (context_messages or [])
            )

            self.logger.info(f"Plan feedback analyzed: {result.intent_type}")

            return result

        except Exception as e:
            self.logger.error(f"Error analyzing plan feedback: {e}")
            # 返回降级结果
            return UserIntentAnalysis(
                intent_type="supplement",
                next_action="retry",
                extracted_info=feedback
            )

    def optimize_plan(
            self,
            original_plan: ExecutionPlan,
            feedback: str,
            optimization_focus: List[str] = None
    ) -> ExecutionPlan:
        """
        优化计划
        
        Args:
            original_plan: 原始计划
            feedback: 用户反馈
            optimization_focus: 优化重点
            
        Returns:
            优化后的计划
        """
        try:
            self.logger.info("Optimizing execution plan")

            # 构建优化提示词
            optimization_prompt = f"""
            请根据用户反馈优化以下执行计划：
            
            原始计划：{original_plan.model_dump_json(indent=2)}
            
            用户反馈：{feedback}
            
            优化重点：{optimization_focus or ['整体优化']}
            
            请保持计划的基本结构，重点改进用户关注的方面。
            """

            # 使用结构化输出优化计划
            optimized_plan = self.invoke_with_structured_output(
                output_schema=ExecutionPlan,
                user_input=optimization_prompt
            )

            self.logger.info(f"Plan optimized: {optimized_plan.title}")

            return optimized_plan

        except Exception as e:
            self.logger.error(f"Error optimizing plan: {e}")
            # 返回原始计划
            return original_plan

    def _format_previous_plan(self, plan: Optional[Dict[str, Any]]) -> str:
        """
        格式化之前的计划
        
        Args:
            plan: 计划字典
            
        Returns:
            格式化的计划字符串
        """
        if not plan:
            return "无"

        try:
            formatted = f"标题：{plan.get('title', '未知')}\n"

            steps = plan.get('steps', [])
            if steps:
                formatted += "执行步骤：\n"
                for i, step in enumerate(steps, 1):
                    formatted += f"{i}. {step.get('title', '未知步骤')}\n"
                    if step.get('description'):
                        formatted += f"   描述：{step['description']}\n"

            return formatted

        except Exception as e:
            self.logger.error(f"Error formatting previous plan: {e}")
            return "计划格式化失败"

    def _create_fallback_plan(self, user_input: str) -> ExecutionPlan:
        """
        创建降级计划

        Args:
            user_input: 用户输入需求

        Returns:
            降级执行计划
        """
        from src.models.state import PlanStep

        # 创建固定流程的PlanStep对象列表
        steps = [
            PlanStep(
                title="品牌数据收集",
                description="调用品牌MCP Tool获取品牌事件数据和图表DSL配置，包括连接MCP Tool、获取品牌事件数据、获取图表DSL配置、等待异步执行完成等步骤"
            ),
            PlanStep(
                title="报告生成",
                description="根据品牌事件图表DSL调用报表服务Tool生成分析报告，包括解析DSL、调用报表服务、生成分析报告、格式化输出等步骤",
                skip_execute=True
            )
        ]

        return ExecutionPlan(
            title="品牌舆情分析执行计划",
            steps=steps
        )

    def get_capabilities(self) -> List[str]:
        """获取Agent能力列表"""
        return [
            "执行计划制定",
            "计划反馈分析",
            "计划优化调整",
            "品牌舆情分析专业规划",
            "多轮计划迭代"
        ]

    def validate_requirements(self, requirements: str) -> bool:
        """
        验证需求是否完整

        Args:
            requirements: 用户需求

        Returns:
            是否有效
        """
        if not self.validate_input(requirements):
            return False

        # 检查是否包含基本信息
        requirements_lower = requirements.lower()

        # 检查是否包含品牌信息
        has_brand = any(keyword in requirements_lower for keyword in [
            "品牌", "公司", "产品", "brand", "company"
        ])

        # 检查是否包含分析需求
        has_analysis = any(keyword in requirements_lower for keyword in [
            "分析", "舆情", "传播", "监测", "analysis", "sentiment"
        ])

        return has_brand and has_analysis

    def execute(self, state: SpecificState, writer: StreamWriter) -> Command[
        Literal["planning", "execution", "__end__"]]:
        """
        规划Agent的主要调用方法 - Supervisor模式
        专注于业务逻辑，返回状态更新字典

        Args:
            state: 当前状态

        Returns:
            状态更新字典
        """
        session_id = state.get('session_id', 'unknown')
        planning_round = state.get("planning_round", 0)
        self.logger.info(f"Start doing planning (round {planning_round + 1}), session_id:{session_id}")

        try:

            # 获取基本信息
            latest_feedback = self._get_latest_plan_feedback(state)
            context_messages = state.get("messages", [])

            # 如果有用户反馈且计划未审批通过，先分析用户意图
            if (planning_round > 0 and
                    latest_feedback and
                    state.get("task_plan") and
                    not state.get("plan_approved", False)):

                # 发送分析反馈的状态消息
                writer({"live_status_message": "正在分析您的计划反馈..."})

                intent_analysis = self.analyze_plan_feedback(
                    feedback=latest_feedback,
                    context_messages=context_messages
                )

                if intent_analysis.intent_type == "agreement":
                    # 发送计划确认的消息
                    writer({"live_status_message": "计划已确认，准备开始执行..."})
                    writer({"agent_message": "计划已确认，准备开始执行，请稍等"})

                    self.logger.info(f"Completed planning, result: plan approved, session_id:{session_id}")
                    return Command(
                        goto="execution",
                        update={
                            "plan_approved": True,
                            "execution_started": True,
                            "workflow_status": WorkflowStatus.EXECUTING,  # 审批通过，进入执行阶段
                            "messages": [HumanMessage(content="感谢您的确认！我将按照制定的计划开始执行任务。")]
                        }
                    )

                elif intent_analysis.intent_type == "supplement":
                    # 发送计划修改的消息
                    writer({"live_status_message": "正在根据您的建议调整计划..."})
                    writer({"agent_message": "我理解您的建议，让我根据您的反馈重新调整执行计划。"})

                    self.logger.info(f"Plan modifications requested, regenerating plan, session_id:{session_id}")

            # 发送计划创建的状态消息
            writer({"live_status_message": "制定计划中..."})

            # 创建执行计划
            user_input = self._get_clarified_requirements(state)
            previous_plan = state.get("task_plan")

            execution_plan = self.create_plan(
                user_input=user_input,
                previous_plan=previous_plan,
                context_messages=context_messages
            )

            # 发送计划完成的消息
            writer({"live_status_message": "执行计划制定完成"})
            if previous_plan:
                writer({
                           "agent_message": "已根据您的反馈调整了执行计划。请确认是否符合您的要求。\n" + execution_plan.to_markdown()})
            else:
                writer({
                           "agent_message": "已为您制定了详细的执行计划。请确认是否符合您的需求。\n" + execution_plan.to_markdown()})

            # 格式化计划展示消息并返回
            plan_message = self._format_plan_message(execution_plan, bool(previous_plan))

            # 发送人工审批消息
            feedback_message = f"请输入'同意'或提供反馈信息以重新生成计划"
            writer({"human_feedback_message": feedback_message})

            self.logger.info(
                f"Completed planning, result: plan created ({execution_plan.title}), session_id:{session_id}")
            return Command(
                goto="__end__",
                update={
                    "task_plan": execution_plan.model_dump(),
                    "plan_approved": False,
                    "workflow_status": WorkflowStatus.PLANNING,
                    "messages": [HumanMessage(content=plan_message)],
                    "planning_round": planning_round + 1
                }
            )

        except Exception as e:
            self.logger.error(f"Failed planning, error: {str(e)}, session_id:{session_id}")
            return Command(
                goto="__end__",
                update={
                    "error_info": {"message": str(e), "node": "planning"},
                    "workflow_status": WorkflowStatus.FAILED
                }
            )

    def _get_latest_plan_feedback(self, state: SpecificState) -> Optional[str]:
        """获取最新的计划反馈"""
        messages = state.get("messages", [])
        for msg in reversed(messages):
            if isinstance(msg, HumanMessage):
                return msg.content
        return None

    def _get_clarified_requirements(self, state: SpecificState) -> str:
        """获取澄清后的需求"""
        # 优先使用结构化的澄清结果
        clarification_result = state.get("clarification_result")
        if clarification_result:
            # 构建结构化的需求描述
            requirements = []
            if clarification_result.get("event"):
                requirements.append(f"事件：{clarification_result['event']}")
            if clarification_result.get("time_range"):
                requirements.append(f"时间范围：{clarification_result['time_range']}")
            if clarification_result.get("platform"):
                requirements.append(f"平台范围：{clarification_result['platform']}")
            if requirements:
                structured_requirements = "；".join(requirements)
                # 如果有intent_summary，结合使用
                intent_summary = state.get("intent_summary", "")
                if intent_summary:
                    return f"{intent_summary}。具体要求：{structured_requirements}"
                else:
                    return structured_requirements

        # 降级到使用intent_summary
        intent_summary = state.get("intent_summary", "")
        if intent_summary:
            return intent_summary

        # 最后降级到原始用户输入
        return state.get("user_input", "")

    def _get_plan_feedback_from_messages(self, state: SpecificState) -> str:
        """从消息中获取计划反馈历史"""
        messages = state.get("messages", [])
        feedback_messages = []

        for msg in messages:
            if isinstance(msg, HumanMessage) and "计划" in msg.content:
                feedback_messages.append(msg.content)

        return "\n".join(feedback_messages) if feedback_messages else "无历史反馈"

    def _format_plan_message(self, plan: ExecutionPlan, is_revision: bool = False) -> str:
        """格式化计划展示消息"""
        prefix = "根据您的反馈，我重新制定了执行计划：" if is_revision else "根据您的需求，我制定了以下执行计划："

        # 使用ExecutionPlan的to_markdown方法
        plan_markdown = plan.to_markdown()

        message = f"{prefix}\n\n{plan_markdown}\n"
        message += "请确认此计划是否符合您的需求。如有调整建议，请告诉我。确认无误后，我将开始执行。"

        return message
