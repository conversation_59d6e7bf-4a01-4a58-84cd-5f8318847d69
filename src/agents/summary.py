"""
总结Agent - 专门负责任务总结和报告生成
"""

from typing import List, Optional, Dict, Any, Literal
from langchain_core.messages import BaseMessage, HumanMessage
from langchain_core.language_models import BaseChatModel
from langgraph.types import Command
from langgraph.config import get_stream_writer
from langgraph.types import StreamWriter
import logging

from .base import BaseAgent
from src.models.state import SpecificState, WorkflowStatus
from langchain_core.messages import SystemMessage


class SummaryAgent(BaseAgent):
    """
    总结Agent
    
    职责：
    1. 生成执行总结
    2. 整理任务成果
    3. 提供改进建议
    """
    
    def __init__(self, llm: BaseChatModel):
        """
        初始化总结Agent

        Args:
            llm: 语言模型实例
        """
        system_prompt = """
        你是专业的总结报告专家，专门负责品牌舆情分析任务的总结和报告生成。

        你的核心能力：
        1. 生成简洁而全面的执行总结
        2. 整理和归纳任务执行成果
        3. 提供有价值的改进建议和洞察

        总结原则：
        - 内容准确、逻辑清晰
        - 突出关键成果和发现
        - 提供可行的改进建议
        - 语言专业、易于理解

        专业领域：品牌舆情分析、数据分析报告、业务洞察
        """

        super().__init__(
            llm=llm,
            role_name="SummaryAgent",
            system_prompt=system_prompt
        )

    def build_summary_prompt(self) -> SystemMessage:
        """
        构建总结提示词

        Returns:
            系统消息
        """
        prompt = """
        你是专业的任务总结专家，需要为品牌舆情分析任务生成全面的执行总结。

        ## 总结要求
        1. 回顾整个任务执行过程
        2. 总结主要执行成果和发现
        3. 评估任务完成质量
        4. 提供有价值的洞察和建议

        ## 总结结构
        请按以下格式生成总结：

        ### 任务执行总结

        **任务概述**
        - 用户需求回顾
        - 执行计划概要
        - 总体执行情况

        **主要成果**
        - 关键执行步骤回顾
        - 重要发现和结果
        - 数据分析成果

        **质量评估**
        - 执行完整性评价
        - 结果质量分析
        - 目标达成情况

        **洞察与建议**
        - 关键洞察总结
        - 改进建议
        - 后续行动建议

        **总结**
        - 整体评价
        - 价值体现
        - 结语

        请基于对话历史和执行过程，生成专业、全面的总结报告。
        """

        return SystemMessage(content=prompt)
    
    def generate_summary(
        self,
        user_requirements: str,
        execution_plan: Optional[Dict[str, Any]] = None,
        execution_results: Optional[List[str]] = None,
        context_messages: Optional[List[BaseMessage]] = None
    ) -> str:
        """
        生成执行总结
        
        Args:
            user_requirements: 用户原始需求
            execution_plan: 执行计划
            execution_results: 执行结果列表
            context_messages: 上下文消息
            
        Returns:
            总结报告
        """
        try:
            self.logger.info("Generating execution summary")
            
            # 构建总结提示词
            summary_prompt = self.build_summary_prompt()
            
            # 构建详细的总结内容
            summary_context = self._build_summary_context(
                user_requirements=user_requirements,
                execution_plan=execution_plan,
                execution_results=execution_results
            )
            
            # 调用LLM生成总结
            response = self.invoke(
                user_input="",  # 用户输入已经在context中
                context_messages=[summary_prompt] + (context_messages or []),
                additional_system_prompt=summary_context
            )
            
            summary_result = response.content
            
            self.logger.info("Summary generation completed")
            
            return summary_result
            
        except Exception as e:
            self.logger.error(f"Error generating summary: {e}")
            return self._generate_fallback_summary(user_requirements)
    
    def analyze_execution_quality(
        self,
        execution_plan: Dict[str, Any],
        execution_results: List[str],
        user_requirements: str
    ) -> Dict[str, Any]:
        """
        分析执行质量
        
        Args:
            execution_plan: 执行计划
            execution_results: 执行结果
            user_requirements: 用户需求
            
        Returns:
            质量分析结果
        """
        try:
            self.logger.info("Analyzing execution quality")
            
            quality_analysis = {
                "completeness_score": self._calculate_completeness_score(execution_plan, execution_results),
                "quality_score": self._calculate_quality_score(execution_results),
                "alignment_score": self._calculate_alignment_score(user_requirements, execution_results),
                "overall_score": 0,
                "strengths": [],
                "improvements": []
            }
            
            # 计算总体评分
            quality_analysis["overall_score"] = round(
                (quality_analysis["completeness_score"] + 
                 quality_analysis["quality_score"] + 
                 quality_analysis["alignment_score"]) / 3, 2
            )
            
            # 分析优势和改进点
            quality_analysis["strengths"] = self._identify_strengths(quality_analysis)
            quality_analysis["improvements"] = self._identify_improvements(quality_analysis)
            
            self.logger.info(f"Quality analysis completed: overall score {quality_analysis['overall_score']}")
            
            return quality_analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing execution quality: {e}")
            return {
                "completeness_score": 0,
                "quality_score": 0,
                "alignment_score": 0,
                "overall_score": 0,
                "strengths": [],
                "improvements": ["分析过程中出现错误，建议重新评估"],
                "error": str(e)
            }
    
    def _build_summary_context(
        self,
        user_requirements: str,
        execution_plan: Optional[Dict[str, Any]],
        execution_results: Optional[List[str]]
    ) -> str:
        """
        构建总结上下文
        
        Args:
            user_requirements: 用户需求
            execution_plan: 执行计划
            execution_results: 执行结果
            
        Returns:
            总结上下文
        """
        context = f"""
        ## 任务总结上下文
        
        ### 用户原始需求
        {user_requirements}
        
        ### 执行计划
        """
        
        if execution_plan:
            context += f"""
        计划标题：{execution_plan.get('title', '未知')}
        执行步骤：
        """
            steps = execution_plan.get('steps', [])
            for i, step in enumerate(steps, 1):
                context += f"        {i}. {step.get('title', '未知步骤')}\n"
        else:
            context += "        无执行计划信息\n"
        
        context += "\n        ### 执行结果\n"
        
        if execution_results:
            for i, result in enumerate(execution_results, 1):
                context += f"        步骤 {i} 结果：{result[:200]}...\n"
        else:
            context += "        无执行结果信息\n"
        
        return context
    
    def _calculate_completeness_score(
        self,
        execution_plan: Dict[str, Any],
        execution_results: List[str]
    ) -> float:
        """计算完整性评分"""
        if not execution_plan or not execution_results:
            return 0.0
        
        planned_steps = len(execution_plan.get('steps', []))
        completed_steps = len(execution_results)
        
        if planned_steps == 0:
            return 0.0
        
        return min(1.0, completed_steps / planned_steps) * 100
    
    def _calculate_quality_score(self, execution_results: List[str]) -> float:
        """计算质量评分"""
        if not execution_results:
            return 0.0
        
        # 简单的质量评估：基于结果长度和内容丰富度
        total_length = sum(len(result) for result in execution_results)
        avg_length = total_length / len(execution_results)
        
        # 假设平均长度在100-500字符之间为较好质量
        if avg_length >= 100:
            return min(100.0, 50 + (avg_length - 100) / 10)
        else:
            return avg_length / 2
    
    def _calculate_alignment_score(
        self,
        user_requirements: str,
        execution_results: List[str]
    ) -> float:
        """计算需求匹配度评分"""
        if not user_requirements or not execution_results:
            return 0.0
        
        # 简单的匹配度评估：检查关键词重叠
        requirements_words = set(user_requirements.lower().split())
        results_text = " ".join(execution_results).lower()
        
        matched_words = sum(1 for word in requirements_words if word in results_text)
        
        if len(requirements_words) == 0:
            return 0.0
        
        return (matched_words / len(requirements_words)) * 100
    
    def _identify_strengths(self, quality_analysis: Dict[str, Any]) -> List[str]:
        """识别执行优势"""
        strengths = []
        
        if quality_analysis["completeness_score"] >= 80:
            strengths.append("任务执行完整度高")
        
        if quality_analysis["quality_score"] >= 70:
            strengths.append("执行结果质量良好")
        
        if quality_analysis["alignment_score"] >= 75:
            strengths.append("很好地满足了用户需求")
        
        if quality_analysis["overall_score"] >= 80:
            strengths.append("整体执行效果优秀")
        
        return strengths if strengths else ["任务已完成基本执行"]
    
    def _identify_improvements(self, quality_analysis: Dict[str, Any]) -> List[str]:
        """识别改进点"""
        improvements = []
        
        if quality_analysis["completeness_score"] < 80:
            improvements.append("建议完善任务执行的完整性")
        
        if quality_analysis["quality_score"] < 70:
            improvements.append("建议提升执行结果的质量和深度")
        
        if quality_analysis["alignment_score"] < 75:
            improvements.append("建议更好地对齐用户需求")
        
        return improvements if improvements else ["继续保持当前执行水平"]
    
    def _generate_fallback_summary(self, user_requirements: str) -> str:
        """生成降级总结"""
        return f"""
        ## 任务执行总结
        
        ### 任务概述
        用户需求：{user_requirements}
        
        ### 执行状态
        任务已完成基本执行流程。
        
        ### 总结
        由于系统限制，无法生成详细总结，但任务执行流程已按计划进行。
        建议查看具体执行步骤的详细结果。
        """
    
    def get_capabilities(self) -> List[str]:
        """获取Agent能力列表"""
        return [
            "执行总结生成",
            "任务成果整理",
            "执行质量分析",
            "改进建议提供",
            "专业报告撰写"
        ]

    def execute(self, state: SpecificState, writer: StreamWriter) -> Command[Literal["__end__"]]:
        """
        总结Agent的主要调用方法 - Supervisor模式
        专注于业务逻辑，返回状态更新字典

        Args:
            state: 当前状态

        Returns:
            状态更新字典
        """
        session_id = state.get('session_id', 'unknown')
        self.logger.info(f"Start doing task summary generation, session_id:{session_id}")

        try:
            # 发送任务开始的Agent消息
            start_message = "任务执行已完成！现在让我为您生成详细的总结报告，分析执行结果和质量。"
            writer({"agent_message": start_message})

            # 发送实时状态消息
            writer({"live_status_message": "正在收集执行结果..."})
            # 收集总结所需信息
            user_requirements = self._get_clarified_requirements(state)
            task_plan = state.get("task_plan", {})
            context_messages = state.get("messages", [])

            # 获取执行结果 - 优先使用结构化的执行结果
            execution_results = state.get("execution_results", [])
            if not execution_results:
                # 降级：从消息中提取执行结果
                execution_results = []
                for msg in context_messages:
                    if isinstance(msg, HumanMessage) and "执行" in msg.content:
                        execution_results.append(msg.content)

            # 发送生成总结的状态消息
            writer({"live_status_message": "正在生成总结报告..."})
            writer({"agent_message": "我正在分析所有执行结果，为您生成全面的总结报告。"})

            # 使用SummaryAgent生成总结
            summary_result = self.generate_summary(
                user_requirements=user_requirements,
                execution_plan=task_plan,
                execution_results=execution_results,
                context_messages=context_messages
            )

            # 发送质量分析的状态消息
            writer({"live_status_message": "正在分析执行质量..."})

            # 分析执行质量
            quality_analysis = None
            if execution_results and task_plan:
                # 处理新的执行结果格式
                if isinstance(execution_results, list) and execution_results and isinstance(execution_results[0], dict):
                    # 新格式：结构化的执行结果
                    quality_analysis = self._analyze_structured_execution_quality(
                        execution_plan=task_plan,
                        execution_results=execution_results,
                        user_requirements=user_requirements
                    )
                else:
                    # 旧格式：文本执行结果
                    quality_analysis = self.analyze_execution_quality(
                        execution_plan=task_plan,
                        execution_results=execution_results,
                        user_requirements=user_requirements
                    )

            # 发送总结完成的消息
            writer({"live_status_message": "总结报告生成完成"})

            # 根据质量分析生成完成消息
            if quality_analysis:
                overall_score = quality_analysis.get('overall_score', 0)
                if overall_score >= 80:
                    completion_message = "🎉 完美！我已为您生成了高质量的总结报告。任务执行效果优秀，所有目标都得到了很好的实现。"
                elif overall_score >= 60:
                    completion_message = "✅ 很好！我已为您生成了详细的总结报告。任务执行效果良好，大部分目标都已实现。"
                else:
                    completion_message = "📋 我已为您生成了总结报告。虽然执行过程中遇到了一些挑战，但任务已基本完成。"
            else:
                completion_message = "📋 我已为您生成了详细的总结报告，包含了完整的执行过程和结果分析。"

            writer({"agent_message": completion_message})

            # 返回状态更新，让Supervisor决定是否结束
            update_data = {
                "workflow_status": WorkflowStatus.COMPLETED,
                "summary": summary_result,
                "messages": [HumanMessage(content=summary_result)]
            }

            if quality_analysis:
                update_data["quality_analysis"] = quality_analysis

            # 计算总结质量指标
            summary_length = len(summary_result)
            quality_score = quality_analysis.get('overall_score', 0) if quality_analysis else 0
            result_summary = f"summary generated ({summary_length} chars, quality: {quality_score})"

            self.logger.info(f"Completed task summary generation, result: {result_summary}, session_id:{session_id}")
            return Command(goto="__end__", update=update_data)

        except Exception as e:
            self.logger.error(f"Failed task summary generation, error: {str(e)}, session_id:{session_id}")

            # 使用SummaryAgent生成降级总结
            fallback_summary = self._generate_fallback_summary(
                user_requirements=state.get('user_input', '未知需求')
            )

            return Command(
                goto="__end__",
                update={
                    "workflow_status": WorkflowStatus.COMPLETED,
                    "summary": fallback_summary,
                    "messages": [HumanMessage(content=fallback_summary)],
                    "error_info": {"message": str(e), "node": "summary"}
                }
            )

    def _get_clarified_requirements(self, state: SpecificState) -> str:
        """获取澄清后的需求"""
        intent_summary = state.get("intent_summary", "")
        if intent_summary:
            return intent_summary
        return state.get("user_input", "")

    def _analyze_structured_execution_quality(
        self,
        execution_plan: Dict[str, Any],
        execution_results: List[Dict[str, Any]],
        user_requirements: str
    ) -> Dict[str, Any]:
        """
        分析结构化执行结果的质量

        Args:
            execution_plan: 执行计划
            execution_results: 结构化执行结果列表
            user_requirements: 用户需求

        Returns:
            质量分析结果
        """
        try:
            self.logger.info("Analyzing structured execution quality")

            total_steps = len(execution_plan.get("steps", []))
            completed_steps = len(execution_results)
            successful_steps = len([r for r in execution_results if not r.get('error', False)])
            failed_steps = completed_steps - successful_steps

            # 计算各项评分
            completeness_score = (completed_steps / total_steps) * 100 if total_steps > 0 else 0
            success_rate = (successful_steps / completed_steps) * 100 if completed_steps > 0 else 0

            # 质量评分基于成功率和结果内容
            quality_score = self._calculate_structured_quality_score(execution_results)

            # 需求匹配度评分
            alignment_score = self._calculate_structured_alignment_score(
                user_requirements, execution_results
            )

            # 总体评分
            overall_score = round(
                (completeness_score * 0.3 + success_rate * 0.3 + quality_score * 0.2 + alignment_score * 0.2), 2
            )

            quality_analysis = {
                "completeness_score": round(completeness_score, 2),
                "success_rate": round(success_rate, 2),
                "quality_score": round(quality_score, 2),
                "alignment_score": round(alignment_score, 2),
                "overall_score": overall_score,
                "total_steps": total_steps,
                "completed_steps": completed_steps,
                "successful_steps": successful_steps,
                "failed_steps": failed_steps,
                "strengths": self._identify_structured_strengths(successful_steps, total_steps, overall_score),
                "improvements": self._identify_structured_improvements(failed_steps, overall_score)
            }

            self.logger.info(f"Structured quality analysis completed: overall score {overall_score}")

            return quality_analysis

        except Exception as e:
            self.logger.error(f"Error analyzing structured execution quality: {e}")
            return {
                "completeness_score": 0,
                "success_rate": 0,
                "quality_score": 0,
                "alignment_score": 0,
                "overall_score": 0,
                "error": str(e)
            }

    def _calculate_structured_quality_score(self, execution_results: List[Dict[str, Any]]) -> float:
        """计算结构化结果的质量评分"""
        if not execution_results:
            return 0.0

        total_score = 0
        for result in execution_results:
            if result.get('error', False):
                # 失败步骤得分较低
                total_score += 20
            else:
                # 成功步骤基于结果内容长度和质量评分
                result_content = result.get('result', '')
                content_length = len(result_content)

                if content_length >= 200:
                    total_score += 90
                elif content_length >= 100:
                    total_score += 70
                elif content_length >= 50:
                    total_score += 50
                else:
                    total_score += 30

        return total_score / len(execution_results)

    def _calculate_structured_alignment_score(
        self,
        user_requirements: str,
        execution_results: List[Dict[str, Any]]
    ) -> float:
        """计算结构化结果的需求匹配度评分"""
        if not user_requirements or not execution_results:
            return 0.0

        requirements_words = set(user_requirements.lower().split())

        total_alignment = 0
        for result in execution_results:
            if not result.get('error', False):
                result_content = result.get('result', '').lower()
                matched_words = sum(1 for word in requirements_words if word in result_content)
                alignment = (matched_words / len(requirements_words)) * 100 if requirements_words else 0
                total_alignment += alignment

        successful_results = [r for r in execution_results if not r.get('error', False)]
        return total_alignment / len(successful_results) if successful_results else 0

    def _identify_structured_strengths(
        self,
        successful_steps: int,
        total_steps: int,
        overall_score: float
    ) -> List[str]:
        """识别结构化执行的优势"""
        strengths = []

        success_rate = (successful_steps / total_steps) * 100 if total_steps > 0 else 0

        if success_rate >= 90:
            strengths.append("执行成功率极高")
        elif success_rate >= 70:
            strengths.append("执行成功率良好")

        if successful_steps == total_steps:
            strengths.append("所有步骤均成功执行")

        if overall_score >= 80:
            strengths.append("整体执行质量优秀")
        elif overall_score >= 60:
            strengths.append("整体执行质量良好")

        return strengths if strengths else ["任务已完成基本执行"]

    def _identify_structured_improvements(
        self,
        failed_steps: int,
        overall_score: float
    ) -> List[str]:
        """识别结构化执行的改进点"""
        improvements = []

        if failed_steps > 0:
            improvements.append(f"需要改进{failed_steps}个失败步骤的执行")

        if overall_score < 60:
            improvements.append("建议提升整体执行质量")

        if overall_score < 40:
            improvements.append("建议重新审视执行策略")

        return improvements if improvements else ["继续保持当前执行水平"]
