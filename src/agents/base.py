"""
BaseAgent - 统一的Agent基类
提供LLM调用、角色设定、系统提示词、工具管理等通用功能
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Type
from langchain_core.messages import BaseMessage, SystemMessage, HumanMessage
from langchain_core.language_models import BaseChatModel
from langchain_core.tools import BaseTool
from pydantic import BaseModel
import logging


class BaseAgent(ABC):
    """
    Agent基类，提供统一的接口和通用功能
    
    设计原则：
    1. 统一的LLM调用接口
    2. 灵活的角色和提示词配置
    3. 可选的工具集成
    4. 结构化输出支持
    5. 可扩展的自定义方法
    """
    
    def __init__(
        self,
        llm: BaseChatModel,
        role_name: str = "Assistant",
        system_prompt: str = "",
        tools: Optional[List[BaseTool]] = None
    ):
        """
        初始化Agent

        Args:
            llm: 语言模型实例
            role_name: Agent角色名称
            system_prompt: 系统提示词
            tools: 可用工具列表
        """
        self.llm = llm
        self.role_name = role_name
        self.system_prompt = system_prompt
        self.tools = tools or []

        # 每个Agent使用自己的logger，基于类名和角色名
        logger_name = f"specific.agents.{self.__class__.__name__.lower()}"
        self.logger = logging.getLogger(logger_name)
        
        # 如果有工具，绑定到LLM
        if self.tools:
            self.llm_with_tools = self.llm.bind_tools(self.tools)
        else:
            self.llm_with_tools = self.llm
    
    def set_system_prompt(self, prompt: str) -> None:
        """设置系统提示词"""
        self.system_prompt = prompt
    
    def add_tool(self, tool: BaseTool) -> None:
        """添加工具"""
        self.tools.append(tool)
        self.llm_with_tools = self.llm.bind_tools(self.tools)
    
    def build_messages(
        self,
        user_input: str,
        context_messages: Optional[List[BaseMessage]] = None,
        additional_system_prompt: str = ""
    ) -> List[BaseMessage]:
        """
        构建消息列表
        
        Args:
            user_input: 用户输入
            context_messages: 上下文消息
            additional_system_prompt: 额外的系统提示词
            
        Returns:
            构建好的消息列表
        """
        messages = []
        
        # 添加系统消息
        full_system_prompt = self.system_prompt
        if additional_system_prompt:
            full_system_prompt += f"\n\n{additional_system_prompt}"
        
        if full_system_prompt:
            messages.append(SystemMessage(content=full_system_prompt))
        
        # 添加上下文消息
        if context_messages:
            messages.extend(context_messages)
        
        # 添加用户输入
        if user_input:
            messages.append(HumanMessage(content=user_input))
        
        return messages
    
    def invoke(
        self,
        user_input: str,
        context_messages: Optional[List[BaseMessage]] = None,
        additional_system_prompt: str = "",
        use_tools: bool = True
    ) -> Any:
        """
        统一的调用接口

        Args:
            user_input: 用户输入
            context_messages: 上下文消息
            additional_system_prompt: 额外的系统提示词
            use_tools: 是否使用工具

        Returns:
            LLM响应结果
        """
        try:
            messages = self.build_messages(user_input, context_messages, additional_system_prompt)

            # 选择使用带工具的LLM还是普通LLM
            llm_to_use = self.llm_with_tools if (use_tools and self.tools) else self.llm

            self.logger.debug(f"{self.role_name} invoking LLM with {len(messages)} messages")

            response = llm_to_use.invoke(messages)

            self.logger.debug(f"{self.role_name} received response")

            return response

        except Exception as e:
            self.logger.error(f"Error in {self.role_name} invoke: {e}")
            raise

    def invoke_with_structured_output(
        self,
        output_schema: Type[BaseModel],
        user_input: str,
        context_messages: Optional[List[BaseMessage]] = None,
        additional_system_prompt: str = "",
        method: str = "json_mode"
    ) -> BaseModel:
        """
        使用结构化输出的调用接口
        
        Args:
            output_schema: 输出结构模式
            user_input: 用户输入
            context_messages: 上下文消息
            additional_system_prompt: 额外的系统提示词
            method: 结构化输出方法
            
        Returns:
            结构化输出结果
        """
        try:
            messages = self.build_messages(user_input, context_messages, additional_system_prompt)
            
            # 使用结构化输出
            structured_llm = self.llm.with_structured_output(output_schema, method=method)
            
            self.logger.debug(f"{self.role_name} invoking structured LLM")
            
            response = structured_llm.invoke(messages)
            
            self.logger.debug(f"{self.role_name} received structured response")
            
            return response
            
        except Exception as e:
            self.logger.error(f"Error in {self.role_name} structured invoke: {e}")
            raise
    
    def get_role_info(self) -> Dict[str, Any]:
        """获取Agent角色信息"""
        return {
            "role_name": self.role_name,
            "system_prompt": self.system_prompt,
            "tools_count": len(self.tools),
            "tools": [tool.name for tool in self.tools] if self.tools else []
        }

    def get_capabilities(self) -> List[str]:
        """
        获取Agent能力列表（抽象方法，子类必须实现）
        
        Returns:
            能力描述列表
        """
        pass
    
    def validate_input(self, user_input: str) -> bool:
        """
        验证输入（可选重写）
        
        Args:
            user_input: 用户输入
            
        Returns:
            是否有效
        """
        return bool(user_input and user_input.strip())
    
    def preprocess_input(self, user_input: str) -> str:
        """
        预处理输入（可选重写）
        
        Args:
            user_input: 原始用户输入
            
        Returns:
            处理后的输入
        """
        return user_input.strip()
    
    def postprocess_output(self, output: Any) -> Any:
        """
        后处理输出（可选重写）
        
        Args:
            output: 原始输出
            
        Returns:
            处理后的输出
        """
        return output
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}(role={self.role_name}, tools={len(self.tools)})"
    
    def __repr__(self) -> str:
        return self.__str__()
