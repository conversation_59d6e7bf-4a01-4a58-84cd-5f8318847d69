"""
BaseAgent - Unified Agent base class
Provides LLM invocation, role configuration, system prompts, and tool management
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Type
from langchain_core.messages import BaseMessage, SystemMessage, HumanMessage
from langchain_core.language_models import BaseChatModel
from langchain_core.tools import BaseTool
from pydantic import BaseModel
import logging
from src.utils.logging import SpecificLogger


class BaseAgent(ABC):
    """
    Base Agent class providing unified interface and common functionality

    Design principles:
    1. Unified LLM invocation interface
    2. Flexible role and prompt configuration
    3. Optional tool integration
    4. Structured output support
    5. Extensible custom methods
    """

    def __init__(
        self,
        llm: BaseChatModel,
        role_name: str = "Assistant",
        system_prompt: str = "",
        tools: Optional[List[BaseTool]] = None
    ):
        """
        Initialize Agent

        Args:
            llm: Language model instance
            role_name: Agent role name
            system_prompt: System prompt
            tools: Available tools list
        """
        self.llm = llm
        self.role_name = role_name
        self.system_prompt = system_prompt
        self.tools = tools or []

        # Each Agent uses standardized logger with role name
        self.logger = SpecificLogger(f"Agent:{role_name}")

        # Bind tools to LLM if available
        if self.tools:
            self.llm_with_tools = self.llm.bind_tools(self.tools)
        else:
            self.llm_with_tools = self.llm
    
    def set_system_prompt(self, prompt: str) -> None:
        """Set system prompt"""
        self.system_prompt = prompt

    def add_tool(self, tool: BaseTool) -> None:
        """Add tool to agent"""
        self.tools.append(tool)
        self.llm_with_tools = self.llm.bind_tools(self.tools)

    def build_messages(
        self,
        user_input: str,
        context_messages: Optional[List[BaseMessage]] = None,
        additional_system_prompt: str = ""
    ) -> List[BaseMessage]:
        """
        Build message list for LLM invocation

        Args:
            user_input: User input
            context_messages: Context messages
            additional_system_prompt: Additional system prompt

        Returns:
            Built message list
        """
        messages = []

        # Add system message
        full_system_prompt = self.system_prompt
        if additional_system_prompt:
            full_system_prompt += f"\n\n{additional_system_prompt}"

        if full_system_prompt:
            messages.append(SystemMessage(content=full_system_prompt))

        # Add context messages
        if context_messages:
            messages.extend(context_messages)

        # Add user input
        if user_input:
            messages.append(HumanMessage(content=user_input))

        return messages
    
    def invoke(
        self,
        user_input: str,
        context_messages: Optional[List[BaseMessage]] = None,
        additional_system_prompt: str = "",
        use_tools: bool = True
    ) -> Any:
        """
        Unified invocation interface

        Args:
            user_input: User input
            context_messages: Context messages
            additional_system_prompt: Additional system prompt
            use_tools: Whether to use tools

        Returns:
            LLM response result
        """
        try:
            messages = self.build_messages(user_input, context_messages, additional_system_prompt)

            # Choose LLM with tools or regular LLM
            llm_to_use = self.llm_with_tools if (use_tools and self.tools) else self.llm

            self.logger.debug(f"Invoking LLM with {len(messages)} messages")

            response = llm_to_use.invoke(messages)

            self.logger.debug("Received LLM response")

            return response

        except Exception as e:
            self.logger.error(f"Error in LLM invoke: {e}", exc_info=True)
            raise

    def invoke_with_structured_output(
        self,
        output_schema: Type[BaseModel],
        user_input: str,
        context_messages: Optional[List[BaseMessage]] = None,
        additional_system_prompt: str = "",
        method: str = "json_mode"
    ) -> BaseModel:
        """
        Invocation interface with structured output

        Args:
            output_schema: Output structure schema
            user_input: User input
            context_messages: Context messages
            additional_system_prompt: Additional system prompt
            method: Structured output method

        Returns:
            Structured output result
        """
        try:
            messages = self.build_messages(user_input, context_messages, additional_system_prompt)

            # Use structured output
            structured_llm = self.llm.with_structured_output(output_schema, method=method)

            self.logger.debug("Invoking structured LLM")

            response = structured_llm.invoke(messages)

            self.logger.debug("Received structured response")

            return response

        except Exception as e:
            self.logger.error(f"Error in structured invoke: {e}", exc_info=True)
            raise
    
    def get_role_info(self) -> Dict[str, Any]:
        """Get Agent role information"""
        return {
            "role_name": self.role_name,
            "system_prompt": self.system_prompt,
            "tools_count": len(self.tools),
            "tools": [tool.name for tool in self.tools] if self.tools else []
        }

    def get_capabilities(self) -> List[str]:
        """
        Get Agent capability list (abstract method, must be implemented by subclasses)

        Returns:
            Capability description list
        """
        pass

    def validate_input(self, user_input: str) -> bool:
        """
        Validate input (optional override)

        Args:
            user_input: User input

        Returns:
            Whether input is valid
        """
        return bool(user_input and user_input.strip())

    def preprocess_input(self, user_input: str) -> str:
        """
        Preprocess input (optional override)

        Args:
            user_input: Raw user input

        Returns:
            Processed input
        """
        return user_input.strip()

    def postprocess_output(self, output: Any) -> Any:
        """
        Postprocess output (optional override)

        Args:
            output: Raw output

        Returns:
            Processed output
        """
        return output
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}(role={self.role_name}, tools={len(self.tools)})"
    
    def __repr__(self) -> str:
        return self.__str__()
