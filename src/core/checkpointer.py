"""
Checkpointer 工厂和配置模块
支持 LangGraph 官方的持久化方案
"""

import os
import logging
from typing import Optional, Dict, Any
from pathlib import Path

from langgraph.checkpoint.memory import MemorySaver

# 可选依赖导入
try:
    import aiosqlite
    from langgraph.checkpoint.sqlite.aio import AsyncSqliteSaver
    SQLITE_AVAILABLE = True
except ImportError:
    SQLITE_AVAILABLE = False

try:
    from langgraph.checkpoint.postgres import PostgresSaver
    from psycopg import Connection
    POSTGRES_AVAILABLE = True
except ImportError:
    POSTGRES_AVAILABLE = False

logger = logging.getLogger("specific.core.checkpointer")

class CheckpointerConfig:
    """Checkpointer 配置类"""

    def __init__(
        self,
        type: str = "memory",
        database_path: Optional[str] = None,
        postgres_url: Optional[str] = None,
        **kwargs
    ):
        self.type = type
        self.database_path = database_path
        self.postgres_url = postgres_url
        self.kwargs = kwargs


def create_checkpointer(config: CheckpointerConfig = None):
    """
    创建 checkpointer 实例
    
    Args:
        config: Checkpointer 配置
        
    Returns:
        Checkpointer 实例
    """
    if config is None:
        config = CheckpointerConfig()
    
    if config.type == "memory":
        logger.info("Using MemorySaver checkpointer")
        return MemorySaver()
    
    elif config.type == "sqlite":
        if not SQLITE_AVAILABLE:
            logger.warning("SQLite dependencies not available, falling back to memory checkpointer")
            return MemorySaver()

        db_path = config.database_path or "data/checkpoints.db"

        # 确保使用绝对路径
        if not os.path.isabs(db_path):
            db_path = os.path.abspath(db_path)

        # 确保目录存在
        db_dir = Path(db_path).parent
        try:
            db_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {db_dir}")
        except Exception as e:
            logger.error(f"Failed to create directory {db_dir}: {e}")
            logger.info("Falling back to memory checkpointer")
            return MemorySaver()

        # 检查目录权限
        if not os.access(db_dir, os.W_OK):
            logger.error(f"No write permission for directory: {db_dir}")
            logger.info("Falling back to memory checkpointer")
            return MemorySaver()

        logger.info(f"Using AsyncSqliteSaver checkpointer with database: {db_path}")

        try:
            conn=aiosqlite.connect(db_path)
            checkpointer = AsyncSqliteSaver(conn)
            # await checkpointer.setup()
            logger.info(f"AsyncSqliteSaver created successfully with database: {db_path}")
            return checkpointer
        except Exception as e:
            logger.error(f"Failed to create AsyncSqliteSaver: {e}")
            logger.info("Falling back to memory checkpointer")
            return MemorySaver()

    elif config.type == "postgres":
        if not POSTGRES_AVAILABLE:
            logger.warning("PostgreSQL dependencies not available, falling back to memory checkpointer")
            return MemorySaver()

        postgres_url = "postgresql://liai_kb_rw:<EMAIL>:3306/liai_kb_dev"

        connection_kwargs = {
            "autocommit": True,
            "prepare_threshold": 0,
        }

        try:
            conn = Connection.connect(postgres_url, **connection_kwargs)
            checkpointer = PostgresSaver(conn)
            checkpointer.setup()
            logger.info(f"Using PostgresSaver checkpointer with URL: {postgres_url}")
            return checkpointer
        except Exception as e:
            logger.error(f"Failed to create PostgresSaver: {e}")
            logger.info("Falling back to memory checkpointer")
            return MemorySaver()
    
    else:
        logger.warning(f"Unknown checkpointer type: {config.type}, falling back to memory")
        return MemorySaver()


def get_default_checkpointer():
    """
    获取默认的 checkpointer

    根据环境变量或配置文件决定使用哪种类型
    优先级：memory（暂时禁用 sqlite 直到修复异步问题）
    """
    # 从环境变量读取配置，暂时默认使用 memory
    checkpointer_type = os.getenv("CHECKPOINTER_TYPE", "memory")
    database_path = os.getenv("CHECKPOINTER_DB_PATH", "data/checkpoints.db")
    postgres_url = os.getenv("CHECKPOINTER_POSTGRES_URL", "postgresql://localhost/langgraph")

    config = CheckpointerConfig(
        type=checkpointer_type,
        database_path=database_path,
        postgres_url=postgres_url
    )

    return create_checkpointer(config)


class CheckpointerManager:
    """
    Checkpointer 管理器
    提供会话管理和清理功能
    """
    
    def __init__(self, checkpointer):
        self.checkpointer = checkpointer
        self.logger = logging.getLogger("specific.core.checkpointer_manager")
    
    def list_sessions(self) -> list:
        """列出所有会话"""
        try:
            if hasattr(self.checkpointer, 'list'):
                return self.checkpointer.list()
            else:
                self.logger.warning("Checkpointer does not support listing sessions")
                return []
        except Exception as e:
            self.logger.error(f"Error listing sessions: {e}")
            return []
    
    def get_session_history(self, session_id: str) -> list:
        """获取会话历史"""
        try:
            if hasattr(self.checkpointer, 'get_tuple'):
                # 这里需要根据具体的 checkpointer 实现来调整
                return []
            else:
                self.logger.warning("Checkpointer does not support getting session history")
                return []
        except Exception as e:
            self.logger.error(f"Error getting session history for {session_id}: {e}")
            return []
    
    def clear_session(self, session_id: str) -> bool:
        """清除特定会话"""
        try:
            if hasattr(self.checkpointer, 'delete'):
                self.checkpointer.delete(session_id)
                self.logger.info(f"Cleared session: {session_id}")
                return True
            else:
                self.logger.warning("Checkpointer does not support deleting sessions")
                return False
        except Exception as e:
            self.logger.error(f"Error clearing session {session_id}: {e}")
            return False
    
    def clear_all_sessions(self) -> bool:
        """清除所有会话"""
        try:
            sessions = self.list_sessions()
            for session in sessions:
                self.clear_session(session)
            self.logger.info("Cleared all sessions")
            return True
        except Exception as e:
            self.logger.error(f"Error clearing all sessions: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        try:
            sessions = self.list_sessions()
            return {
                "total_sessions": len(sessions),
                "checkpointer_type": type(self.checkpointer).__name__,
                "sessions": sessions[:10]  # 只返回前10个会话
            }
        except Exception as e:
            self.logger.error(f"Error getting stats: {e}")
            return {
                "total_sessions": 0,
                "checkpointer_type": type(self.checkpointer).__name__,
                "error": str(e)
            }


# 便捷函数
def create_sqlite_checkpointer(db_path: str = "data/checkpoints.db"):
    """创建 SQLite checkpointer"""
    return create_checkpointer(CheckpointerConfig(type="sqlite", database_path=db_path))


def create_postgres_checkpointer(postgres_url: str = "postgresql://localhost/langgraph"):
    """创建 PostgreSQL checkpointer"""
    return create_checkpointer(CheckpointerConfig(type="postgres", postgres_url=postgres_url))


def create_memory_checkpointer():
    """创建内存 checkpointer"""
    return create_checkpointer(CheckpointerConfig(type="memory"))



