"""
Core Workflow V4, removing Agent and tool complexity.

Design philosophy:
1. Maintain clear process design and code structure
2. Use LangGraph native capabilities
3. Direct LLM calls in nodes
4. Use structured output to ensure format consistency
5. Simplified architecture, improved performance and maintainability
"""

import logging
from typing import Literal, Dict, Any, List, Optional
from datetime import datetime
import json

from langgraph.graph import State<PERSON>raph, END
from langgraph.types import Command
from pydantic import SecretStr

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import ChatOpenAI

from .state import SpecificState, WorkflowStatus, create_initial_state
from .checkpointer import get_default_checkpointer, CheckpointerManager
from src.agents import SupervisorAgent, IntentAnalysisAgent, PlanningAgent, ExecutionAgent, SummaryAgent, ReportAgent
from src.utils.logging import SpecificLogger


class SpecificWorkflow:
    """
    Specific Simplified Workflow.

    Simplified design features:
    - Remove Agent and tool complexity
    - Direct LLM calls in nodes
    - Use structured output to ensure format consistency
    - Maintain clear process control and state management
    """

    def __init__(self, checkpointer=None):
        """
        Initialize workflow.

        Args:
            checkpointer: Checkpoint manager, defaults to persistent SQLite checkpointer
        """
        self.logger = SpecificLogger("Workflow")

        # Set checkpoint manager - defaults to persistent SQLite
        self.checkpointer = checkpointer or get_default_checkpointer()

        # Create checkpointer manager
        self.checkpointer_manager = CheckpointerManager(self.checkpointer)

        # Initialize LLM
        self.llm = ChatOpenAI(
                base_url='https://llm-model-proxy.dev.fc.chj.cloud/agentops',
                model='azure-gpt-4o-mini',
                api_key=SecretStr('test'),
                temperature=0.0,
            )

        mini_llm=ChatOpenAI(
            base_url='https://llm-model-proxy.dev.fc.chj.cloud/agentops',
            model='azure-gpt-4o-mini',
            api_key=SecretStr('test')
        )

        # Initialize specialized Agents
        self.supervisor_agent = SupervisorAgent(llm=mini_llm)
        self.intent_agent = IntentAnalysisAgent(llm=self.llm)
        self.planning_agent = PlanningAgent(llm=self.llm)
        self.execution_agent = ExecutionAgent(llm=self.llm)
        self.summary_agent = SummaryAgent(llm=self.llm)
        self.report_agent = ReportAgent(llm=self.llm)

        # Build LangGraph workflow - using native capabilities
        self.graph = self._build_graph()

        self.logger.info("Specific simplified workflow initialized successfully")
        self.logger.info(f"Using checkpointer: {type(self.checkpointer).__name__}")


    def _build_graph(self):
        """
        Build LangGraph workflow - Supervisor pattern.

        Supervisor pattern design:
        - Supervisor handles intelligent routing decisions, analyzes user messages and routes to appropriate Agent
        - Each Agent focuses on business logic, execute method returns Command for autonomous routing
        - Agents can route to other Agents or __end__, no need to force return to Supervisor
        - Based on elegant architecture from hierarchical_agent_teams.ipynb
        """
        # Create state graph - using LangGraph native capabilities
        workflow = StateGraph(SpecificState)

        # Add Supervisor node
        workflow.add_node("supervisor", self._supervisor_node)

        # Add business nodes
        workflow.add_node("intent_clarification", self._intent_clarification_node)
        workflow.add_node("planning", self._planning_node)
        workflow.add_node("execution", self._execution_node)
        workflow.add_node("summary", self._summary_node)
        workflow.add_node("report", self._report_node)

        # Set entry point as Supervisor node
        workflow.set_entry_point("supervisor")

        # Compile graph and add checkpointer
        return workflow.compile(checkpointer=self.checkpointer)


    def get_session_stats(self):
        """Get session statistics"""
        return self.checkpointer_manager.get_stats()

    def list_sessions(self):
        """List all sessions"""
        return self.checkpointer_manager.list_sessions()

    def clear_session(self, session_id: str):
        """Clear specific session"""
        return self.checkpointer_manager.clear_session(session_id)

    def clear_all_sessions(self):
        """Clear all sessions"""
        return self.checkpointer_manager.clear_all_sessions()




    # ==================== Core Node Methods ====================


    def _supervisor_node(self, state: SpecificState, writer) -> Command[Literal["intent_clarification", "planning", "execution", "summary", "report", "__end__"]]:
        """Supervisor node - LLM-based intelligent routing decisions"""
        return self.supervisor_agent.execute(state, writer)


    def _intent_clarification_node(self, state: SpecificState, writer):
        """Intent clarification node"""
        return self.intent_agent.execute(state, writer)

    def _planning_node(self, state: SpecificState, writer):
        """Planning node"""
        return self.planning_agent.execute(state, writer)

    async def _execution_node(self, state: SpecificState, writer):
        """Execution node"""
        return await self.execution_agent.execute(state, writer)

    def _summary_node(self, state: SpecificState, writer):
        """Summary node"""
        return self.summary_agent.execute(state, writer)

    async def _report_node(self, state: SpecificState, writer):
        """Report node"""
        return await self.report_agent.execute(state, writer)


    def get_session_status(self, session_id: str) -> Dict[str, Any]:
        """获取会话状态"""
        try:
            config = {"configurable": {"thread_id": session_id}}
            state = self.graph.get_state(config)

            if state and state.values:
                return {
                    "session_id": session_id,
                    "workflow_status": state.values.get("workflow_status"),
                    "intent_clarified": state.values.get("intent_clarified", False),
                    "intent_approved": state.values.get("intent_approved", False),
                    "plan_approved": state.values.get("plan_approved", False),
                    "execution_started": state.values.get("execution_started", False),
                    "has_task_plan": bool(state.values.get("task_plan")),
                    "message_count": len(state.values.get("messages", []))
                }
            else:
                return {
                    "session_id": session_id,
                    "workflow_status": "not_found",
                    "exists": False
                }
        except Exception as e:
            self.logger.error(f"Error getting session status: {e}")
            return {
                "session_id": session_id,
                "workflow_status": "error",
                "error": str(e)
            }

    # 注意：各个Agent的execute方法返回Command，包含路由信息，节点直接返回Agent的结果

    # 公共接口方法
    async def run(self, user_input: str, session_id: str, user_id: str = None) -> SpecificState:
        """
        运行完整工作流.

        Args:
            user_input: 用户输入
            session_id: 会话ID
            user_id: 用户ID

        Returns:
            最终状态
        """
        self.logger.info(f"Starting workflow for session {session_id}")

        try:
            # 创建初始状态
            initial_state = create_initial_state(
                session_id=session_id,
                user_input=user_input,
                user_id=user_id
            )

            # 创建LangGraph配置 - 直接使用thread_id
            config = {"configurable": {"thread_id": session_id}}
            # config
            # 运行工作流 - 使用LangGraph原生能力
            # final_state = await self.graph.ainvoke(initial_state, config=config)
            final_state =self.graph.invoke(initial_state, config=config)


            self.logger.info(f"Workflow completed for session {session_id}")
            return final_state

        except Exception as e:
            self.logger.error(f"Workflow failed for session {session_id}: {e}")
            raise

    async def resume(self, session_id: str, user_input: str = None) -> SpecificState:
        """
        恢复工作流.

        Args:
            session_id: 会话ID
            user_input: 新的用户输入（可选）

        Returns:
            恢复后的状态
        """
        self.logger.info(f"Resuming workflow for session {session_id}")

        try:
            # 创建配置
            config = {"configurable": {"thread_id": session_id}}

            # 获取当前状态
            current_state = self.graph.get_state(config)

            if user_input:
                # 添加新的用户输入
                current_state.values["user_input"] = user_input

            # 继续执行
            final_state = await self.graph.ainvoke(None, config=config)

            self.logger.info(f"Workflow resumed for session {session_id}")
            return final_state

        except Exception as e:
            self.logger.error(f"Failed to resume workflow for session {session_id}: {e}")
            raise

    def get_stats(self) -> Dict[str, Any]:
        """获取工作流统计信息."""
        return {
            "architecture": "simplified_v4",
            "design": "direct_llm_calls",
            "nodes": ["supervisor", "intent_clarification", "planning", "execution", "summary", "report"],
            "checkpointer": type(self.checkpointer).__name__,
            "graph_nodes": len(self.graph.nodes),
            "llm_model": self.llm.model_name if hasattr(self.llm, 'model_name') else "unknown"
        }


# 为 LangGraph Studio 创建全局图实例
# LangGraph Studio 会自动加载这个 'graph' 变量
_workflow_instance = SpecificWorkflow()
graph = _workflow_instance.graph
