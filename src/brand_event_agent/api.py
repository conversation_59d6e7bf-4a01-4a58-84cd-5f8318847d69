"""
API interface for brand event analysis workflow.

This module provides FastAPI endpoints and data schemas that maintain compatibility
with existing API implementations while using the new workflow.
"""

import uuid
import asyncio
from typing import Dict, Any, Optional, AsyncGenerator, Literal, List
from datetime import datetime
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field

from .workflow import workflow, run_workflow, stream_workflow
from .state import WorkflowState, WorkflowStatus, create_initial_state
from .config import WorkflowConfiguration
from .utils import get_logger

# ==================== Request Schemas ====================

class BrandEventRequest(BaseModel):
    """
    Request schema for brand event analysis.

    This schema maintains compatibility with existing ChatRequest.
    """

    message: str = Field(..., description="User message or query")
    session_id: Optional[str] = Field(None, description="Session identifier")
    user_id: Optional[str] = Field(None, description="User identifier")

    # Workflow control
    workflow_status: Optional[str] = Field(None, description="Current workflow status")

    # Report DSL fields - maintaining compatibility
    report_dsl_data: Optional[Dict[str, Any]] = Field(None, description="Report DSL data structure")
    report_dsl_status: Optional[Literal["SUCCESS", "FAILED"]] = Field(None, description="Report DSL generation status")
    report_dsl_message: Optional[str] = Field(None, description="Report DSL status message")

    # Additional context
    task_id: Optional[str] = Field(None, description="Task identifier")
    sandbox_id: Optional[str] = Field(None, description="Sandbox identifier")
    event_webhook: Optional[str] = Field(None, description="Event webhook URL")
    extensions: Optional[Dict[str, Any]] = Field(None, description="Extension data")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class BrandEventStreamRequest(BaseModel):
    """Request schema for streaming brand event analysis."""

    message: str = Field(..., description="User message or query")
    session_id: Optional[str] = Field(None, description="Session identifier")
    user_id: Optional[str] = Field(None, description="User identifier")

    # Streaming options
    stream_type: Literal["full", "status_only", "messages_only"] = Field(
        default="full",
        description="Type of streaming data to return"
    )

    # Workflow control
    workflow_status: Optional[str] = Field(None, description="Current workflow status")

    # Additional context
    task_id: Optional[str] = Field(None, description="Task identifier")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

# ==================== Response Schemas ====================

class BrandEventResponse(BaseModel):
    """
    Response schema for brand event analysis.

    This schema maintains compatibility with existing ChatResponse.
    """

    session_id: str = Field(..., description="Session identifier")
    response: str = Field(..., description="Response message")
    status: str = Field(..., description="Response status")
    requires_feedback: bool = Field(default=False, description="Whether user feedback is required")

    # Results
    final_report: Optional[str] = Field(None, description="Final analysis report")
    html_report: Optional[str] = Field(None, description="HTML format report")
    summary_data: Optional[Dict[str, Any]] = Field(None, description="Summary data")

    # Workflow state
    workflow_status: Optional[str] = Field(None, description="Current workflow status")
    intent_clarified: Optional[bool] = Field(None, description="Whether intent is clarified")
    plan_approved: Optional[bool] = Field(None, description="Whether plan is approved")
    execution_started: Optional[bool] = Field(None, description="Whether execution has started")

    # Metadata
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

class BrandEventStreamResponse(BaseModel):
    """Response schema for streaming brand event analysis."""

    session_id: str = Field(..., description="Session identifier")
    stream_type: str = Field(..., description="Type of streaming data")
    data: Dict[str, Any] = Field(..., description="Streaming data payload")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")

class BrandEventError(BaseModel):
    """Error response schema."""

    session_id: Optional[str] = Field(None, description="Session identifier")
    error_type: str = Field(..., description="Type of error")
    error_message: str = Field(..., description="Error message")
    error_details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.now, description="Error timestamp")

class WorkflowStatusResponse(BaseModel):
    """Workflow status response schema."""

    session_id: str = Field(..., description="Session identifier")
    workflow_status: str = Field(..., description="Current workflow status")
    current_step: str = Field(..., description="Current workflow step")

    # Progress indicators
    intent_clarified: bool = Field(..., description="Whether intent is clarified")
    intent_approved: bool = Field(..., description="Whether intent is approved")
    plan_approved: bool = Field(..., description="Whether plan is approved")
    execution_started: bool = Field(..., description="Whether execution has started")

    # Results availability
    has_final_report: bool = Field(..., description="Whether final report is available")
    has_html_report: bool = Field(..., description="Whether HTML report is available")

    # Timestamps
    created_at: Optional[datetime] = Field(None, description="Session creation time")
    updated_at: datetime = Field(default_factory=datetime.now, description="Last update time")

# ==================== Utility Functions ====================

def create_error_response(
    error_type: str,
    error_message: str,
    session_id: Optional[str] = None,
    error_details: Optional[Dict[str, Any]] = None
) -> BrandEventError:
    """
    Create a standardized error response.

    Args:
        error_type: Type of error
        error_message: Error message
        session_id: Optional session identifier
        error_details: Optional additional error details

    Returns:
        Standardized error response
    """
    return BrandEventError(
        session_id=session_id,
        error_type=error_type,
        error_message=error_message,
        error_details=error_details
    )

def create_success_response(
    session_id: str,
    response: str,
    status: str = "success",
    requires_feedback: bool = False,
    **kwargs
) -> BrandEventResponse:
    """
    Create a standardized success response.

    Args:
        session_id: Session identifier
        response: Response message
        status: Response status
        requires_feedback: Whether user feedback is required
        **kwargs: Additional response fields

    Returns:
        Standardized success response
    """
    return BrandEventResponse(
        session_id=session_id,
        response=response,
        status=status,
        requires_feedback=requires_feedback,
        **kwargs
    )

def create_stream_response(
    session_id: str,
    stream_type: str,
    data: Dict[str, Any]
) -> BrandEventStreamResponse:
    """
    Create a standardized streaming response.

    Args:
        session_id: Session identifier
        stream_type: Type of streaming data
        data: Streaming data payload

    Returns:
        Standardized streaming response
    """
    return BrandEventStreamResponse(
        session_id=session_id,
        stream_type=stream_type,
        data=data
    )

logger = get_logger("BrandEventAPI")

class BrandEventAPI:
    """
    API interface for brand event analysis workflow.
    
    This class provides FastAPI endpoints that maintain compatibility
    with existing API implementations.
    """
    
    def __init__(self, config: Optional[WorkflowConfiguration] = None):
        """
        Initialize the API interface.
        
        Args:
            config: Configuration for the API and workflow
        """
        self.config = config
        self.app = self._create_app()
        
        # Session storage (in production, use Redis or database)
        self.sessions: Dict[str, Dict[str, Any]] = {}
        
        logger.info("BrandEventAPI initialized")
    
    def _create_app(self) -> FastAPI:
        """
        Create and configure FastAPI application.
        
        Returns:
            Configured FastAPI application
        """
        app = FastAPI(
            title="Brand Event Analysis API",
            description="Multi-agent system for brand sentiment analysis",
            version="1.0.0"
        )
        
        # Add CORS middleware
        app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Add routes
        self._add_routes(app)
        
        return app
    
    def _add_routes(self, app: FastAPI):
        """Add API routes to the FastAPI application."""
        
        @app.post("/chat", response_model=BrandEventResponse)
        async def chat(request: BrandEventRequest) -> BrandEventResponse:
            """
            Main chat endpoint - maintains compatibility with existing API.
            
            Args:
                request: Brand event analysis request
                
            Returns:
                Brand event analysis response
            """
            try:
                # Generate session ID if not provided
                session_id = request.session_id or str(uuid.uuid4())
                
                logger.info(f"Received chat request, session_id: {session_id}")
                
                # Create or update initial state
                state = create_initial_state(
                    session_id=session_id,
                    user_input=request.message,
                    user_id=request.user_id,
                    task_id=request.task_id,
                    sandbox_id=request.sandbox_id,
                    event_webhook=request.event_webhook,
                    extensions=request.extensions,
                    report_dsl_data=request.report_dsl_data,
                    report_dsl_status=request.report_dsl_status,
                    report_dsl_message=request.report_dsl_message,
                    metadata=request.metadata,
                    workflow_status=request.workflow_status
                )
                
                # Run workflow
                final_state = await run_workflow(state, config=self.config.to_dict())
                
                # Store session state
                self.sessions[session_id] = final_state
                
                # Build response
                response = self._build_response(final_state)
                
                logger.info(f"Chat request completed, session_id: {session_id}")
                return response
                
            except Exception as e:
                logger.error(f"Chat request failed: {e}")
                error = create_error_response(
                    error_type="workflow_error",
                    error_message=str(e),
                    session_id=request.session_id
                )
                raise HTTPException(status_code=500, detail=error.dict())
        
        @app.post("/chat/stream")
        async def chat_stream(request: BrandEventStreamRequest):
            """
            Streaming chat endpoint for real-time updates.
            
            Args:
                request: Streaming brand event analysis request
                
            Returns:
                Streaming response with real-time updates
            """
            try:
                # Generate session ID if not provided
                session_id = request.session_id or str(uuid.uuid4())
                
                logger.info(f"Received streaming chat request, session_id: {session_id}")
                
                # Create initial state
                state = create_initial_state(
                    session_id=session_id,
                    user_input=request.message,
                    user_id=request.user_id,
                    task_id=request.task_id,
                    metadata=request.metadata,
                    workflow_status=request.workflow_status
                )
                
                # Create streaming response
                return StreamingResponse(
                    self._stream_workflow(state, request.stream_type),
                    media_type="text/plain"
                )
                
            except Exception as e:
                logger.error(f"Streaming chat request failed: {e}")
                error = create_error_response(
                    error_type="streaming_error",
                    error_message=str(e),
                    session_id=request.session_id
                )
                raise HTTPException(status_code=500, detail=error.dict())
        
        @app.get("/status/{session_id}", response_model=WorkflowStatusResponse)
        async def get_status(session_id: str) -> WorkflowStatusResponse:
            """
            Get workflow status for a session.
            
            Args:
                session_id: Session identifier
                
            Returns:
                Workflow status response
            """
            try:
                if session_id not in self.sessions:
                    raise HTTPException(status_code=404, detail="Session not found")
                
                state = self.sessions[session_id]
                
                return WorkflowStatusResponse(
                    session_id=session_id,
                    workflow_status=state.get("workflow_status", WorkflowStatus.INITIALIZING),
                    current_step=state.get("current_step", "start"),
                    intent_clarified=state.get("intent_clarified", False),
                    intent_approved=state.get("intent_approved", False),
                    plan_approved=state.get("plan_approved", False),
                    execution_started=state.get("execution_started", False),
                    has_final_report=bool(state.get("final_report")),
                    has_html_report=bool(state.get("html_report"))
                )
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"Status request failed: {e}")
                error = create_error_response(
                    error_type="status_error",
                    error_message=str(e),
                    session_id=session_id
                )
                raise HTTPException(status_code=500, detail=error.dict())
        
        @app.delete("/session/{session_id}")
        async def delete_session(session_id: str):
            """
            Delete a session and its data.
            
            Args:
                session_id: Session identifier
                
            Returns:
                Success message
            """
            try:
                if session_id in self.sessions:
                    del self.sessions[session_id]
                    logger.info(f"Session deleted: {session_id}")
                
                return {"message": "Session deleted successfully"}
                
            except Exception as e:
                logger.error(f"Session deletion failed: {e}")
                error = create_error_response(
                    error_type="deletion_error",
                    error_message=str(e),
                    session_id=session_id
                )
                raise HTTPException(status_code=500, detail=error.dict())
        
        @app.get("/health")
        async def health_check():
            """Health check endpoint."""
            return {"status": "healthy", "service": "brand_event_agent"}
    
    def _build_response(self, state: WorkflowState) -> BrandEventResponse:
        """
        Build API response from workflow state.
        
        Args:
            state: Final workflow state
            
        Returns:
            API response
        """
        # Determine response message and status
        workflow_status = state.get("workflow_status", WorkflowStatus.INITIALIZING)
        
        if workflow_status == WorkflowStatus.COMPLETED:
            response_message = state.get("final_report", "分析已完成")
            status = "completed"
            requires_feedback = False
        elif workflow_status == WorkflowStatus.FAILED:
            error_info = state.get("error_info", {})
            response_message = f"分析失败：{error_info.get('message', '未知错误')}"
            status = "failed"
            requires_feedback = False
        elif workflow_status in [WorkflowStatus.CLARIFYING_INTENT, WorkflowStatus.PLANNING]:
            # Get the last AI message
            messages = state.get("messages", [])
            response_message = "请提供更多信息"
            for msg in reversed(messages):
                if hasattr(msg, 'content') and 'AI' in str(type(msg)):
                    response_message = msg.content
                    break
            status = "waiting_feedback"
            requires_feedback = True
        else:
            response_message = "分析正在进行中..."
            status = "processing"
            requires_feedback = False
        
        return create_success_response(
            session_id=state["session_id"],
            response=response_message,
            status=status,
            requires_feedback=requires_feedback,
            final_report=state.get("final_report"),
            html_report=state.get("html_report"),
            summary_data=state.get("summary_data"),
            workflow_status=workflow_status,
            intent_clarified=state.get("intent_clarified"),
            plan_approved=state.get("plan_approved"),
            execution_started=state.get("execution_started"),
            metadata=state.get("metadata")
        )
    
    async def _stream_workflow(self, state: WorkflowState, stream_type: str) -> AsyncGenerator[str, None]:
        """
        Stream workflow execution with real-time updates.
        
        Args:
            state: Initial workflow state
            stream_type: Type of streaming data to return
            
        Yields:
            Streaming response data
        """
        try:
            session_id = state["session_id"]
            
            # Create a queue for streaming data
            stream_queue = asyncio.Queue()
            
            # Writer function to capture streaming data
            def writer(data: Dict[str, Any]):
                try:
                    stream_queue.put_nowait(data)
                except asyncio.QueueFull:
                    logger.warning(f"Stream queue full, dropping data: {data}")
            
            # Start workflow in background
            workflow_task = asyncio.create_task(
                run_workflow(state, config=self.config.to_dict(), writer=writer)
            )
            
            # Stream data as it becomes available
            while not workflow_task.done():
                try:
                    # Wait for data with timeout
                    data = await asyncio.wait_for(stream_queue.get(), timeout=1.0)
                    
                    # Filter data based on stream type
                    if self._should_stream_data(data, stream_type):
                        response = create_stream_response(
                            session_id=session_id,
                            stream_type=stream_type,
                            data=data
                        )
                        yield f"data: {response.json()}\n\n"
                        
                except asyncio.TimeoutError:
                    # Send heartbeat
                    heartbeat = create_stream_response(
                        session_id=session_id,
                        stream_type="heartbeat",
                        data={"status": "alive"}
                    )
                    yield f"data: {heartbeat.json()}\n\n"
            
            # Get final result
            final_state = await workflow_task
            
            # Store session state
            self.sessions[session_id] = final_state
            
            # Send final result
            final_response = create_stream_response(
                session_id=session_id,
                stream_type="final",
                data={"final_state": final_state}
            )
            yield f"data: {final_response.json()}\n\n"
            
        except Exception as e:
            logger.error(f"Streaming workflow failed: {e}")
            error_response = create_stream_response(
                session_id=state["session_id"],
                stream_type="error",
                data={"error": str(e)}
            )
            yield f"data: {error_response.json()}\n\n"
    
    def _should_stream_data(self, data: Dict[str, Any], stream_type: str) -> bool:
        """
        Determine whether to stream specific data based on stream type.
        
        Args:
            data: Data to potentially stream
            stream_type: Type of streaming requested
            
        Returns:
            Whether to stream the data
        """
        if stream_type == "full":
            return True
        elif stream_type == "status_only":
            return "live_status_message" in data
        elif stream_type == "messages_only":
            return "agent_message" in data or "human_feedback_message" in data
        else:
            return False
    
    def get_app(self) -> FastAPI:
        """Get the FastAPI application."""
        return self.app
