# Brand Event Agent

基于open_deep_research风格重构的品牌舆情分析多Agent系统。

## 项目概述

本项目是对原有specific项目的重构，采用open_deep_research的扁平化目录结构和代码风格，同时严格保持原有Agent的执行逻辑、提示词和路由逻辑不变，确保完全兼容性。

## 目录结构

```
src/brand_event_agent/
├── __init__.py                    # 包初始化，版本信息
├── state.py                       # 状态定义（基于原SpecificState）
├── workflow.py                    # 工作流节点和图构建（遵循open_deep_research模式）
├── prompts.py                     # 所有提示词集中管理
├── config.py                      # 配置管理（支持RunnableConfig）
├── utils.py                       # 工具类、辅助函数和工具集合
├── api.py                         # API接口实现和数据模式定义
├── test_workflow.py               # 测试脚本
└── README.md                      # 项目说明
```

## 核心特性

### 1. 完全兼容性保证

- **提示词完全一致**: 所有提示词直接从现有Agent中提取，无任何修改
- **执行逻辑完全一致**: 节点实现完全移植原Agent的execute方法逻辑
- **路由逻辑完全一致**: 保持原有的状态转换和路由决策
- **数据结构完全一致**: 状态定义基于原SpecificState，确保字段兼容

### 2. 扁平化架构（遵循open_deep_research模式）

- 采用open_deep_research的扁平化目录结构
- 无子目录，所有模块在同一层级
- 提示词集中在单个`prompts.py`文件中
- 工作流节点和图构建集中在`workflow.py`文件中
- 工具和utils合并在`utils.py`文件中
- API和数据模式合并在`api.py`文件中
- 配置支持`RunnableConfig`模式

### 3. 工作流节点

#### Supervisor Node (supervisor_node)
- **功能**: 智能路由协调
- **逻辑**: 完全移植SupervisorAgent.execute
- **路由规则**: 基于workflow_status和消息类型进行路由

#### Intent Clarification Node (intent_clarification_node)
- **功能**: 用户意图分析和澄清
- **逻辑**: 完全移植IntentAnalysisAgent.execute
- **处理**: 多轮澄清、意图分析、需求确认

#### Planning Node (planning_node)
- **功能**: 执行计划制定
- **逻辑**: 完全移植PlanningAgent.execute
- **特性**: 计划生成、反馈分析、计划调整

#### Execution Node (execution_node)
- **功能**: 任务执行
- **逻辑**: 完全移植ExecutionAgent.execute
- **能力**: 步骤执行、MCP工具调用、进度报告

#### Summary Node (summary_node)
- **功能**: 结果总结
- **逻辑**: 完全移植SummaryAgent.execute
- **输出**: 执行总结、关键发现、质量评估

#### Report Node (report_node)
- **功能**: 报告生成
- **逻辑**: 完全移植ReportAgent.execute
- **服务**: HTML报告生成、S3上传、DSL解析

### 4. 状态管理

```python
class BrandEventState(TypedDict):
    # 核心工作流状态
    messages: Annotated[List[BaseMessage], add_messages]
    session_id: str
    user_id: Optional[str]
    user_input: str
    
    # 工作流控制
    workflow_status: WorkflowStatus
    current_step: str
    requires_human_approval: bool
    
    # 意图分析
    intent_clarified: bool
    intent_approved: bool
    clarification_result: Optional[Dict[str, Any]]
    
    # 规划
    task_plan: Optional[Dict[str, Any]]
    plan_approved: bool
    
    # 执行
    execution_started: bool
    execution_results: List[Dict[str, Any]]
    
    # 报告
    final_report: Optional[str]
    html_report: Optional[str]
    report_dsl_data: Optional[Dict[str, Any]]
    # ... 其他字段
```

### 5. 配置管理

```python
@dataclass
class BrandEventConfig:
    # 模型配置
    supervisor_model: str = "gpt-4o-mini"
    analyst_model: str = "gpt-4o-mini"
    planner_model: str = "gpt-4o-mini"
    executor_model: str = "gpt-4o-mini"
    summarizer_model: str = "gpt-4o-mini"
    reporter_model: str = "gpt-4o-mini"
    
    # 服务配置
    report_service_url: str = "https://console-playground.fed.chehejia.com"
    upload_service_url: str = "https://xuanji-backend-service-dev.inner.chj.cloud/api/v1/ois/upload"
    
    # 工作流配置
    max_iterations: int = 10
    timeout_seconds: int = 300
    # ... 其他配置
```

## 使用方法

### 1. 基本使用

```python
from brand_event_agent import workflow, run_workflow, create_initial_state

# 创建初始状态
state = create_initial_state(
    session_id="test_session",
    user_input="请帮我分析理想汽车最近的舆情情况",
    user_id="test_user"
)

# 运行工作流
final_state = await run_workflow(state)

# 或者直接使用workflow对象
final_state = await workflow.ainvoke(state)
```

### 2. 带流式输出

```python
# 定义输出处理函数
def writer(data):
    if "agent_message" in data:
        print(f"Agent: {data['agent_message']}")
    elif "live_status_message" in data:
        print(f"Status: {data['live_status_message']}")

# 运行带流式输出的工作流
final_state = await workflow.arun(state, writer=writer)
```

### 3. API使用

```python
from brand_event_agent import BrandEventAPI

# 创建API实例
api = BrandEventAPI()

# 获取FastAPI应用
app = api.get_app()

# 运行服务器
import uvicorn
uvicorn.run(app, host="0.0.0.0", port=8000)
```

### 4. API端点

- `POST /chat` - 主要聊天接口
- `POST /chat/stream` - 流式聊天接口
- `GET /status/{session_id}` - 获取会话状态
- `DELETE /session/{session_id}` - 删除会话
- `GET /health` - 健康检查

## 测试

运行测试脚本验证功能：

```python
from brand_event_agent.test_workflow import TestBrandEventWorkflow
import asyncio

async def run_tests():
    test_suite = TestBrandEventWorkflow()
    await test_suite.run_all_tests()

asyncio.run(run_tests())
```

## 兼容性说明

### 与原有系统的兼容性

1. **API兼容性**: 保持与原有ChatRequest/ChatResponse的兼容
2. **状态兼容性**: BrandEventState与SpecificState完全兼容
3. **配置兼容性**: 支持原有的配置参数和环境变量
4. **功能兼容性**: 所有原有功能都得到保留

### 迁移指南

1. **导入更新**: 
   ```python
   # 原有
   from src.core.workflow import SpecificWorkflow
   
   # 新版
   from src.brand_event_agent import BrandEventWorkflow
   ```

2. **状态创建**:
   ```python
   # 原有
   state = create_initial_state(...)
   
   # 新版（相同）
   state = create_initial_state(...)
   ```

3. **工作流运行**:
   ```python
   # 原有
   result = await workflow.run(state, writer)
   
   # 新版
   result = await workflow.arun(state, writer)
   ```

## 技术栈

- **LangGraph**: 工作流编排
- **LangChain**: LLM集成
- **FastAPI**: API框架
- **Pydantic**: 数据验证
- **OpenAI**: LLM服务
- **asyncio**: 异步处理

## 开发说明

### 代码风格

- 遵循open_deep_research的代码风格
- 使用类型注解
- 详细的文档字符串
- 统一的错误处理

### 扩展指南

1. **添加新节点**: 在nodes.py中实现新的节点函数
2. **修改提示词**: 在prompts.py中更新相应提示词
3. **添加工具**: 在tools.py中实现新的工具函数
4. **更新配置**: 在config.py中添加新的配置项

## 注意事项

1. **保持兼容性**: 任何修改都必须保持与原有系统的兼容性
2. **测试验证**: 所有修改都需要通过测试验证
3. **文档更新**: 重要修改需要更新相应文档
4. **性能考虑**: 注意异步处理和资源管理

## 版本信息

- **版本**: 1.0.0
- **基于**: open_deep_research workflow模式
- **兼容**: specific项目原有功能
- **Python**: 3.8+
