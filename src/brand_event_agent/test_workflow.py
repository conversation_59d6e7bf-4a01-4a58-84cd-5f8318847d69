"""
Test script for brand event analysis workflow.

This script provides basic tests to verify that the refactored workflow
maintains compatibility with existing Agent implementations.
"""

import asyncio
import uuid
from typing import Dict, Any

from .workflow import workflow, run_workflow, stream_workflow
from .state import create_initial_state, WorkflowStatus
from .config import WorkflowConfiguration
from .api import BrandEventAPI, BrandEventRequest
from .utils import get_logger

logger = get_logger("BrandEventTest")

class TestBrandEventWorkflow:
    """Test class for brand event analysis workflow."""
    
    def __init__(self):
        """Initialize test environment."""
        self.config = WorkflowConfiguration()
        self.api = BrandEventAPI(self.config)
        
    def test_state_creation(self):
        """Test state creation functionality."""
        logger.info("Testing state creation...")
        
        session_id = str(uuid.uuid4())
        user_input = "请帮我分析理想汽车最近的舆情情况"
        
        state = create_initial_state(
            session_id=session_id,
            user_input=user_input,
            user_id="test_user"
        )
        
        # Verify state structure
        assert state["session_id"] == session_id
        assert state["user_input"] == user_input
        assert state["workflow_status"] == WorkflowStatus.INITIALIZING
        assert state["intent_clarified"] == False
        assert state["plan_approved"] == False
        assert state["execution_started"] == False
        
        logger.info("✅ State creation test passed")
        
    def test_config_loading(self):
        """Test configuration loading."""
        logger.info("Testing configuration loading...")
        
        # Test default config
        config = WorkflowConfiguration()
        assert config.supervisor_model == "gpt-4o-mini"
        assert config.report_service_url == "https://console-playground.fed.chehejia.com"
        assert config.max_iterations == 10
        
        # Test config serialization
        config_dict = config.to_dict()
        assert isinstance(config_dict, dict)
        assert "supervisor_model" in config_dict
        
        # Test config from dict
        new_config = WorkflowConfiguration.from_dict(config_dict)
        assert new_config.supervisor_model == config.supervisor_model
        
        logger.info("✅ Configuration loading test passed")
        
    async def test_workflow_basic(self):
        """Test basic workflow execution."""
        logger.info("Testing basic workflow execution...")
        
        session_id = str(uuid.uuid4())
        user_input = "请帮我分析理想汽车最近的舆情情况"
        
        # Create initial state
        state = create_initial_state(
            session_id=session_id,
            user_input=user_input,
            user_id="test_user"
        )
        
        # Create a simple writer for testing
        messages = []
        def test_writer(data: Dict[str, Any]):
            messages.append(data)
            logger.info(f"Writer received: {data}")
        
        try:
            # Run workflow
            final_state = await run_workflow(state, config=self.config.to_dict(), writer=test_writer)
            
            # Verify final state
            assert final_state["session_id"] == session_id
            assert "workflow_status" in final_state
            
            # Verify writer was called
            assert len(messages) > 0
            
            logger.info(f"✅ Basic workflow test passed. Final status: {final_state.get('workflow_status')}")
            logger.info(f"📝 Writer received {len(messages)} messages")
            
            return final_state
            
        except Exception as e:
            logger.error(f"❌ Basic workflow test failed: {e}")
            raise
    
    async def test_api_endpoint(self):
        """Test API endpoint functionality."""
        logger.info("Testing API endpoint...")
        
        # Create test request
        request = BrandEventRequest(
            message="请帮我分析理想汽车最近的舆情情况",
            session_id=str(uuid.uuid4()),
            user_id="test_user"
        )
        
        try:
            # Get FastAPI app
            app = self.api.get_app()
            
            # Test that app was created
            assert app is not None
            assert hasattr(app, 'routes')
            
            # Verify routes exist
            route_paths = [route.path for route in app.routes]
            expected_paths = ["/chat", "/chat/stream", "/health"]
            
            for path in expected_paths:
                assert any(path in route_path for route_path in route_paths), f"Missing route: {path}"
            
            logger.info("✅ API endpoint test passed")
            
        except Exception as e:
            logger.error(f"❌ API endpoint test failed: {e}")
            raise
    
    async def test_prompt_functions(self):
        """Test prompt building functions."""
        logger.info("Testing prompt functions...")

        try:
            from .prompts import (
                build_supervisor_routing_prompt,
                build_intent_analysis_prompt,
                build_clarification_prompt,
                build_planning_prompt,
                build_planning_intent_analysis_prompt,
                build_summary_prompt
            )
            
            # Test supervisor prompt
            test_state = {
                "workflow_status": "initializing",
                "messages": [],
                "user_input": "test input"
            }
            supervisor_prompt = build_supervisor_routing_prompt(test_state)
            assert isinstance(supervisor_prompt, str)
            assert len(supervisor_prompt) > 0
            
            # Test intent analysis prompt
            intent_prompt = build_intent_analysis_prompt("test_stage", "test_response")
            assert isinstance(intent_prompt, str)
            assert "test_stage" in intent_prompt
            assert "test_response" in intent_prompt
            
            # Test clarification prompt
            clarification_prompt = build_clarification_prompt("test_input")
            assert isinstance(clarification_prompt, str)
            assert "test_input" in clarification_prompt
            
            # Test planning prompt
            planning_prompt = build_planning_prompt("test_requirements")
            assert isinstance(planning_prompt, str)
            assert "test_requirements" in planning_prompt
            
            # Test planning intent analysis prompt
            planning_intent_prompt = build_planning_intent_analysis_prompt("test_feedback")
            assert isinstance(planning_intent_prompt, str)
            assert "test_feedback" in planning_intent_prompt
            
            # Test summary prompt
            summary_prompt = build_summary_prompt()
            assert isinstance(summary_prompt, str)
            assert len(summary_prompt) > 0
            
            logger.info("✅ Prompt functions test passed")
            
        except Exception as e:
            logger.error(f"❌ Prompt functions test failed: {e}")
            raise
    
    def test_tools_functions(self):
        """Test tool functions."""
        logger.info("Testing tool functions...")
        
        try:
            from .utils import create_access_token, parse_dsl_summary, create_brand_analysis_dsl
            
            # Test access token creation
            token = create_access_token("test_user")
            assert isinstance(token, str)
            assert len(token) > 0
            
            # Test DSL creation
            dsl = create_brand_analysis_dsl()
            assert isinstance(dsl, dict)
            assert "title" in dsl
            assert "sections" in dsl
            
            # Test DSL parsing
            summary = parse_dsl_summary(dsl)
            assert isinstance(summary, dict)
            assert "ai_summary" in summary
            assert "key_metrics" in summary
            
            logger.info("✅ Tool functions test passed")
            
        except Exception as e:
            logger.error(f"❌ Tool functions test failed: {e}")
            raise
    
    async def run_all_tests(self):
        """Run all tests."""
        logger.info("🚀 Starting brand event workflow tests...")
        
        try:
            # Run synchronous tests
            self.test_state_creation()
            self.test_config_loading()
            self.test_prompt_functions()
            self.test_tools_functions()
            
            # Run asynchronous tests
            await self.test_workflow_basic()
            await self.test_api_endpoint()
            
            logger.info("🎉 All tests passed successfully!")
            
        except Exception as e:
            logger.error(f"💥 Test suite failed: {e}")
            raise

# ==================== Test Runner ====================

async def main():
    """Main test runner."""
    test_suite = TestBrandEventWorkflow()
    await test_suite.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
