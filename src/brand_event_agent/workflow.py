"""
Main workflow for brand event analysis.

This module provides the complete workflow implementation following
open_deep_research patterns while maintaining compatibility with
existing Agent implementations.
"""

from typing import Dict, Any, Optional
from langgraph.graph import StateGraph, END
from langgraph.types import Command

from .state import BrandEventState, WorkflowStatus
from .nodes import (
    supervisor_node,
    intent_clarification_node,
    planning_node,
    execution_node,
    summary_node,
    report_node
)
from .config import BrandEventConfig, get_default_config
from .utils import get_logger

logger = get_logger("BrandEventWorkflow")

class BrandEventWorkflow:
    """
    Multi-agent workflow for brand event analysis.
    
    This workflow follows the open_deep_research pattern with supervisor coordination
    and specialized agent roles, while maintaining complete compatibility with
    existing Agent implementations.
    """
    
    def __init__(self, config: Optional[BrandEventConfig] = None):
        """
        Initialize the brand event analysis workflow.
        
        Args:
            config: Configuration for the workflow
        """
        self.config = config or get_default_config()
        self.graph = self._build_graph()
        
        logger.info("BrandEventWorkflow initialized")
    
    def _build_graph(self) -> StateGraph:
        """
        Build the workflow graph with all nodes and edges.
        
        Returns:
            Compiled workflow graph
        """
        # Create workflow graph
        workflow = StateGraph(BrandEventState)
        
        # Add all nodes
        workflow.add_node("supervisor", self._supervisor_wrapper)
        workflow.add_node("intent_clarification", self._intent_clarification_wrapper)
        workflow.add_node("planning", self._planning_wrapper)
        workflow.add_node("execution", self._execution_wrapper)
        workflow.add_node("summary", self._summary_wrapper)
        workflow.add_node("report", self._report_wrapper)
        
        # Set entry point
        workflow.set_entry_point("supervisor")
        
        # Add conditional edges from supervisor
        workflow.add_conditional_edges(
            "supervisor",
            self._supervisor_router,
            {
                "intent_clarification": "intent_clarification",
                "planning": "planning", 
                "execution": "execution",
                "summary": "summary",
                "report": "report",
                "__end__": END
            }
        )
        
        # Add edges from other nodes back to supervisor or END
        workflow.add_conditional_edges(
            "intent_clarification",
            self._intent_clarification_router,
            {
                "planning": "planning",
                "supervisor": "supervisor",
                "__end__": END
            }
        )
        
        workflow.add_conditional_edges(
            "planning",
            self._planning_router,
            {
                "execution": "execution",
                "supervisor": "supervisor",
                "__end__": END
            }
        )
        
        workflow.add_conditional_edges(
            "execution",
            self._execution_router,
            {
                "summary": "summary",
                "__end__": END
            }
        )
        
        workflow.add_conditional_edges(
            "summary",
            self._summary_router,
            {
                "report": "report",
                "__end__": END
            }
        )
        
        workflow.add_edge("report", END)
        
        # Compile the graph
        return workflow.compile()
    
    # ==================== Node Wrappers ====================
    
    async def _supervisor_wrapper(self, state: BrandEventState) -> Command:
        """Wrapper for supervisor node with config injection."""
        config_dict = self.config.to_dict()
        return await supervisor_node(state, config_dict)

    async def _intent_clarification_wrapper(self, state: BrandEventState) -> Command:
        """Wrapper for intent clarification node with config injection."""
        config_dict = self.config.to_dict()
        return await intent_clarification_node(state, config_dict)

    async def _planning_wrapper(self, state: BrandEventState) -> Command:
        """Wrapper for planning node with config injection."""
        config_dict = self.config.to_dict()
        return await planning_node(state, config_dict)

    async def _execution_wrapper(self, state: BrandEventState) -> Command:
        """Wrapper for execution node with config injection."""
        config_dict = self.config.to_dict()
        return await execution_node(state, config_dict)

    async def _summary_wrapper(self, state: BrandEventState) -> Command:
        """Wrapper for summary node with config injection."""
        config_dict = self.config.to_dict()
        return await summary_node(state, config_dict)

    async def _report_wrapper(self, state: BrandEventState) -> Command:
        """Wrapper for report node with config injection."""
        config_dict = self.config.to_dict()
        return await report_node(state, config_dict)
    
    # ==================== Router Functions ====================
    
    def _supervisor_router(self, state: BrandEventState) -> str:
        """Route from supervisor based on the Command result."""
        # The supervisor node returns a Command with goto field
        # This router just needs to return the goto value
        # Since LangGraph handles Command objects automatically,
        # we don't need to implement this router
        pass
    
    def _intent_clarification_router(self, state: BrandEventState) -> str:
        """Route from intent clarification based on state."""
        workflow_status = state.get("workflow_status", WorkflowStatus.INITIALIZING)
        
        if workflow_status == WorkflowStatus.PLANNING:
            return "planning"
        elif workflow_status == WorkflowStatus.CLARIFYING_INTENT:
            return "__end__"
        else:
            return "supervisor"
    
    def _planning_router(self, state: BrandEventState) -> str:
        """Route from planning based on state."""
        workflow_status = state.get("workflow_status", WorkflowStatus.INITIALIZING)
        
        if workflow_status == WorkflowStatus.EXECUTING:
            return "execution"
        elif workflow_status == WorkflowStatus.PLANNING:
            return "__end__"
        else:
            return "supervisor"
    
    def _execution_router(self, state: BrandEventState) -> str:
        """Route from execution based on state."""
        workflow_status = state.get("workflow_status", WorkflowStatus.INITIALIZING)
        
        if workflow_status == WorkflowStatus.SUMMARIZING:
            return "summary"
        else:
            return "__end__"
    
    def _summary_router(self, state: BrandEventState) -> str:
        """Route from summary based on state."""
        workflow_status = state.get("workflow_status", WorkflowStatus.INITIALIZING)
        
        if workflow_status == WorkflowStatus.REPORT:
            return "report"
        else:
            return "__end__"
    
    # ==================== Public Methods ====================
    
    async def arun(self, state: BrandEventState, writer=None) -> BrandEventState:
        """
        Run the workflow asynchronously.
        
        Args:
            state: Initial workflow state
            writer: Optional writer function for streaming updates
            
        Returns:
            Final workflow state
        """
        try:
            logger.info(f"Starting workflow execution, session_id: {state.get('session_id', 'unknown')}")
            
            # Inject writer into config if provided
            if writer:
                config_dict = self.config.to_dict()
                config_dict['writer'] = writer
                
                # Update all wrapper methods to use the writer
                self._update_config_with_writer(config_dict)
            
            # Run the workflow
            final_state = await self.graph.ainvoke(state)
            
            logger.info(f"Workflow execution completed, session_id: {state.get('session_id', 'unknown')}")
            return final_state
            
        except Exception as e:
            logger.error(f"Workflow execution failed: {e}, session_id: {state.get('session_id', 'unknown')}")
            
            # Return error state
            error_state = state.copy()
            error_state.update({
                "workflow_status": WorkflowStatus.FAILED,
                "error_info": {"message": str(e), "source": "workflow"}
            })
            return error_state
    
    def run(self, state: BrandEventState, writer=None) -> BrandEventState:
        """
        Run the workflow synchronously.
        
        Args:
            state: Initial workflow state
            writer: Optional writer function for streaming updates
            
        Returns:
            Final workflow state
        """
        try:
            logger.info(f"Starting synchronous workflow execution, session_id: {state.get('session_id', 'unknown')}")
            
            # Inject writer into config if provided
            if writer:
                config_dict = self.config.to_dict()
                config_dict['writer'] = writer
                
                # Update all wrapper methods to use the writer
                self._update_config_with_writer(config_dict)
            
            # Run the workflow
            final_state = self.graph.invoke(state)
            
            logger.info(f"Synchronous workflow execution completed, session_id: {state.get('session_id', 'unknown')}")
            return final_state
            
        except Exception as e:
            logger.error(f"Synchronous workflow execution failed: {e}, session_id: {state.get('session_id', 'unknown')}")
            
            # Return error state
            error_state = state.copy()
            error_state.update({
                "workflow_status": WorkflowStatus.FAILED,
                "error_info": {"message": str(e), "source": "workflow"}
            })
            return error_state
    
    def _update_config_with_writer(self, config_dict: Dict[str, Any]):
        """Update configuration with writer for all node wrappers."""
        # This is a simple approach - in a more sophisticated implementation,
        # you might want to use dependency injection or a context manager
        self._current_config = config_dict
    
    def get_config(self) -> BrandEventConfig:
        """Get the current configuration."""
        return self.config
    
    def update_config(self, new_config: BrandEventConfig):
        """Update the workflow configuration."""
        self.config = new_config
        logger.info("Workflow configuration updated")
