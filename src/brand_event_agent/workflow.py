"""
Brand event analysis workflow implementation.

This module provides workflow nodes and graph construction following
open_deep_research patterns while maintaining compatibility with
existing Agent implementations.
"""

from typing import Dict, Any, Optional, Literal, List
from langgraph.graph import StateGraph, START, END
from langgraph.types import Command
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import Chat<PERSON>penA<PERSON>
from pydantic import BaseModel, Field

from .state import BrandEventState, WorkflowStatus
from .config import BrandEventConfig
from .prompts import (
    SUPERVISOR_SYSTEM_PROMPT, build_supervisor_routing_prompt,
    INTENT_ANALYSIS_SYSTEM_PROMPT, build_intent_analysis_prompt, build_clarification_prompt,
    PLANNING_SYSTEM_PROMPT, build_planning_prompt, build_planning_intent_analysis_prompt,
    EXECUTION_SYSTEM_PROMPT,
    SUMMARY_SYSTEM_PROMPT, build_summary_prompt,
    REPORTING_SYSTEM_PROMPT
)
from .utils import (
    get_logger, get_latest_user_response, get_latest_plan_feedback, get_clarified_requirements,
    format_plan_message, create_access_token, generate_report, upload_html_to_s3,
    parse_dsl_summary, create_brand_analysis_dsl
)

logger = get_logger("BrandEventWorkflow")

# ==================== Data Models ====================
# Extracted from existing Agent implementations

class UserMessageAnalysis(BaseModel):
    """用户消息分析结果"""
    message_type: Literal["greeting", "task", "supplement", "agreement", "rejection", "detailed_description", "short_reply"] = Field(
        description="消息类型：greeting(问候), task(任务请求), supplement(补充信息), agreement(确认同意), rejection(拒绝否定), detailed_description(详细描述), short_reply(简短回复)"
    )
    user_intent: str = Field(description="用户真实意图分析")
    extracted_info: str = Field(default="", description="从用户消息中提取的关键信息")

class RouterDecisionWithAnalysis(BaseModel):
    """路由决策结果（包含分析）"""
    message_analysis: UserMessageAnalysis = Field(description="用户消息分析结果")
    next: Literal["intent_clarification", "planning", "execution", "summary", "report", "__end__"] = Field(
        description="下一步路由目标"
    )
    reason: str = Field(description="路由决策理由")
    response_message: str = Field(default="", description="需要返回给用户的消息（仅问候时填写）")
    workflow_status: str = Field(description="更新后的工作流状态")

class UserIntentAnalysis(BaseModel):
    """用户意图分析结果"""
    intent_type: Literal["agreement", "supplement", "rejection"] = Field(description="意图类型")
    next_action: Literal["continue", "retry"] = Field(description="下一步行动")
    extracted_info: str = Field(default="", description="提取的补充信息")

class IntentClarificationResult(BaseModel):
    """意图澄清结果"""
    intent_clear: bool = Field(description="意图是否清晰")
    clarification_questions: List[str] = Field(default=[], description="澄清问题列表")
    clarification_result: Optional[Dict[str, Any]] = Field(default=None, description="澄清结果")
    summary: str = Field(description="分析总结")
    response_message: str = Field(description="回复消息")

class PlanStep(BaseModel):
    """计划步骤"""
    title: str = Field(description="步骤标题")
    description: str = Field(description="步骤描述")
    estimated_time: str = Field(default="", description="预估时间")
    skip_execute: bool = Field(default=False, description="是否跳过执行")

class ExecutionPlan(BaseModel):
    """执行计划"""
    title: str = Field(description="计划标题")
    objective: str = Field(description="计划目标")
    steps: List[PlanStep] = Field(description="执行步骤")
    estimated_time: str = Field(default="", description="总预估时间")

# ==================== Workflow Nodes ====================

async def supervisor_node(state: BrandEventState, config: RunnableConfig) -> Command:
    """
    Supervisor node - 完全移植SupervisorAgent.execute逻辑
    """
    configurable = BrandEventConfig.from_runnable_config(config)
    session_id = state.get('session_id', 'unknown')
    logger.info(f"Start doing workflow supervision, session_id:{session_id}")

    try:
        # 发送监督开始的消息
        writer = getattr(configurable, 'writer', None)
        if writer:
            writer({"live_status_message": "正在分析当前状态和用户消息..."})

        # 使用LLM进行智能路由决策
        result = await _llm_based_routing(state, session_id, writer, configurable)
        route_reason = result.update.get('reason', 'N/A')

        logger.info(f"Completed workflow supervision, result: route to {result.goto}, reason: {route_reason}, session_id:{session_id}")
        return result

    except Exception as e:
        logger.error(f"Failed workflow supervision, error: {str(e)}, session_id:{session_id}")

        # 发送错误分析消息
        writer = getattr(configurable, 'writer', None)
        if writer:
            writer({"live_status_message": "路由分析遇到问题，使用默认策略"})

        # 出错时默认路由到意图澄清
        return Command(goto="intent_clarification", update={"workflow_status": WorkflowStatus.CLARIFYING_INTENT})

async def _llm_based_routing(state: BrandEventState, session_id: str, writer, configurable: BrandEventConfig) -> Command:
    """
    基于LLM的智能路由决策 - 完全移植SupervisorAgent._llm_based_routing逻辑
    """
    try:
        # 构建路由决策提示词
        routing_prompt = build_supervisor_routing_prompt(state)

        # 发送LLM分析的消息
        if writer:
            writer({"live_status_message": "正在分析用户消息类型和意图..."})

        # 初始化LLM
        llm = ChatOpenAI(
            model=configurable.supervisor_model,
            temperature=0
        )

        # 使用结构化输出获取路由决策
        messages = [HumanMessage(content=routing_prompt)]
        response = llm.with_structured_output(RouterDecisionWithAnalysis).invoke(messages)

        # 记录分析结果
        logger.info(f"User message analysis: type={response.message_analysis.message_type}, session_id:{session_id}")
        logger.info(f"LLM routing decision: {response.next}, reason: {response.reason}, session_id:{session_id}")

        # 直接根据LLM返回的结构化结果构建Command
        update_data = {
            "reason": response.reason,
            "workflow_status": response.workflow_status
        }

        # 如果是问候消息，添加回复消息
        if response.message_analysis.message_type == "greeting" and response.next=='__end__' and response.response_message:
            if writer:
                writer({"agent_message": response.response_message})
                writer({"human_feedback_message": "请输入具体任务需求，我将为您提供帮助。"})

        return Command(goto=response.next, update=update_data)

    except Exception as e:
        logger.error(f"Failed LLM-based routing, error: {str(e)}, session_id:{session_id}")

        # 发送错误分析消息
        if writer:
            writer({"live_status_message": "路由分析遇到问题，使用默认策略"})

        # 出错时默认路由到意图澄清
        return Command(goto="intent_clarification", update={"workflow_status": WorkflowStatus.CLARIFYING_INTENT})

async def intent_clarification_node(state: BrandEventState, config: RunnableConfig) -> Command:
    """
    Intent clarification node - 完全移植IntentAnalysisAgent.execute逻辑
    """
    configurable = BrandEventConfig.from_runnable_config(config)
    session_id = state.get('session_id', 'unknown')
    clarification_round = state.get("clarification_round", 0)
    logger.info(f"Start doing intent clarification (round {clarification_round + 1}), session_id:{session_id}")

    try:
        writer = getattr(configurable, 'writer', None)

        # 发送任务开始的Agent消息
        if clarification_round == 0 and writer:
            writer({"agent_message": "正在分析您的需求，确保完全理解您的需求。"})

        # 发送实时状态消息
        if writer:
            writer({"live_status_message": "正在分析用户意图..."})

        # 获取基本信息
        latest_user_response = get_latest_user_response(state)
        context_messages = state.get("messages", [])

        # 初始化LLM
        llm = ChatOpenAI(
            model=configurable.analyst_model,
            temperature=0
        )

        # 如果是多轮澄清，先分析用户意图
        if clarification_round > 0 and latest_user_response:
            # 发送分析意图的状态消息
            if writer:
                writer({"live_status_message": "正在分析您的回复意图..."})

            intent_analysis = await _analyze_user_intent(
                llm=llm,
                user_response=latest_user_response,
                current_stage="意图澄清阶段",
                context_messages=context_messages
            )

            # 根据意图分析结果处理
            if intent_analysis.intent_type == "agreement":
                # 发送用户确认的消息
                if writer:
                    writer({"agent_message": "意图已确认，将为您制定详细的执行计划"})

                logger.info(f"Completed intent clarification, result: user approved requirements, session_id:{session_id}")
                return Command(
                    goto="planning",
                    update={
                        "intent_clarified": True,
                        "intent_approved": True,  # 用户确认审批通过
                        "workflow_status": WorkflowStatus.PLANNING,  # 审批通过，进入计划阶段
                        "messages": state["messages"] + [HumanMessage(content="感谢您的确认！我现在开始为您制定执行计划。")]
                    }
                )

            elif intent_analysis.intent_type == "supplement":
                # 发送补充信息的消息
                if writer:
                    writer({"agent_message": "我理解您提供的补充信息，让我重新分析您的需求。"})

                logger.info(f"User provided supplement info, re-analyzing intent, session_id:{session_id}")
                # 继续进行澄清分析

        # 发送澄清分析的状态消息
        if writer:
            writer({"live_status_message": "正在分析需求清晰度..."})

        # 判断用户意图是否清晰
        user_input = state.get("user_input", "")
        clarification_result = await _is_intent_clear(
            llm=llm,
            user_input=user_input,
            context_messages=context_messages
        )

        if clarification_result.intent_clear:
            # 意图清晰，直接进入计划阶段
            if writer:
                writer({"agent_message": clarification_result.response_message})
                writer({"agent_message": "需求已明确，将为您制定详细的执行计划"})

            logger.info(f"Completed intent clarification, result: intent is clear, session_id:{session_id}")
            return Command(
                goto="planning",
                update={
                    "intent_clarified": True,
                    "intent_approved": True,
                    "clarification_result": clarification_result.clarification_result,
                    "workflow_status": WorkflowStatus.PLANNING,
                    "messages": state["messages"] + [
                        HumanMessage(content=clarification_result.response_message),
                        HumanMessage(content="需求已明确，开始制定执行计划。")
                    ],
                    "clarification_round": clarification_round + 1
                }
            )
        else:
            # 需要澄清，发送澄清问题
            if writer:
                writer({"agent_message": clarification_result.response_message})
                writer({"human_feedback_message": "请提供更多详细信息，以便我为您制定最佳的分析方案。"})

            logger.info(f"Completed intent clarification, result: need clarification, session_id:{session_id}")
            return Command(
                goto="__end__",
                update={
                    "intent_clarified": False,
                    "intent_approved": False,
                    "workflow_status": WorkflowStatus.CLARIFYING_INTENT,
                    "messages": state["messages"] + [HumanMessage(content=clarification_result.response_message)],
                    "clarification_round": clarification_round + 1
                }
            )

    except Exception as e:
        logger.error(f"Failed intent clarification, error: {str(e)}, session_id:{session_id}")
        return Command(goto="__end__", update={"workflow_status": WorkflowStatus.FAILED})

class BrandEventWorkflow:
    """
    Multi-agent workflow for brand event analysis.
    
    This workflow follows the open_deep_research pattern with supervisor coordination
    and specialized agent roles, while maintaining complete compatibility with
    existing Agent implementations.
    """
    
    def __init__(self, config: Optional[BrandEventConfig] = None):
        """
        Initialize the brand event analysis workflow.
        
        Args:
            config: Configuration for the workflow
        """
        self.config = config or get_default_config()
        self.graph = self._build_graph()
        
        logger.info("BrandEventWorkflow initialized")
    
    def _build_graph(self) -> StateGraph:
        """
        Build the workflow graph with all nodes and edges.
        
        Returns:
            Compiled workflow graph
        """
        # Create workflow graph
        workflow = StateGraph(BrandEventState)
        
        # Add all nodes
        workflow.add_node("supervisor", self._supervisor_wrapper)
        workflow.add_node("intent_clarification", self._intent_clarification_wrapper)
        workflow.add_node("planning", self._planning_wrapper)
        workflow.add_node("execution", self._execution_wrapper)
        workflow.add_node("summary", self._summary_wrapper)
        workflow.add_node("report", self._report_wrapper)
        
        # Set entry point
        workflow.set_entry_point("supervisor")
        
        # Add conditional edges from supervisor
        workflow.add_conditional_edges(
            "supervisor",
            self._supervisor_router,
            {
                "intent_clarification": "intent_clarification",
                "planning": "planning", 
                "execution": "execution",
                "summary": "summary",
                "report": "report",
                "__end__": END
            }
        )
        
        # Add edges from other nodes back to supervisor or END
        workflow.add_conditional_edges(
            "intent_clarification",
            self._intent_clarification_router,
            {
                "planning": "planning",
                "supervisor": "supervisor",
                "__end__": END
            }
        )
        
        workflow.add_conditional_edges(
            "planning",
            self._planning_router,
            {
                "execution": "execution",
                "supervisor": "supervisor",
                "__end__": END
            }
        )
        
        workflow.add_conditional_edges(
            "execution",
            self._execution_router,
            {
                "summary": "summary",
                "__end__": END
            }
        )
        
        workflow.add_conditional_edges(
            "summary",
            self._summary_router,
            {
                "report": "report",
                "__end__": END
            }
        )
        
        workflow.add_edge("report", END)
        
        # Compile the graph
        return workflow.compile()
    
    # ==================== Node Wrappers ====================
    
    async def _supervisor_wrapper(self, state: BrandEventState) -> Command:
        """Wrapper for supervisor node with config injection."""
        config_dict = self.config.to_dict()
        return await supervisor_node(state, config_dict)

    async def _intent_clarification_wrapper(self, state: BrandEventState) -> Command:
        """Wrapper for intent clarification node with config injection."""
        config_dict = self.config.to_dict()
        return await intent_clarification_node(state, config_dict)

    async def _planning_wrapper(self, state: BrandEventState) -> Command:
        """Wrapper for planning node with config injection."""
        config_dict = self.config.to_dict()
        return await planning_node(state, config_dict)

    async def _execution_wrapper(self, state: BrandEventState) -> Command:
        """Wrapper for execution node with config injection."""
        config_dict = self.config.to_dict()
        return await execution_node(state, config_dict)

    async def _summary_wrapper(self, state: BrandEventState) -> Command:
        """Wrapper for summary node with config injection."""
        config_dict = self.config.to_dict()
        return await summary_node(state, config_dict)

    async def _report_wrapper(self, state: BrandEventState) -> Command:
        """Wrapper for report node with config injection."""
        config_dict = self.config.to_dict()
        return await report_node(state, config_dict)
    
    # ==================== Router Functions ====================
    
    def _supervisor_router(self, state: BrandEventState) -> str:
        """Route from supervisor based on the Command result."""
        # The supervisor node returns a Command with goto field
        # This router just needs to return the goto value
        # Since LangGraph handles Command objects automatically,
        # we don't need to implement this router
        pass
    
    def _intent_clarification_router(self, state: BrandEventState) -> str:
        """Route from intent clarification based on state."""
        workflow_status = state.get("workflow_status", WorkflowStatus.INITIALIZING)
        
        if workflow_status == WorkflowStatus.PLANNING:
            return "planning"
        elif workflow_status == WorkflowStatus.CLARIFYING_INTENT:
            return "__end__"
        else:
            return "supervisor"
    
    def _planning_router(self, state: BrandEventState) -> str:
        """Route from planning based on state."""
        workflow_status = state.get("workflow_status", WorkflowStatus.INITIALIZING)
        
        if workflow_status == WorkflowStatus.EXECUTING:
            return "execution"
        elif workflow_status == WorkflowStatus.PLANNING:
            return "__end__"
        else:
            return "supervisor"
    
    def _execution_router(self, state: BrandEventState) -> str:
        """Route from execution based on state."""
        workflow_status = state.get("workflow_status", WorkflowStatus.INITIALIZING)
        
        if workflow_status == WorkflowStatus.SUMMARIZING:
            return "summary"
        else:
            return "__end__"
    
    def _summary_router(self, state: BrandEventState) -> str:
        """Route from summary based on state."""
        workflow_status = state.get("workflow_status", WorkflowStatus.INITIALIZING)
        
        if workflow_status == WorkflowStatus.REPORT:
            return "report"
        else:
            return "__end__"
    
    # ==================== Public Methods ====================
    
    async def arun(self, state: BrandEventState, writer=None) -> BrandEventState:
        """
        Run the workflow asynchronously.
        
        Args:
            state: Initial workflow state
            writer: Optional writer function for streaming updates
            
        Returns:
            Final workflow state
        """
        try:
            logger.info(f"Starting workflow execution, session_id: {state.get('session_id', 'unknown')}")
            
            # Inject writer into config if provided
            if writer:
                config_dict = self.config.to_dict()
                config_dict['writer'] = writer
                
                # Update all wrapper methods to use the writer
                self._update_config_with_writer(config_dict)
            
            # Run the workflow
            final_state = await self.graph.ainvoke(state)
            
            logger.info(f"Workflow execution completed, session_id: {state.get('session_id', 'unknown')}")
            return final_state
            
        except Exception as e:
            logger.error(f"Workflow execution failed: {e}, session_id: {state.get('session_id', 'unknown')}")
            
            # Return error state
            error_state = state.copy()
            error_state.update({
                "workflow_status": WorkflowStatus.FAILED,
                "error_info": {"message": str(e), "source": "workflow"}
            })
            return error_state
    
    def run(self, state: BrandEventState, writer=None) -> BrandEventState:
        """
        Run the workflow synchronously.
        
        Args:
            state: Initial workflow state
            writer: Optional writer function for streaming updates
            
        Returns:
            Final workflow state
        """
        try:
            logger.info(f"Starting synchronous workflow execution, session_id: {state.get('session_id', 'unknown')}")
            
            # Inject writer into config if provided
            if writer:
                config_dict = self.config.to_dict()
                config_dict['writer'] = writer
                
                # Update all wrapper methods to use the writer
                self._update_config_with_writer(config_dict)
            
            # Run the workflow
            final_state = self.graph.invoke(state)
            
            logger.info(f"Synchronous workflow execution completed, session_id: {state.get('session_id', 'unknown')}")
            return final_state
            
        except Exception as e:
            logger.error(f"Synchronous workflow execution failed: {e}, session_id: {state.get('session_id', 'unknown')}")
            
            # Return error state
            error_state = state.copy()
            error_state.update({
                "workflow_status": WorkflowStatus.FAILED,
                "error_info": {"message": str(e), "source": "workflow"}
            })
            return error_state
    
    def _update_config_with_writer(self, config_dict: Dict[str, Any]):
        """Update configuration with writer for all node wrappers."""
        # This is a simple approach - in a more sophisticated implementation,
        # you might want to use dependency injection or a context manager
        self._current_config = config_dict
    
    def get_config(self) -> BrandEventConfig:
        """Get the current configuration."""
        return self.config
    
    def update_config(self, new_config: BrandEventConfig):
        """Update the workflow configuration."""
        self.config = new_config
        logger.info("Workflow configuration updated")
