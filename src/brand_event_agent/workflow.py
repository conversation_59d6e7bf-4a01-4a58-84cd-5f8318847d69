"""
Brand event analysis workflow implementation.

This module provides workflow nodes and graph construction following
open_deep_research patterns while maintaining compatibility with
existing Agent implementations.
"""

from typing import Dict, Any, Literal, Optional, List
from langgraph.graph import StateGraph, START, END
from langgraph.types import Command
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_openai import Chat<PERSON>penA<PERSON>
from pydantic import BaseModel, Field

from .state import BrandEventState, WorkflowStatus
from .config import BrandEventConfig
from .prompts import (
    SUPERVISOR_SYSTEM_PROMPT, build_supervisor_routing_prompt,
    INTENT_ANALYSIS_SYSTEM_PROMPT, build_intent_analysis_prompt, build_clarification_prompt,
    PLANNING_SYSTEM_PROMPT, build_planning_prompt, build_planning_intent_analysis_prompt,
    EXECUTION_SYSTEM_PROMPT,
    SUMMARY_SYSTEM_PROMPT, build_summary_prompt,
    REPORTING_SYSTEM_PROMPT
)
from .utils import (
    get_logger, get_latest_user_response, get_latest_plan_feedback, get_clarified_requirements,
    format_plan_message, create_access_token, generate_report, upload_html_to_s3,
    parse_dsl_summary, create_brand_analysis_dsl
)

logger = get_logger("BrandEventWorkflow")

# ==================== Data Models ====================
# Extracted from existing Agent implementations

class UserMessageAnalysis(BaseModel):
    """用户消息分析结果"""
    message_type: Literal["greeting", "task", "supplement", "agreement", "rejection", "detailed_description", "short_reply"] = Field(
        description="消息类型：greeting(问候), task(任务请求), supplement(补充信息), agreement(确认同意), rejection(拒绝否定), detailed_description(详细描述), short_reply(简短回复)"
    )
    user_intent: str = Field(description="用户真实意图分析")
    extracted_info: str = Field(default="", description="从用户消息中提取的关键信息")

class RouterDecisionWithAnalysis(BaseModel):
    """路由决策结果（包含分析）"""
    message_analysis: UserMessageAnalysis = Field(description="用户消息分析结果")
    next: Literal["intent_clarification", "planning", "execution", "summary", "report", "__end__"] = Field(
        description="下一步路由目标"
    )
    reason: str = Field(description="路由决策理由")
    response_message: str = Field(default="", description="需要返回给用户的消息（仅问候时填写）")
    workflow_status: str = Field(description="更新后的工作流状态")

class UserIntentAnalysis(BaseModel):
    """用户意图分析结果"""
    intent_type: Literal["agreement", "supplement", "rejection"] = Field(description="意图类型")
    next_action: Literal["continue", "retry"] = Field(description="下一步行动")
    extracted_info: str = Field(default="", description="提取的补充信息")

class IntentClarificationResult(BaseModel):
    """意图澄清结果"""
    intent_clear: bool = Field(description="意图是否清晰")
    clarification_questions: List[str] = Field(default=[], description="澄清问题列表")
    clarification_result: Optional[Dict[str, Any]] = Field(default=None, description="澄清结果")
    summary: str = Field(description="分析总结")
    response_message: str = Field(description="回复消息")

class PlanStep(BaseModel):
    """计划步骤"""
    title: str = Field(description="步骤标题")
    description: str = Field(description="步骤描述")
    estimated_time: str = Field(default="", description="预估时间")
    skip_execute: bool = Field(default=False, description="是否跳过执行")

class ExecutionPlan(BaseModel):
    """执行计划"""
    title: str = Field(description="计划标题")
    objective: str = Field(description="计划目标")
    steps: List[PlanStep] = Field(description="执行步骤")
    estimated_time: str = Field(default="", description="总预估时间")

# ==================== Supervisor Node ====================

async def supervisor_node(state: BrandEventState, config: RunnableConfig) -> Command:
    """
    Supervisor node - 完全移植SupervisorAgent.execute逻辑
    """
    configurable = BrandEventConfig.from_runnable_config(config)
    session_id = state.get('session_id', 'unknown')
    logger.info(f"Start doing workflow supervision, session_id:{session_id}")

    try:
        # 发送监督开始的消息
        writer = getattr(configurable, 'writer', None)
        if writer:
            writer({"live_status_message": "正在分析当前状态和用户消息..."})
        
        # 使用LLM进行智能路由决策
        result = await _llm_based_routing(state, session_id, writer, configurable)
        route_reason = result.update.get('reason', 'N/A')
        
        logger.info(f"Completed workflow supervision, result: route to {result.goto}, reason: {route_reason}, session_id:{session_id}")
        return result
        
    except Exception as e:
        logger.error(f"Failed workflow supervision, error: {str(e)}, session_id:{session_id}")
        
        # 发送错误分析消息
        if writer:
            writer({"live_status_message": "路由分析遇到问题，使用默认策略"})
        
        # 出错时默认路由到意图澄清
        return Command(goto="intent_clarification", update={"workflow_status": WorkflowStatus.CLARIFYING_INTENT})

async def _llm_based_routing(state: BrandEventState, session_id: str, writer, config: Dict[str, Any]) -> Command:
    """
    基于LLM的智能路由决策 - 完全移植SupervisorAgent._llm_based_routing逻辑
    """
    try:
        # 构建路由决策提示词
        routing_prompt = build_supervisor_routing_prompt(state)
        
        # 发送LLM分析的消息
        if writer:
            writer({"live_status_message": "正在分析用户消息类型和意图..."})
        
        # 初始化LLM
        llm = ChatOpenAI(
            model=config.get("supervisor_model", "gpt-4o-mini"),
            temperature=0
        )
        
        # 使用结构化输出获取路由决策
        messages = [HumanMessage(content=routing_prompt)]
        response = llm.with_structured_output(RouterDecisionWithAnalysis).invoke(messages)
        
        # 记录分析结果
        logger.info(f"User message analysis: type={response.message_analysis.message_type}, session_id:{session_id}")
        logger.info(f"LLM routing decision: {response.next}, reason: {response.reason}, session_id:{session_id}")
        
        # 直接根据LLM返回的结构化结果构建Command
        update_data = {
            "reason": response.reason,
            "workflow_status": response.workflow_status
        }
        
        # 如果是问候消息，添加回复消息
        if response.message_analysis.message_type == "greeting" and response.next=='__end__' and response.response_message:
            if writer:
                writer({"agent_message": response.response_message})
                writer({"human_feedback_message": "请输入具体任务需求，我将为您提供帮助。"})
        
        return Command(goto=response.next, update=update_data)
        
    except Exception as e:
        logger.error(f"Failed LLM-based routing, error: {str(e)}, session_id:{session_id}")
        
        # 发送错误分析消息
        if writer:
            writer({"live_status_message": "路由分析遇到问题，使用默认策略"})
        
        # 出错时默认路由到意图澄清
        return Command(goto="intent_clarification", update={"workflow_status": WorkflowStatus.CLARIFYING_INTENT})

# ==================== Intent Clarification Node ====================

async def intent_clarification_node(state: BrandEventState, config: RunnableConfig) -> Command:
    """
    Intent clarification node - 完全移植IntentAnalysisAgent.execute逻辑
    """
    configurable = BrandEventConfig.from_runnable_config(config)
    session_id = state.get('session_id', 'unknown')
    clarification_round = state.get("clarification_round", 0)
    logger.info(f"Start doing intent clarification (round {clarification_round + 1}), session_id:{session_id}")

    try:
        writer = getattr(configurable, 'writer', None)

        # 发送任务开始的Agent消息
        if clarification_round == 0 and writer:
            writer({"agent_message": "正在分析您的需求，确保完全理解您的需求。"})

        # 发送实时状态消息
        if writer:
            writer({"live_status_message": "正在分析用户意图..."})

        # 获取基本信息
        latest_user_response = get_latest_user_response(state)
        context_messages = state.get("messages", [])

        # 初始化LLM
        llm = ChatOpenAI(
            model=configurable.analyst_model,
            temperature=0
        )

        # 如果是多轮澄清，先分析用户意图
        if clarification_round > 0 and latest_user_response:
            # 发送分析意图的状态消息
            if writer:
                writer({"live_status_message": "正在分析您的回复意图..."})

            intent_analysis = await _analyze_user_intent(
                llm=llm,
                user_response=latest_user_response,
                current_stage="意图澄清阶段",
                context_messages=context_messages
            )

            # 根据意图分析结果处理
            if intent_analysis.intent_type == "agreement":
                # 发送用户确认的消息
                if writer:
                    writer({"agent_message": "意图已确认，将为您制定详细的执行计划"})

                logger.info(f"Completed intent clarification, result: user approved requirements, session_id:{session_id}")
                return Command(
                    goto="planning",
                    update={
                        "intent_clarified": True,
                        "intent_approved": True,  # 用户确认审批通过
                        "workflow_status": WorkflowStatus.PLANNING,  # 审批通过，进入计划阶段
                        "messages": state["messages"] + [HumanMessage(content="感谢您的确认！我现在开始为您制定执行计划。")]
                    }
                )

            elif intent_analysis.intent_type == "supplement":
                # 发送补充信息的消息
                if writer:
                    writer({"agent_message": "我理解您提供的补充信息，让我重新分析您的需求。"})

                logger.info(f"User provided supplement info, re-analyzing intent, session_id:{session_id}")
                # 继续进行澄清分析

        # 发送澄清分析的状态消息
        if writer:
            writer({"live_status_message": "正在分析需求清晰度..."})

        # 判断用户意图是否清晰
        user_input = state.get("user_input", "")
        clarification_result = await _is_intent_clear(
            llm=llm,
            user_input=user_input,
            context_messages=context_messages
        )

        if clarification_result.intent_clear:
            # 意图清晰，直接进入计划阶段
            if writer:
                writer({"agent_message": clarification_result.response_message})
                writer({"agent_message": "需求已明确，将为您制定详细的执行计划"})

            logger.info(f"Completed intent clarification, result: intent is clear, session_id:{session_id}")
            return Command(
                goto="planning",
                update={
                    "intent_clarified": True,
                    "intent_approved": True,
                    "clarification_result": clarification_result.clarification_result,
                    "workflow_status": WorkflowStatus.PLANNING,
                    "messages": state["messages"] + [
                        HumanMessage(content=clarification_result.response_message),
                        HumanMessage(content="需求已明确，开始制定执行计划。")
                    ],
                    "clarification_round": clarification_round + 1
                }
            )
        else:
            # 需要澄清，发送澄清问题
            if writer:
                writer({"agent_message": clarification_result.response_message})
                writer({"human_feedback_message": "请提供更多详细信息，以便我为您制定最佳的分析方案。"})

            logger.info(f"Completed intent clarification, result: need clarification, session_id:{session_id}")
            return Command(
                goto="__end__",
                update={
                    "intent_clarified": False,
                    "intent_approved": False,
                    "workflow_status": WorkflowStatus.CLARIFYING_INTENT,
                    "messages": state["messages"] + [HumanMessage(content=clarification_result.response_message)],
                    "clarification_round": clarification_round + 1
                }
            )

    except Exception as e:
        logger.error(f"Failed intent clarification, error: {str(e)}, session_id:{session_id}")
        return Command(goto="__end__", update={"workflow_status": WorkflowStatus.FAILED})

# ==================== Helper Functions ====================
# 完全移植现有Agent的辅助函数

async def _analyze_user_intent(llm, user_response: str, current_stage: str, context_messages) -> UserIntentAnalysis:
    """
    分析用户意图 - 完全移植IntentAnalysisAgent._analyze_user_intent逻辑
    """
    try:
        # 构建意图分析提示词
        intent_prompt = build_intent_analysis_prompt(current_stage, user_response)

        # 使用结构化输出分析用户意图
        messages = [
            SystemMessage(content=INTENT_ANALYSIS_SYSTEM_PROMPT),
            HumanMessage(content=intent_prompt)
        ]

        response = llm.with_structured_output(UserIntentAnalysis).invoke(messages)
        return response

    except Exception as e:
        logger.error(f"Failed to analyze user intent: {e}")
        # 返回默认的补充信息意图
        return UserIntentAnalysis(
            intent_type="supplement",
            next_action="retry",
            extracted_info=user_response
        )

async def _is_intent_clear(llm, user_input: str, context_messages) -> IntentClarificationResult:
    """
    判断用户意图是否清晰 - 完全移植IntentAnalysisAgent._is_intent_clear逻辑
    """
    try:
        # 构建澄清分析提示词
        clarification_prompt = build_clarification_prompt(user_input)

        # 使用结构化输出分析意图清晰度
        messages = [
            SystemMessage(content=INTENT_ANALYSIS_SYSTEM_PROMPT),
            HumanMessage(content=clarification_prompt)
        ]

        response = llm.with_structured_output(IntentClarificationResult).invoke(messages)
        return response

    except Exception as e:
        logger.error(f"Failed to analyze intent clarity: {e}")
        # 返回默认的需要澄清结果
        return IntentClarificationResult(
            intent_clear=False,
            clarification_questions=["请提供更多详细信息"],
            clarification_result=None,
            summary="分析过程中出现错误",
            response_message="为了更好地为您服务，请提供更多详细信息。"
        )

# ==================== Planning Node ====================

async def planning_node(state: BrandEventState, config: RunnableConfig) -> Command:
    """
    Planning node - 完全移植PlanningAgent.execute逻辑
    """
    session_id = state.get('session_id', 'unknown')
    planning_round = state.get("planning_round", 0)
    logger.info(f"Start doing planning (round {planning_round + 1}), session_id:{session_id}")

    try:
        writer = config.get('writer')

        # 发送任务开始的Agent消息
        if planning_round == 0 and writer:
            writer({"agent_message": "正在为您制定详细的执行计划。"})

        # 发送实时状态消息
        if writer:
            writer({"live_status_message": "正在制定执行计划..."})

        # 获取基本信息
        latest_plan_feedback = get_latest_plan_feedback(state)

        # 初始化LLM
        llm = ChatOpenAI(
            model=config.get("planner_model", "gpt-4o-mini"),
            temperature=0
        )

        # 如果有用户反馈且计划未批准，先分析反馈意图
        if latest_plan_feedback and not state.get("plan_approved", False):
            # 发送分析反馈的状态消息
            if writer:
                writer({"live_status_message": "正在分析您的反馈意图..."})

            feedback_analysis = await _analyze_plan_feedback_intent(
                llm=llm,
                feedback=latest_plan_feedback
            )

            # 根据反馈分析结果处理
            if feedback_analysis.intent_type == "agreement":
                # 发送用户确认的消息
                if writer:
                    writer({"agent_message": "计划已确认，将开始执行分析任务"})

                logger.info(f"Completed planning, result: user approved plan, session_id:{session_id}")
                return Command(
                    goto="execution",
                    update={
                        "plan_approved": True,
                        "workflow_status": WorkflowStatus.EXECUTING,
                        "messages": state["messages"] + [HumanMessage(content="感谢您的确认！我现在开始执行分析任务。")]
                    }
                )

            elif feedback_analysis.intent_type == "supplement":
                # 发送修改计划的消息
                if writer:
                    writer({"agent_message": "我理解您的修改建议，让我重新制定计划。"})

                logger.info(f"User provided plan modification suggestions, re-planning, session_id:{session_id}")
                # 继续进行计划制定

        # 发送制定计划的状态消息
        if writer:
            writer({"live_status_message": "正在生成执行计划..."})

        # 制定执行计划
        clarified_requirements = get_clarified_requirements(state)
        previous_plan = state.get("task_plan")

        execution_plan = await _create_execution_plan(
            llm=llm,
            user_requirements=clarified_requirements,
            previous_plan=previous_plan
        )

        # 格式化计划消息
        is_revision = planning_round > 0
        plan_message = format_plan_message(execution_plan.dict(), is_revision)

        # 发送计划消息
        if writer:
            writer({"agent_message": plan_message})
            writer({"human_feedback_message": "请确认执行计划，如需修改请告诉我具体调整建议。"})

        logger.info(f"Completed planning, result: plan created, session_id:{session_id}")
        return Command(
            goto="__end__",
            update={
                "task_plan": execution_plan.dict(),
                "plan_approved": False,  # 等待用户确认
                "workflow_status": WorkflowStatus.PLANNING,
                "messages": state["messages"] + [HumanMessage(content=plan_message)],
                "planning_round": planning_round + 1
            }
        )

    except Exception as e:
        logger.error(f"Failed planning, error: {str(e)}, session_id:{session_id}")
        return Command(goto="__end__", update={"workflow_status": WorkflowStatus.FAILED})

# ==================== Execution Node ====================

async def execution_node(state: BrandEventState, config: RunnableConfig) -> Command:
    """
    Execution node - 完全移植ExecutionAgent.execute逻辑
    """
    session_id = state.get('session_id', 'unknown')
    logger.info(f"Start doing execution, session_id:{session_id}")

    try:
        writer = config.get('writer')

        # 发送任务开始的Agent消息
        if writer:
            writer({"agent_message": "开始执行分析任务，请稍候..."})

        # 发送实时状态消息
        if writer:
            writer({"live_status_message": "正在执行分析计划..."})

        # 获取执行计划
        task_plan = state.get("task_plan", {})
        if not task_plan:
            logger.error(f"No task plan found, session_id:{session_id}")
            return Command(goto="__end__", update={"workflow_status": WorkflowStatus.FAILED})

        # 初始化LLM
        llm = ChatOpenAI(
            model=config.get("executor_model", "gpt-4o-mini"),
            temperature=0
        )

        # 执行计划中的所有步骤
        execution_results = []
        steps = task_plan.get("steps", [])

        for i, step in enumerate(steps):
            step_title = step.get("title", f"步骤{i+1}")
            step_description = step.get("description", "")
            skip_execute = step.get("skip_execute", False)

            # 发送步骤开始消息
            if writer:
                writer({"live_status_message": f"正在执行：{step_title}"})
                writer({"agent_message": f"开始执行步骤：{step_title}"})

            if skip_execute:
                # 跳过执行的步骤
                logger.info(f"Skipping step: {step_title}, session_id:{session_id}")
                execution_results.append({
                    "step": step_title,
                    "status": "skipped",
                    "result": "此步骤已跳过执行",
                    "description": step_description
                })

                if writer:
                    writer({"agent_message": f"步骤 {step_title} 已跳过"})

                continue

            # 执行步骤
            try:
                step_result = await _execute_step(
                    llm=llm,
                    step=step,
                    state=state,
                    writer=writer,
                    config=config
                )

                execution_results.append({
                    "step": step_title,
                    "status": "completed",
                    "result": step_result,
                    "description": step_description
                })

                if writer:
                    writer({"agent_message": f"步骤 {step_title} 执行完成"})

                logger.info(f"Completed step: {step_title}, session_id:{session_id}")

            except Exception as e:
                logger.error(f"Failed to execute step {step_title}: {e}, session_id:{session_id}")

                execution_results.append({
                    "step": step_title,
                    "status": "failed",
                    "result": f"执行失败：{str(e)}",
                    "description": step_description
                })

                if writer:
                    writer({"agent_message": f"步骤 {step_title} 执行失败：{str(e)}"})

        # 生成执行报告
        execution_report = _generate_execution_report(execution_results)

        # 发送完成消息
        if writer:
            writer({"agent_message": "所有执行步骤已完成，正在生成总结..."})

        logger.info(f"Completed execution, session_id:{session_id}")
        return Command(
            goto="summary",
            update={
                "execution_started": True,
                "execution_results": execution_results,
                "execution_report": execution_report,
                "workflow_status": WorkflowStatus.SUMMARIZING,
                "messages": state["messages"] + [HumanMessage(content="执行阶段已完成，开始生成总结。")]
            }
        )

    except Exception as e:
        logger.error(f"Failed execution, error: {str(e)}, session_id:{session_id}")
        return Command(goto="__end__", update={"workflow_status": WorkflowStatus.FAILED})

# ==================== Summary Node ====================

async def summary_node(state: BrandEventState, config: RunnableConfig) -> Command:
    """
    Summary node - 完全移植SummaryAgent.execute逻辑
    """
    session_id = state.get('session_id', 'unknown')
    logger.info(f"Start doing summary, session_id:{session_id}")

    try:
        writer = config.get('writer')

        # 发送任务开始的Agent消息
        if writer:
            writer({"agent_message": "正在生成任务执行总结..."})

        # 发送实时状态消息
        if writer:
            writer({"live_status_message": "正在整理和分析执行结果..."})

        # 初始化LLM
        llm = ChatOpenAI(
            model=config.get("summarizer_model", "gpt-4o-mini"),
            temperature=0
        )

        # 生成总结
        summary_report = await _generate_summary_report(llm, state, writer)

        # 发送总结消息
        if writer:
            writer({"agent_message": summary_report})
            writer({"agent_message": "总结已完成，准备生成最终报告..."})

        logger.info(f"Completed summary, session_id:{session_id}")
        return Command(
            goto="report",
            update={
                "summary_report": summary_report,
                "workflow_status": WorkflowStatus.REPORT,
                "messages": state["messages"] + [HumanMessage(content="总结阶段已完成，开始生成报告。")]
            }
        )

    except Exception as e:
        logger.error(f"Failed summary, error: {str(e)}, session_id:{session_id}")
        return Command(goto="__end__", update={"workflow_status": WorkflowStatus.FAILED})

# ==================== Report Node ====================

async def report_node(state: BrandEventState, config: RunnableConfig) -> Command:
    """
    Report node - 完全移植ReportAgent.execute逻辑
    """
    session_id = state.get('session_id', 'unknown')
    logger.info(f"Start doing report generation, session_id:{session_id}")

    try:
        writer = config.get('writer')

        # 发送任务开始的Agent消息
        if writer:
            writer({"agent_message": "正在生成最终分析报告..."})

        # 检查report_dsl_status
        report_dsl_status = state.get("report_dsl_status")
        if report_dsl_status == "FAILED":
            error_message = state.get("report_dsl_message", "报告DSL生成失败")
            logger.error(f"Report DSL generation failed: {error_message}, session_id:{session_id}")

            if writer:
                writer({"agent_message": f"报告生成失败：{error_message}"})

            return Command(
                goto="__end__",
                update={
                    "workflow_status": WorkflowStatus.FAILED,
                    "final_report": f"报告生成失败：{error_message}",
                    "messages": state["messages"] + [HumanMessage(content=f"报告生成失败：{error_message}")]
                }
            )

        # 发送实时状态消息
        if writer:
            writer({"live_status_message": "正在调用报告服务..."})

        # 获取或创建report_dsl_data
        report_dsl_data = state.get("report_dsl_data")
        if not report_dsl_data:
            # 创建默认的DSL数据
            report_dsl_data = create_brand_analysis_dsl()
            logger.info(f"Created default report DSL, session_id:{session_id}")

        # 调用报告服务生成HTML报告
        report_result = await _generate_html_report(report_dsl_data, writer, config)

        if not report_result["success"]:
            error_message = report_result.get("message", "报告生成失败")
            logger.error(f"HTML report generation failed: {error_message}, session_id:{session_id}")

            if writer:
                writer({"agent_message": f"报告生成失败：{error_message}"})

            return Command(
                goto="__end__",
                update={
                    "workflow_status": WorkflowStatus.FAILED,
                    "final_report": f"报告生成失败：{error_message}",
                    "messages": state["messages"] + [HumanMessage(content=f"报告生成失败：{error_message}")]
                }
            )

        # 上传HTML到S3
        if writer:
            writer({"live_status_message": "正在上传报告..."})

        html_content = report_result["html_content"]
        upload_result = await _upload_report_to_s3(html_content, writer, config)

        # 解析DSL生成文本总结
        if writer:
            writer({"live_status_message": "正在生成报告摘要..."})

        summary_data = parse_dsl_summary(report_dsl_data)

        # 生成最终报告消息
        final_report = _build_final_report_message(
            summary_data=summary_data,
            upload_result=upload_result,
            execution_results=state.get("execution_results", [])
        )

        # 发送完成消息
        if writer:
            writer({"agent_message": final_report})
            writer({"agent_message": "🎉 品牌舆情分析任务已全部完成！"})

        logger.info(f"Completed report generation, session_id:{session_id}")
        return Command(
            goto="__end__",
            update={
                "final_report": final_report,
                "html_report": html_content,
                "summary_data": summary_data,
                "upload_result": upload_result,
                "workflow_status": WorkflowStatus.COMPLETED,
                "messages": state["messages"] + [HumanMessage(content="报告生成已完成。")]
            }
        )

    except Exception as e:
        logger.error(f"Failed report generation, error: {str(e)}, session_id:{session_id}")
        return Command(goto="__end__", update={"workflow_status": WorkflowStatus.FAILED})

# ==================== Planning Helper Functions ====================

async def _analyze_plan_feedback_intent(llm, feedback: str) -> UserIntentAnalysis:
    """
    分析计划反馈意图 - 完全移植PlanningAgent._analyze_plan_feedback_intent逻辑
    """
    try:
        # 构建反馈分析提示词
        feedback_prompt = build_planning_intent_analysis_prompt(feedback)

        # 使用结构化输出分析反馈意图
        messages = [
            SystemMessage(content=PLANNING_SYSTEM_PROMPT),
            HumanMessage(content=feedback_prompt)
        ]

        response = llm.with_structured_output(UserIntentAnalysis).invoke(messages)
        return response

    except Exception as e:
        logger.error(f"Failed to analyze plan feedback intent: {e}")
        # 返回默认的补充信息意图
        return UserIntentAnalysis(
            intent_type="supplement",
            next_action="retry",
            extracted_info=feedback
        )

async def _create_execution_plan(llm, user_requirements: str, previous_plan=None) -> ExecutionPlan:
    """
    创建执行计划 - 完全移植PlanningAgent._create_execution_plan逻辑
    """
    try:
        # 构建规划提示词
        planning_prompt = build_planning_prompt(user_requirements, previous_plan)

        # 使用结构化输出生成执行计划
        messages = [
            SystemMessage(content=PLANNING_SYSTEM_PROMPT),
            HumanMessage(content=planning_prompt)
        ]

        response = llm.with_structured_output(ExecutionPlan).invoke(messages)
        return response

    except Exception as e:
        logger.error(f"Failed to create execution plan: {e}")
        # 返回默认计划
        return ExecutionPlan(
            title="品牌舆情分析计划",
            objective="分析品牌舆情状况",
            steps=[
                PlanStep(
                    title="数据收集",
                    description="收集品牌相关舆情数据",
                    estimated_time="30分钟"
                ),
                PlanStep(
                    title="数据分析",
                    description="分析舆情数据并生成洞察",
                    estimated_time="20分钟"
                ),
                PlanStep(
                    title="报告生成",
                    description="生成分析报告",
                    estimated_time="10分钟",
                    skip_execute=True
                )
            ],
            estimated_time="60分钟"
        )

# ==================== Execution Helper Functions ====================

async def _execute_step(llm, step: Dict[str, Any], state: BrandEventState, writer, config: Dict[str, Any]) -> str:
    """
    执行单个步骤 - 完全移植ExecutionAgent._execute_step逻辑
    """
    step_title = step.get("title", "")
    step_description = step.get("description", "")

    # 根据步骤类型执行不同逻辑
    if "数据收集" in step_title or "收集" in step_title:
        return await _execute_data_collection_step(llm, step, state, writer, config)
    elif "分析" in step_title:
        return await _execute_analysis_step(llm, step, state, writer, config)
    else:
        return await _execute_generic_step(llm, step, state, writer, config)

async def _execute_data_collection_step(llm, step: Dict[str, Any], state: BrandEventState, writer, config: Dict[str, Any]) -> str:
    """
    执行数据收集步骤 - 完全移植ExecutionAgent._execute_data_collection_step逻辑
    """
    try:
        # 获取用户需求
        user_input = state.get("user_input", "")
        user_id = state.get("user_id")

        if writer:
            writer({"live_status_message": "正在调用MCP工具收集数据..."})

        # 创建访问令牌
        if user_id:
            access_token = create_access_token(user_id)
            logger.info(f"Created access token for user: {user_id}")
        else:
            logger.warning("No user_id provided, using default token")
            access_token = "default_token"

        # 模拟MCP工具调用（实际实现中会调用真实的MCP工具）
        # 这里保持与原ExecutionAgent相同的逻辑
        collection_result = {
            "status": "success",
            "data_collected": True,
            "summary": f"已成功收集关于 '{user_input}' 的相关数据",
            "details": "数据收集包括社交媒体、新闻、论坛等多个渠道的信息"
        }

        if writer:
            writer({"agent_message": f"数据收集完成：{collection_result['summary']}"})

        return f"数据收集步骤执行成功：{collection_result['summary']}"

    except Exception as e:
        logger.error(f"Failed to execute data collection step: {e}")
        return f"数据收集步骤执行失败：{str(e)}"

async def _execute_analysis_step(llm, step: Dict[str, Any], state: BrandEventState, writer, config: Dict[str, Any]) -> str:
    """
    执行分析步骤 - 完全移植ExecutionAgent._execute_analysis_step逻辑
    """
    try:
        if writer:
            writer({"live_status_message": "正在进行数据分析..."})

        # 模拟分析过程
        analysis_result = {
            "status": "success",
            "analysis_completed": True,
            "key_findings": [
                "品牌整体舆情趋势良好",
                "用户满意度较高",
                "存在一些改进空间"
            ],
            "sentiment_score": 0.75
        }

        if writer:
            writer({"agent_message": "数据分析完成，发现了一些关键洞察"})

        return f"分析步骤执行成功，发现关键洞察：{', '.join(analysis_result['key_findings'])}"

    except Exception as e:
        logger.error(f"Failed to execute analysis step: {e}")
        return f"分析步骤执行失败：{str(e)}"

async def _execute_generic_step(llm, step: Dict[str, Any], state: BrandEventState, writer, config: Dict[str, Any]) -> str:
    """
    执行通用步骤 - 完全移植ExecutionAgent._execute_generic_step逻辑
    """
    try:
        step_title = step.get("title", "")
        step_description = step.get("description", "")

        if writer:
            writer({"live_status_message": f"正在执行：{step_title}"})

        # 模拟步骤执行
        result = f"步骤 '{step_title}' 执行完成：{step_description}"

        if writer:
            writer({"agent_message": f"步骤执行完成：{step_title}"})

        return result

    except Exception as e:
        logger.error(f"Failed to execute generic step: {e}")
        return f"步骤执行失败：{str(e)}"

def _generate_execution_report(execution_results: List[Dict[str, Any]]) -> str:
    """
    生成执行报告 - 完全移植ExecutionAgent._generate_execution_report逻辑
    """
    try:
        total_steps = len(execution_results)
        completed_steps = len([r for r in execution_results if r.get("status") == "completed"])
        failed_steps = len([r for r in execution_results if r.get("status") == "failed"])
        skipped_steps = len([r for r in execution_results if r.get("status") == "skipped"])

        report_lines = [
            "## 执行报告",
            f"- 总步骤数：{total_steps}",
            f"- 完成步骤：{completed_steps}",
            f"- 失败步骤：{failed_steps}",
            f"- 跳过步骤：{skipped_steps}",
            "",
            "### 详细结果："
        ]

        for i, result in enumerate(execution_results, 1):
            step_name = result.get("step", f"步骤{i}")
            status = result.get("status", "unknown")
            step_result = result.get("result", "无结果")

            status_emoji = {
                "completed": "✅",
                "failed": "❌",
                "skipped": "⏭️"
            }.get(status, "❓")

            report_lines.append(f"{i}. {status_emoji} {step_name}: {step_result}")

        return "\n".join(report_lines)

    except Exception as e:
        logger.error(f"Failed to generate execution report: {e}")
        return "执行报告生成失败"

# ==================== Summary Helper Functions ====================

async def _generate_summary_report(llm, state: BrandEventState, writer) -> str:
    """
    生成总结报告 - 完全移植SummaryAgent._generate_summary_report逻辑
    """
    try:
        # 发送生成总结的状态消息
        if writer:
            writer({"live_status_message": "正在生成总结报告..."})

        # 构建总结提示词
        summary_prompt = build_summary_prompt()

        # 获取执行结果和历史消息
        execution_results = state.get("execution_results", [])
        execution_report = state.get("execution_report", "")
        user_input = state.get("user_input", "")

        # 构建上下文信息
        context_info = f"""
## 用户原始需求
{user_input}

## 执行结果
{execution_report}

## 详细执行步骤
"""

        for i, result in enumerate(execution_results, 1):
            step_name = result.get("step", f"步骤{i}")
            status = result.get("status", "unknown")
            step_result = result.get("result", "无结果")
            context_info += f"{i}. {step_name} ({status}): {step_result}\n"

        # 使用LLM生成总结
        messages = [
            SystemMessage(content=SUMMARY_SYSTEM_PROMPT),
            HumanMessage(content=f"{summary_prompt}\n\n{context_info}")
        ]

        response = llm.invoke(messages)
        summary_content = response.content

        logger.info("Summary report generated successfully")
        return summary_content

    except Exception as e:
        logger.error(f"Failed to generate summary report: {e}")
        return "总结报告生成过程中出现错误，请查看执行详情。"

# ==================== Report Helper Functions ====================

async def _generate_html_report(report_dsl_data: Dict[str, Any], writer, config: Dict[str, Any]) -> Dict[str, Any]:
    """
    生成HTML报告 - 完全移植ReportAgent._generate_html_report逻辑
    """
    try:
        # 获取报告服务配置
        report_config = config.get("report_config", {})
        base_url = report_config.get("base_url", "https://console-playground.fed.chehejia.com")
        timeout = report_config.get("timeout", 300)

        # 调用报告生成服务
        result = await generate_report(
            report_dsl=report_dsl_data,
            base_url=base_url,
            timeout=timeout
        )

        return result

    except Exception as e:
        logger.error(f"Failed to generate HTML report: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": f"HTML报告生成失败：{str(e)}"
        }

async def _upload_report_to_s3(html_content: str, writer, config: Dict[str, Any]) -> Dict[str, Any]:
    """
    上传报告到S3 - 完全移植ReportAgent._upload_report_to_s3逻辑
    """
    try:
        # 获取上传服务配置
        report_config = config.get("report_config", {})
        upload_url = report_config.get("upload_url", "https://xuanji-backend-service-dev.inner.chj.cloud/api/v1/ois/upload")
        timeout = report_config.get("timeout", 300)

        # 上传HTML到S3
        result = await upload_html_to_s3(
            html_content=html_content,
            upload_url=upload_url,
            timeout=timeout
        )

        return result

    except Exception as e:
        logger.error(f"Failed to upload report to S3: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": f"报告上传失败：{str(e)}"
        }

def _build_final_report_message(summary_data: Dict[str, Any], upload_result: Dict[str, Any], execution_results: List[Dict[str, Any]]) -> str:
    """
    构建最终报告消息 - 完全移植ReportAgent._build_final_report_message逻辑
    """
    try:
        # 构建报告消息
        report_lines = [
            "## 📊 品牌舆情分析报告",
            "",
            "### 🎯 分析摘要",
            summary_data.get("ai_summary", "分析已完成，请查看详细报告。"),
            ""
        ]

        # 添加关键指标
        key_metrics = summary_data.get("key_metrics", {})
        if key_metrics:
            report_lines.append("### 📈 关键指标")
            for label, value in key_metrics.items():
                report_lines.append(f"- **{label}**: {value}")
            report_lines.append("")

        # 添加执行统计
        total_steps = len(execution_results)
        completed_steps = len([r for r in execution_results if r.get("status") == "completed"])

        report_lines.extend([
            "### ✅ 执行统计",
            f"- 总执行步骤：{total_steps}",
            f"- 成功完成：{completed_steps}",
            f"- 完成率：{(completed_steps/total_steps*100):.1f}%" if total_steps > 0 else "- 完成率：0%",
            ""
        ])

        # 添加报告链接
        if upload_result.get("success"):
            upload_data = upload_result.get("upload_result", {})
            if upload_data.get("url"):
                report_lines.extend([
                    "### 🔗 完整报告",
                    f"[点击查看详细报告]({upload_data['url']})",
                    ""
                ])

        report_lines.append("---")
        report_lines.append("感谢您使用品牌舆情分析服务！")

        return "\n".join(report_lines)

    except Exception as e:
        logger.error(f"Failed to build final report message: {e}")
        return "报告生成完成，但消息构建过程中出现错误。"

# ==================== Workflow Graph Construction ====================

def create_workflow_graph() -> StateGraph:
    """
    Create the brand event analysis workflow graph.

    Following open_deep_research pattern for graph construction.

    Returns:
        Compiled workflow graph
    """
    # Create the main workflow graph
    builder = StateGraph(
        BrandEventState,
        config_schema=BrandEventConfig
    )

    # Add all workflow nodes
    builder.add_node("supervisor", supervisor_node)
    builder.add_node("intent_clarification", intent_clarification_node)
    builder.add_node("planning", planning_node)
    builder.add_node("execution", execution_node)
    builder.add_node("summary", summary_node)
    builder.add_node("report", report_node)

    # Define workflow edges
    # Start with supervisor for routing
    builder.add_edge(START, "supervisor")

    # Supervisor can route to any node or end
    builder.add_edge("supervisor", END)  # For greetings or completed workflows
    builder.add_edge("supervisor", "intent_clarification")
    builder.add_edge("supervisor", "planning")
    builder.add_edge("supervisor", "execution")
    builder.add_edge("supervisor", "summary")
    builder.add_edge("supervisor", "report")

    # Intent clarification can end (for user feedback) or go to planning
    builder.add_edge("intent_clarification", END)
    builder.add_edge("intent_clarification", "planning")

    # Planning can end (for user feedback) or go to execution
    builder.add_edge("planning", END)
    builder.add_edge("planning", "execution")

    # Execution goes to summary
    builder.add_edge("execution", "summary")

    # Summary goes to report
    builder.add_edge("summary", "report")

    # Report ends the workflow
    builder.add_edge("report", END)

    return builder

# ==================== Workflow Instance ====================

# Create the compiled workflow graph
workflow_builder = create_workflow_graph()
workflow = workflow_builder.compile()

# ==================== Utility Functions ====================

async def run_workflow(
    initial_state: BrandEventState,
    config: Optional[Dict[str, Any]] = None,
    writer=None
) -> BrandEventState:
    """
    Run the brand event analysis workflow.

    Args:
        initial_state: Initial workflow state
        config: Optional configuration
        writer: Optional writer function for streaming

    Returns:
        Final workflow state
    """
    try:
        session_id = initial_state.get("session_id", "unknown")
        logger.info(f"Starting workflow execution, session_id: {session_id}")

        # Prepare configuration
        if config is None:
            config = {}

        # Add writer to config if provided
        if writer:
            config["writer"] = writer

        # Create runnable config
        runnable_config = {"configurable": config}

        # Run the workflow
        final_state = await workflow.ainvoke(initial_state, config=runnable_config)

        logger.info(f"Workflow execution completed, session_id: {session_id}")
        return final_state

    except Exception as e:
        logger.error(f"Workflow execution failed: {e}")
        # Return error state
        return {
            **initial_state,
            "workflow_status": WorkflowStatus.FAILED,
            "error_info": {
                "error_type": "workflow_error",
                "message": str(e)
            }
        }

async def stream_workflow(
    initial_state: BrandEventState,
    config: Optional[Dict[str, Any]] = None
):
    """
    Stream the brand event analysis workflow execution.

    Args:
        initial_state: Initial workflow state
        config: Optional configuration

    Yields:
        Workflow state updates
    """
    try:
        session_id = initial_state.get("session_id", "unknown")
        logger.info(f"Starting workflow streaming, session_id: {session_id}")

        # Prepare configuration
        if config is None:
            config = {}

        # Create runnable config
        runnable_config = {"configurable": config}

        # Stream the workflow
        async for chunk in workflow.astream(initial_state, config=runnable_config):
            yield chunk

        logger.info(f"Workflow streaming completed, session_id: {session_id}")

    except Exception as e:
        logger.error(f"Workflow streaming failed: {e}")
        # Yield error state
        yield {
            **initial_state,
            "workflow_status": WorkflowStatus.FAILED,
            "error_info": {
                "error_type": "workflow_streaming_error",
                "message": str(e)
            }
        }

# ==================== Export All Functions ====================

__all__ = [
    "supervisor_node",
    "intent_clarification_node",
    "planning_node",
    "execution_node",
    "summary_node",
    "report_node",
    "workflow",
    "create_workflow_graph",
    "run_workflow",
    "stream_workflow"
]
