"""
Configuration management for brand event analysis workflow.

This module provides configuration settings identical to existing
implementations to ensure complete compatibility.
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, field

@dataclass
class BrandEventConfig:
    """
    Configuration for brand event analysis system.
    
    This configuration maintains compatibility with existing Agent implementations.
    """
    
    # ==================== Model Configuration ====================
    supervisor_model: str = "gpt-4o-mini"
    analyst_model: str = "gpt-4o-mini"
    planner_model: str = "gpt-4o-mini"
    executor_model: str = "gpt-4o-mini"
    summarizer_model: str = "gpt-4o-mini"
    reporter_model: str = "gpt-4o-mini"
    
    # ==================== API Configuration ====================
    openai_api_key: Optional[str] = None
    openai_base_url: Optional[str] = None
    
    # ==================== Report Service Configuration ====================
    report_service_url: str = "https://console-playground.fed.chehejia.com"
    upload_service_url: str = "https://xuanji-backend-service-dev.inner.chj.cloud/api/v1/ois/upload"
    report_timeout: int = 300
    
    # ==================== MCP Configuration ====================
    mcp_server_url: Optional[str] = None
    mcp_timeout: int = 300
    
    # ==================== Workflow Configuration ====================
    max_iterations: int = 10
    timeout_seconds: int = 300
    max_planning_rounds: int = 3
    max_clarification_rounds: int = 3
    
    # ==================== Logging Configuration ====================
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # ==================== Security Configuration ====================
    jwt_secret_key: str = "brand_event"
    jwt_algorithm: str = "HS256"
    jwt_expire_minutes: int = 2880  # 48 hours
    
    # ==================== Additional Configuration ====================
    additional_config: Dict[str, Any] = field(default_factory=dict)
    
    @classmethod
    def from_env(cls) -> "BrandEventConfig":
        """
        Load configuration from environment variables.
        
        Returns:
            Configuration instance loaded from environment
        """
        return cls(
            # Model configuration
            supervisor_model=os.getenv("SUPERVISOR_MODEL", "gpt-4o-mini"),
            analyst_model=os.getenv("ANALYST_MODEL", "gpt-4o-mini"),
            planner_model=os.getenv("PLANNER_MODEL", "gpt-4o-mini"),
            executor_model=os.getenv("EXECUTOR_MODEL", "gpt-4o-mini"),
            summarizer_model=os.getenv("SUMMARIZER_MODEL", "gpt-4o-mini"),
            reporter_model=os.getenv("REPORTER_MODEL", "gpt-4o-mini"),
            
            # API configuration
            openai_api_key=os.getenv("OPENAI_API_KEY"),
            openai_base_url=os.getenv("OPENAI_BASE_URL"),
            
            # Report service configuration
            report_service_url=os.getenv(
                "REPORT_SERVICE_URL", 
                "https://console-playground.fed.chehejia.com"
            ),
            upload_service_url=os.getenv(
                "UPLOAD_SERVICE_URL",
                "https://xuanji-backend-service-dev.inner.chj.cloud/api/v1/ois/upload"
            ),
            report_timeout=int(os.getenv("REPORT_TIMEOUT", "300")),
            
            # MCP configuration
            mcp_server_url=os.getenv("MCP_SERVER_URL"),
            mcp_timeout=int(os.getenv("MCP_TIMEOUT", "300")),
            
            # Workflow configuration
            max_iterations=int(os.getenv("MAX_ITERATIONS", "10")),
            timeout_seconds=int(os.getenv("TIMEOUT_SECONDS", "300")),
            max_planning_rounds=int(os.getenv("MAX_PLANNING_ROUNDS", "3")),
            max_clarification_rounds=int(os.getenv("MAX_CLARIFICATION_ROUNDS", "3")),
            
            # Logging configuration
            log_level=os.getenv("LOG_LEVEL", "INFO"),
            log_format=os.getenv(
                "LOG_FORMAT", 
                "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
            ),
            
            # Security configuration
            jwt_secret_key=os.getenv("JWT_SECRET_KEY", "brand_event"),
            jwt_algorithm=os.getenv("JWT_ALGORITHM", "HS256"),
            jwt_expire_minutes=int(os.getenv("JWT_EXPIRE_MINUTES", "2880")),
        )
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "BrandEventConfig":
        """
        Load configuration from dictionary.
        
        Args:
            config_dict: Configuration dictionary
            
        Returns:
            Configuration instance
        """
        # Extract known fields
        known_fields = {
            field.name for field in cls.__dataclass_fields__.values()
            if field.name != "additional_config"
        }
        
        # Separate known and unknown fields
        known_config = {k: v for k, v in config_dict.items() if k in known_fields}
        additional_config = {k: v for k, v in config_dict.items() if k not in known_fields}
        
        return cls(
            **known_config,
            additional_config=additional_config
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert configuration to dictionary.
        
        Returns:
            Configuration as dictionary
        """
        result = {}
        
        # Add all fields except additional_config
        for field_name, field_def in self.__dataclass_fields__.items():
            if field_name != "additional_config":
                result[field_name] = getattr(self, field_name)
        
        # Add additional config
        result.update(self.additional_config)
        
        return result
    
    def get_llm_config(self, agent_type: str) -> Dict[str, Any]:
        """
        Get LLM configuration for specific agent type.
        
        Args:
            agent_type: Type of agent (supervisor, analyst, planner, etc.)
            
        Returns:
            LLM configuration dictionary
        """
        model_field = f"{agent_type}_model"
        model_name = getattr(self, model_field, "gpt-4o-mini")
        
        config = {
            "model": model_name,
            "temperature": 0,
        }
        
        # Add API configuration if available
        if self.openai_api_key:
            config["api_key"] = self.openai_api_key
        
        if self.openai_base_url:
            config["base_url"] = self.openai_base_url
        
        return config
    
    def get_report_config(self) -> Dict[str, Any]:
        """
        Get report service configuration.
        
        Returns:
            Report service configuration
        """
        return {
            "base_url": self.report_service_url,
            "upload_url": self.upload_service_url,
            "timeout": self.report_timeout
        }
    
    def get_mcp_config(self) -> Dict[str, Any]:
        """
        Get MCP configuration.
        
        Returns:
            MCP configuration
        """
        return {
            "server_url": self.mcp_server_url,
            "timeout": self.mcp_timeout
        }
    
    def get_jwt_config(self) -> Dict[str, Any]:
        """
        Get JWT configuration.
        
        Returns:
            JWT configuration
        """
        return {
            "secret_key": self.jwt_secret_key,
            "algorithm": self.jwt_algorithm,
            "expire_minutes": self.jwt_expire_minutes
        }

# ==================== Default Configuration ====================

DEFAULT_CONFIG = BrandEventConfig()

def get_default_config() -> BrandEventConfig:
    """
    Get default configuration instance.
    
    Returns:
        Default configuration
    """
    return DEFAULT_CONFIG

def load_config_from_env() -> BrandEventConfig:
    """
    Load configuration from environment variables.

    Returns:
        Configuration loaded from environment
    """
    return BrandEventConfig.from_env()

# ==================== Configuration Validation ====================

def validate_config(config: BrandEventConfig) -> bool:
    """
    Validate configuration settings.

    Args:
        config: Configuration to validate

    Returns:
        True if configuration is valid

    Raises:
        ValueError: If configuration is invalid
    """
    # Validate model names
    valid_models = [
        "gpt-4o-mini", "gpt-4o", "gpt-3.5-turbo",
        "claude-3-sonnet", "claude-3-haiku"
    ]

    model_fields = [
        "supervisor_model", "analyst_model", "planner_model",
        "executor_model", "summarizer_model", "reporter_model"
    ]

    for field in model_fields:
        model_name = getattr(config, field)
        if model_name not in valid_models:
            logger.warning(f"Unknown model name: {model_name} for field {field}")

    # Validate URLs
    if config.report_service_url and not config.report_service_url.startswith(("http://", "https://")):
        raise ValueError(f"Invalid report service URL: {config.report_service_url}")

    if config.upload_service_url and not config.upload_service_url.startswith(("http://", "https://")):
        raise ValueError(f"Invalid upload service URL: {config.upload_service_url}")

    # Validate timeouts
    if config.report_timeout <= 0:
        raise ValueError(f"Invalid report timeout: {config.report_timeout}")

    if config.timeout_seconds <= 0:
        raise ValueError(f"Invalid timeout seconds: {config.timeout_seconds}")

    # Validate iteration limits
    if config.max_iterations <= 0:
        raise ValueError(f"Invalid max iterations: {config.max_iterations}")

    if config.max_planning_rounds <= 0:
        raise ValueError(f"Invalid max planning rounds: {config.max_planning_rounds}")

    if config.max_clarification_rounds <= 0:
        raise ValueError(f"Invalid max clarification rounds: {config.max_clarification_rounds}")

    return True

# ==================== Configuration from RunnableConfig ====================

def add_from_runnable_config_method():
    """
    Add from_runnable_config method to BrandEventConfig class.

    Following open_deep_research pattern for configuration handling.
    """
    @classmethod
    def from_runnable_config(cls, config: Optional["RunnableConfig"] = None) -> "BrandEventConfig":
        """
        Create a BrandEventConfig instance from a RunnableConfig.

        Following open_deep_research pattern for configuration handling.

        Args:
            config: Optional RunnableConfig instance

        Returns:
            BrandEventConfig instance
        """
        from langchain_core.runnables import RunnableConfig
        from dataclasses import fields

        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )

        # Get field values from environment or configurable
        values: Dict[str, Any] = {}
        for field in fields(cls):
            if field.init:
                # Try configurable first, then environment, then default
                env_key = field.name.upper()
                value = configurable.get(field.name) or os.environ.get(env_key)
                if value is not None:
                    # Convert to appropriate type
                    if field.type == int:
                        value = int(value)
                    elif field.type == bool:
                        value = str(value).lower() == "true"
                    values[field.name] = value

        return cls(**{k: v for k, v in values.items() if v is not None})

    # Add the method to the class
    BrandEventConfig.from_runnable_config = from_runnable_config

# Apply the method addition
add_from_runnable_config_method()
