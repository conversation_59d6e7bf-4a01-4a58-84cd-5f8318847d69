"""
Configuration management for brand event analysis workflow.

This module provides configuration classes following open_deep_research patterns.
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass, fields
from langchain_core.runnables import RunnableConfig

@dataclass(kw_only=True)
class WorkflowConfiguration:
    """Configuration for brand event analysis workflow."""
    
    # Model configuration
    supervisor_model: str = "gpt-4o-mini"
    analyst_model: str = "gpt-4o-mini"
    planner_model: str = "gpt-4o-mini"
    executor_model: str = "gpt-4o-mini"
    summarizer_model: str = "gpt-4o-mini"
    reporter_model: str = "gpt-4o-mini"
    
    # API configuration
    openai_api_key: Optional[str] = None
    openai_base_url: Optional[str] = None
    
    # Report service configuration
    report_service_url: str = "https://console-playground.fed.chehejia.com"
    upload_service_url: str = "https://xuanji-backend-service-dev.inner.chj.cloud/api/v1/ois/upload"
    report_timeout: int = 300
    
    # MCP configuration
    mcp_server_url: Optional[str] = None
    mcp_timeout: int = 300
    
    # Workflow configuration
    max_iterations: int = 10
    timeout_seconds: int = 300
    max_planning_rounds: int = 3
    max_clarification_rounds: int = 3
    
    # Logging configuration
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # Security configuration
    jwt_secret_key: str = "brand_event"
    jwt_algorithm: str = "HS256"
    jwt_expire_minutes: int = 2880  # 48 hours
    
    @classmethod
    def from_runnable_config(
        cls, config: Optional[RunnableConfig] = None
    ) -> "WorkflowConfiguration":
        """Create a BrandEventConfig instance from a RunnableConfig."""
        configurable = (
            config["configurable"] if config and "configurable" in config else {}
        )
        values: dict[str, Any] = {
            f.name: os.environ.get(f.name.upper(), configurable.get(f.name))
            for f in fields(cls)
            if f.init
        }
        return cls(**{k: v for k, v in values.items() if v})
