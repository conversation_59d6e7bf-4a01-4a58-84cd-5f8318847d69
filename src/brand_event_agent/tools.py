"""
Tool functions for brand event analysis workflow.

These functions are extracted from existing Agent implementations
to maintain complete compatibility.
"""

import os
import tempfile
import httpx
from datetime import datetime
from typing import Dict, Any, Optional
from jose import jwt

from .utils import get_logger

logger = get_logger("BrandEventTools")

# ==================== Authentication Tools ====================

def create_access_token(user_id: str) -> str:
    """
    Create JWT access token for MCP authentication.
    
    Extracted from existing implementation to maintain compatibility.
    
    Args:
        user_id: User identifier
        
    Returns:
        JWT access token
    """
    try:
        # JWT configuration - identical to existing implementation
        SECRET_KEY = "brand_event"
        ALGORITHM = "HS256"
        ACCESS_TOKEN_EXPIRE_MINUTES = 2880  # 48 hours
        
        # Create token payload
        payload = {
            "user_id": user_id,
            "exp": datetime.utcnow().timestamp() + (ACCESS_TOKEN_EXPIRE_MINUTES * 60)
        }
        
        # Generate token
        token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
        
        logger.info(f"Access token created for user: {user_id}")
        return token
        
    except Exception as e:
        logger.error(f"Failed to create access token: {e}")
        raise e

# ==================== Report Generation Tools ====================

async def generate_report(
    report_dsl: Dict[str, Any],
    base_url: str = "https://console-playground.fed.chehejia.com",
    timeout: int = 300
) -> Dict[str, Any]:
    """
    Call external report service to generate report.
    
    Extracted from ReportAgent to maintain compatibility.
    
    Args:
        report_dsl: Report DSL data structure
        base_url: Report service base URL
        timeout: Request timeout in seconds
        
    Returns:
        Report generation result
    """
    try:
        logger.info("Starting report generation")
        
        # Build request URL
        url = f"{base_url}/__ui/report"
        
        # Build request headers
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer api-key:eyJhbGciOiJIUzI1NiJ9.eyJzIjoidWkiLCJpYXQiOjE3NDk2MzI5NDgsImlzcyI6IjdONVg5MHNkOUtuZGlDS1Q1VjlKWGwifQ.yKQahvhNPXjGCfmFHEJVl9iMWqGodm_QJ8GHjKxUuHI",
        }
        
        # Send request
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                url=url,
                headers=headers,
                json=report_dsl
            )
            
            # Check response status
            if response.status_code == 200:
                # Interface returns HTML format report
                html_content = response.text
                logger.info("Report generated successfully")
                return {
                    "success": True,
                    "html_content": html_content,
                    "content_type": "text/html",
                    "message": "报告生成成功"
                }
            else:
                logger.error(f"Report generation failed: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}",
                    "message": "报告生成失败"
                }
                
    except httpx.TimeoutException:
        logger.error("Report generation timeout")
        return {
            "success": False,
            "error": "Request timeout",
            "message": "报告生成超时"
        }
    except Exception as e:
        logger.error(f"Report generation error: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": f"报告生成失败：{str(e)}"
        }

async def upload_html_to_s3(
    html_content: str,
    filename: Optional[str] = None,
    upload_url: str = "https://xuanji-backend-service-dev.inner.chj.cloud/api/v1/ois/upload",
    timeout: int = 300
) -> Dict[str, Any]:
    """
    Upload HTML content to S3 storage.
    
    Extracted from ReportAgent to maintain compatibility.
    
    Args:
        html_content: HTML content to upload
        filename: File name (optional, auto-generated if not provided)
        upload_url: Upload service URL
        timeout: Request timeout in seconds
        
    Returns:
        Upload result
    """
    try:
        logger.info("Starting HTML upload to S3")
        
        # Generate filename if not provided
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d-%H%M%S')
            filename = f"report-{timestamp}.html"
        
        # Create temporary file
        temp_file_path = os.path.join(tempfile.gettempdir(), filename)
        
        # Write HTML content to temporary file
        with open(temp_file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        try:
            # Prepare upload file
            with open(temp_file_path, 'rb') as file:
                files = {
                    'file': (filename, file, 'text/html')
                }
                
                # Send upload request
                async with httpx.AsyncClient(timeout=timeout) as client:
                    response = await client.post(
                        url=upload_url,
                        files=files
                    )
                    
                    # Check response status
                    if response.status_code == 200:
                        result = response.json()
                        logger.info(f"HTML uploaded successfully: {result}")
                        return {
                            "success": True,
                            "upload_result": result,
                            "filename": filename,
                            "message": "HTML报告上传成功"
                        }
                    else:
                        logger.error(f"HTML upload failed: {response.status_code} - {response.text}")
                        return {
                            "success": False,
                            "error": f"HTTP {response.status_code}: {response.text}",
                            "message": "HTML报告上传失败"
                        }
                        
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                
    except Exception as e:
        logger.error(f"HTML upload error: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": f"HTML报告上传失败：{str(e)}"
        }

# ==================== DSL Processing Tools ====================

def parse_dsl_summary(report_dsl: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse AI summary and key data from DSL.
    
    Extracted from ReportAgent to maintain compatibility.
    
    Args:
        report_dsl: Report DSL data structure
        
    Returns:
        Parsed summary data
    """
    try:
        logger.info("Parsing DSL summary data")
        
        # Extract AI summary from DSL structure
        ai_summary = ""
        key_metrics = {}
        
        # Parse DSL sections for summary content
        sections = report_dsl.get("sections", {})
        
        for section_id, section_data in sections.items():
            if isinstance(section_data, dict):
                section_type = section_data.get("type", "")
                section_title = section_data.get("title", "")
                
                # Extract summary from summary sections
                if "summary" in section_type.lower() or "总结" in section_title:
                    content = section_data.get("content", [])
                    if content and isinstance(content, list):
                        for item in content:
                            if isinstance(item, dict) and item.get("type") == "text":
                                ai_summary += item.get("data", "") + "\n"
                
                # Extract metrics from metrics sections
                elif "metrics" in section_type.lower() or "指标" in section_title:
                    content = section_data.get("content", [])
                    if content and isinstance(content, list):
                        for item in content:
                            if isinstance(item, dict) and item.get("type") == "metrics":
                                metrics_data = item.get("data", [])
                                for metric in metrics_data:
                                    if isinstance(metric, dict):
                                        label = metric.get("label", "")
                                        value = metric.get("value", "")
                                        if label and value:
                                            key_metrics[label] = value
        
        # Default summary if none found
        if not ai_summary.strip():
            ai_summary = "品牌舆情分析报告已生成，包含详细的数据分析和洞察。"
        
        result = {
            "ai_summary": ai_summary.strip(),
            "key_metrics": key_metrics,
            "sections_count": len(sections),
            "report_type": "brand_sentiment_analysis"
        }
        
        logger.info("DSL summary parsing completed")
        return result

    except Exception as e:
        logger.error(f"Error parsing DSL summary: {e}")
        return {
            "ai_summary": "报告解析过程中出现错误，请查看完整报告获取详细信息。",
            "key_metrics": {},
            "sections_count": 0,
            "report_type": "brand_sentiment_analysis"
        }

def create_brand_analysis_dsl() -> Dict[str, Any]:
    """
    Create default brand analysis DSL structure.

    Extracted from ReportAgent to maintain compatibility.

    Returns:
        Default brand analysis DSL
    """
    return {
        "title": "品牌舆情分析报告",
        "description": "基于多维度数据的品牌舆情深度分析",
        "sections": {
            "01": {
                "type": "summary",
                "title": "执行摘要",
                "description": "品牌舆情分析核心发现",
                "content": [
                    {
                        "type": "text",
                        "data": "本报告基于全网数据采集和AI智能分析，为品牌提供全面的舆情洞察。通过多维度数据分析，识别品牌传播趋势、用户情感倾向和潜在风险机会。"
                    }
                ]
            },
            "02": {
                "type": "analysis",
                "title": "详细分析",
                "description": "品牌舆情多维度深度分析",
                "content": [
                    {
                        "type": "chart",
                        "chart_type": "line",
                        "title": "舆情趋势分析",
                        "data": {
                            "labels": ["第1周", "第2周", "第3周", "第4周"],
                            "datasets": [
                                {
                                    "label": "正面舆情",
                                    "data": [65, 70, 75, 80],
                                    "borderColor": "#10B981"
                                },
                                {
                                    "label": "负面舆情",
                                    "data": [20, 15, 12, 10],
                                    "borderColor": "#EF4444"
                                }
                            ]
                        }
                    }
                ]
            },
            "03": {
                "type": "section",
                "title": "关键指标",
                "description": "品牌舆情关键性能指标",
                "content": [
                    {
                        "type": "metrics",
                        "data": [
                            {"label": "舆情健康度", "value": "85%", "trend": "up"},
                            {"label": "传播声量", "value": "12,450", "trend": "up"},
                            {"label": "正面占比", "value": "78.5%", "trend": "stable"},
                            {"label": "负面占比", "value": "12.3%", "trend": "down"}
                        ]
                    }
                ]
            }
        }
    }
