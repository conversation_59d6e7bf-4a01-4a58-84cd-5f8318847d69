"""Command line interface for Specific V3 system."""

import asyncio
import logging
import sys
from typing import Optional
import uuid

from .api.main import SpecificV3API
from .api.schemas import ChatRequest


class SpecificV3CLI:
    """Command line interface for Specific V3 system."""
    
    def __init__(self):
        self.api = SpecificV3API()
        self.session_id: Optional[str] = None
        self.logger = logging.getLogger("specific_v3.cli")
    
    async def start_interactive_session(self):
        """Start interactive chat session."""
        print("🚀 欢迎使用 Specific V3 多智能体系统！")
        print("=" * 60)
        print("💡 输入您的任务需求，我将为您提供专业的分析和执行服务")
        print("📝 输入 'help' 查看帮助，输入 'quit' 退出")
        print("=" * 60)
        
        # 显示系统信息
        await self._show_system_info()
        
        print("\n开始对话...")
        print("-" * 40)
        
        while True:
            try:
                # 获取用户输入
                user_input = input("\n👤 您: ").strip()
                
                if not user_input:
                    continue
                
                # 处理特殊命令
                if user_input.lower() == 'quit':
                    print("👋 再见！")
                    break
                elif user_input.lower() == 'help':
                    self._show_help()
                    continue
                elif user_input.lower() == 'status':
                    await self._show_session_status()
                    continue
                elif user_input.lower() == 'tools':
                    await self._show_tools()
                    continue
                elif user_input.lower() == 'agents':
                    await self._show_agents()
                    continue
                elif user_input.lower() == 'new':
                    self.session_id = None
                    print("🔄 开始新会话")
                    continue
                
                # 处理聊天请求
                await self._handle_chat(user_input)
                
            except KeyboardInterrupt:
                print("\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 错误: {e}")
                self.logger.error(f"CLI error: {e}")
    
    async def _show_system_info(self):
        """显示系统信息."""
        try:
            status = await self.api.get_system_status()
            print(f"📊 系统状态: {status.status}")
            print(f"🔧 可用工具: {status.tools_available}")
            print(f"🤖 活跃Agent: {status.agents_active}")
            print(f"⏱️  运行时间: {status.uptime}")
        except Exception as e:
            print(f"⚠️  无法获取系统状态: {e}")
    
    async def _handle_chat(self, user_input: str):
        """处理聊天请求."""
        try:
            # 创建聊天请求
            request = ChatRequest(
                message=user_input,
                session_id=self.session_id,
                user_id="cli_user"
            )
            
            print("🤔 正在思考...")
            
            # 发送请求
            response = await self.api.chat(request)
            
            # 更新会话ID
            self.session_id = response.session_id
            
            # 显示回复
            print(f"\n🤖 系统: {response.response}")
            
            # 显示状态信息
            if response.status != "unknown":
                print(f"📊 状态: {response.status}")
            
            if response.requires_feedback:
                print("💬 系统正在等待您的回复...")
            
            # 显示元数据
            if response.metadata:
                if "step_index" in response.metadata:
                    step = response.metadata.get("step_index", 0)
                    total = response.metadata.get("total_steps", 0)
                    if total > 0:
                        progress = (step / total) * 100
                        print(f"📈 进度: {progress:.1f}% ({step}/{total})")
                        
        except Exception as e:
            print(f"❌ 处理请求时出错: {e}")
            self.logger.error(f"Chat error: {e}")
    
    async def _show_session_status(self):
        """显示会话状态."""
        if not self.session_id:
            print("ℹ️  当前没有活跃会话")
            return
        
        try:
            session_info = await self.api.get_session_info(self.session_id)
            if session_info:
                print(f"\n📋 会话信息:")
                print(f"  ID: {session_info.session_id}")
                print(f"  状态: {session_info.status}")
                print(f"  消息数: {session_info.message_count}")
                print(f"  创建时间: {session_info.created_at}")
                print(f"  更新时间: {session_info.updated_at}")
            
            workflow_status = await self.api.get_workflow_status(self.session_id)
            if workflow_status:
                print(f"\n⚙️ 工作流状态:")
                print(f"  当前步骤: {workflow_status.current_step}")
                print(f"  进度: {workflow_status.progress:.1%}")
                print(f"  已完成: {workflow_status.steps_completed}/{workflow_status.total_steps}")
                
        except Exception as e:
            print(f"❌ 获取会话状态失败: {e}")
    
    async def _show_tools(self):
        """显示可用工具."""
        try:
            tools = await self.api.get_available_tools()
            print(f"\n🔧 可用工具 ({len(tools)} 个):")
            
            # 按类别分组
            categories = {}
            for tool in tools:
                if tool.category not in categories:
                    categories[tool.category] = []
                categories[tool.category].append(tool)
            
            for category, category_tools in categories.items():
                print(f"\n  📂 {category.title()}:")
                for tool in category_tools:
                    status = "✅" if tool.available else "❌"
                    print(f"    {status} {tool.name}: {tool.description}")
                    
        except Exception as e:
            print(f"❌ 获取工具列表失败: {e}")
    
    async def _show_agents(self):
        """显示Agent信息."""
        try:
            agents = await self.api.get_agents_info()
            print(f"\n🤖 Agent信息 ({len(agents)} 个):")
            
            for agent in agents:
                status = "🟢" if agent.status == "active" else "🔴"
                print(f"\n  {status} {agent.name}")
                print(f"    角色: {agent.role}")
                print(f"    描述: {agent.description}")
                print(f"    工具: {', '.join(agent.tools)}")
                
        except Exception as e:
            print(f"❌ 获取Agent信息失败: {e}")
    
    def _show_help(self):
        """显示帮助信息."""
        print("""
📖 帮助信息:

🗣️  聊天命令:
  - 直接输入您的需求开始对话
  - 系统会智能分析您的意图并制定执行计划

🔧 系统命令:
  - help     显示此帮助信息
  - status   显示当前会话状态
  - tools    显示可用工具列表
  - agents   显示Agent信息
  - new      开始新会话
  - quit     退出程序

💡 使用技巧:
  - 尽量提供具体、明确的需求描述
  - 如果系统询问澄清问题，请详细回答
  - 可以随时查看状态了解执行进度

📝 示例:
  "分析2025年京东外卖与美团外卖的竞争情况"
  "帮我研究人工智能行业的发展趋势"
  "对比苹果和三星手机的市场表现"
        """)
    
    async def run_single_command(self, command: str):
        """Run a single command (for testing)."""
        request = ChatRequest(
            message=command,
            user_id="test_user"
        )
        
        response = await self.api.chat(request)
        return response


async def main():
    """Main CLI entry point."""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 创建CLI实例
    cli = SpecificV3CLI()
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        # 单命令模式
        command = " ".join(sys.argv[1:])
        response = await cli.run_single_command(command)
        print(f"Response: {response.response}")
        print(f"Status: {response.status}")
    else:
        # 交互模式
        await cli.start_interactive_session()


if __name__ == "__main__":
    asyncio.run(main())
