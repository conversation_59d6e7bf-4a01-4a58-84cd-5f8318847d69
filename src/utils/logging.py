"""
Unified logging utilities for the Specific multi-agent system.

This module provides standardized logging formats and utilities to ensure
consistent logging across all components of the system.
"""

import logging
from typing import Optional, Any, Dict
from functools import wraps


class SpecificLogger:
    """
    Standardized logger for the Specific system.
    
    Provides consistent formatting with session ID and component context.
    """
    
    def __init__(self, component_name: str, logger_name: Optional[str] = None):
        """
        Initialize the logger.
        
        Args:
            component_name: Name of the component (e.g., 'IntentAnalysisAgent', 'API', 'Workflow')
            logger_name: Custom logger name, defaults to 'specific.{component_name.lower()}'
        """
        self.component_name = component_name
        self.logger_name = logger_name or f"specific.{component_name.lower()}"
        self.logger = logging.getLogger(self.logger_name)
    
    def _format_message(self, message: str, session_id: Optional[str] = None, 
                       context: Optional[Dict[str, Any]] = None) -> str:
        """
        Format log message with standard prefix.
        
        Args:
            message: The log message
            session_id: Optional session ID
            context: Optional additional context
            
        Returns:
            Formatted message string
        """
        parts = []
        
        if session_id:
            parts.append(f"[Session:{session_id}]")
        
        parts.append(f"[{self.component_name}]")
        
        if context:
            for key, value in context.items():
                parts.append(f"[{key}:{value}]")
        
        parts.append(message)
        return " ".join(parts)
    
    def debug(self, message: str, session_id: Optional[str] = None, 
              context: Optional[Dict[str, Any]] = None):
        """Log debug message."""
        formatted_msg = self._format_message(message, session_id, context)
        self.logger.debug(formatted_msg)
    
    def info(self, message: str, session_id: Optional[str] = None, 
             context: Optional[Dict[str, Any]] = None):
        """Log info message."""
        formatted_msg = self._format_message(message, session_id, context)
        self.logger.info(formatted_msg)
    
    def warning(self, message: str, session_id: Optional[str] = None, 
                context: Optional[Dict[str, Any]] = None):
        """Log warning message."""
        formatted_msg = self._format_message(message, session_id, context)
        self.logger.warning(formatted_msg)
    
    def error(self, message: str, session_id: Optional[str] = None, 
              context: Optional[Dict[str, Any]] = None, exc_info: bool = False):
        """Log error message."""
        formatted_msg = self._format_message(message, session_id, context)
        self.logger.error(formatted_msg, exc_info=exc_info)
    
    def critical(self, message: str, session_id: Optional[str] = None, 
                 context: Optional[Dict[str, Any]] = None):
        """Log critical message."""
        formatted_msg = self._format_message(message, session_id, context)
        self.logger.critical(formatted_msg)


def get_logger(component_name: str, logger_name: Optional[str] = None) -> SpecificLogger:
    """
    Get a standardized logger for a component.
    
    Args:
        component_name: Name of the component
        logger_name: Optional custom logger name
        
    Returns:
        SpecificLogger instance
    """
    return SpecificLogger(component_name, logger_name)


def log_operation(operation_name: str, include_args: bool = False):
    """
    Decorator to automatically log operation start and completion.
    
    Args:
        operation_name: Name of the operation being logged
        include_args: Whether to include function arguments in logs
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Try to extract session_id from args/kwargs
            session_id = None
            if hasattr(args[0], 'session_id'):
                session_id = getattr(args[0], 'session_id', None)
            elif 'session_id' in kwargs:
                session_id = kwargs['session_id']
            elif len(args) > 1 and isinstance(args[1], dict) and 'session_id' in args[1]:
                session_id = args[1]['session_id']
            
            # Get logger from the class if available
            logger = None
            if hasattr(args[0], 'logger') and isinstance(args[0].logger, SpecificLogger):
                logger = args[0].logger
            elif hasattr(args[0], 'logger'):
                # Convert standard logger to SpecificLogger
                component_name = getattr(args[0], 'role_name', args[0].__class__.__name__)
                logger = SpecificLogger(component_name)
            else:
                logger = SpecificLogger(func.__name__)
            
            # Log start
            start_msg = f"Starting {operation_name}"
            if include_args and args[1:]:
                start_msg += f" with args: {args[1:]}"
            logger.info(start_msg, session_id)
            
            try:
                result = func(*args, **kwargs)
                logger.info(f"Completed {operation_name} successfully", session_id)
                return result
            except Exception as e:
                logger.error(f"Failed {operation_name}: {str(e)}", session_id, exc_info=True)
                raise
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Try to extract session_id from args/kwargs
            session_id = None
            if hasattr(args[0], 'session_id'):
                session_id = getattr(args[0], 'session_id', None)
            elif 'session_id' in kwargs:
                session_id = kwargs['session_id']
            elif len(args) > 1 and isinstance(args[1], dict) and 'session_id' in args[1]:
                session_id = args[1]['session_id']
            
            # Get logger from the class if available
            logger = None
            if hasattr(args[0], 'logger') and isinstance(args[0].logger, SpecificLogger):
                logger = args[0].logger
            elif hasattr(args[0], 'logger'):
                # Convert standard logger to SpecificLogger
                component_name = getattr(args[0], 'role_name', args[0].__class__.__name__)
                logger = SpecificLogger(component_name)
            else:
                logger = SpecificLogger(func.__name__)
            
            # Log start
            start_msg = f"Starting {operation_name}"
            if include_args and args[1:]:
                start_msg += f" with args: {args[1:]}"
            logger.info(start_msg, session_id)
            
            try:
                result = await func(*args, **kwargs)
                logger.info(f"Completed {operation_name} successfully", session_id)
                return result
            except Exception as e:
                logger.error(f"Failed {operation_name}: {str(e)}", session_id, exc_info=True)
                raise
        
        # Return appropriate wrapper based on whether function is async
        import asyncio
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return wrapper
    
    return decorator


# Standard log messages for common operations
class LogMessages:
    """Standard log message templates."""
    
    # Agent operations
    AGENT_START = "Starting agent execution"
    AGENT_COMPLETE = "Agent execution completed"
    AGENT_ROUTE = "Routing to {destination}"
    AGENT_ERROR = "Agent execution failed: {error}"
    
    # API operations
    API_REQUEST_START = "Processing API request"
    API_REQUEST_COMPLETE = "API request completed"
    API_REQUEST_ERROR = "API request failed: {error}"
    
    # Workflow operations
    WORKFLOW_START = "Starting workflow"
    WORKFLOW_COMPLETE = "Workflow completed"
    WORKFLOW_ERROR = "Workflow failed: {error}"
    
    # System operations
    SYSTEM_INIT = "System component initialized"
    SYSTEM_SHUTDOWN = "System component shutdown"
    SYSTEM_ERROR = "System error: {error}"


def setup_logging(level: str = "INFO", format_string: Optional[str] = None):
    """
    Setup basic logging configuration for the entire application.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        format_string: Custom format string, uses default if None
    """
    if format_string is None:
        format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format=format_string,
        datefmt='%Y-%m-%d %H:%M:%S'
    )
