"""Main API for Specific V4 system."""

import uuid
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from .schemas import (
    ChatRequest, ChatResponse, SessionInfo, SystemStatus,
    WorkflowStatus, HealthCheck, ToolInfo, AgentInfo
)
from src.core.workflow import SpecificWorkflow
from src.models.state import create_initial_state
from src.event.manus_event_handler import event_handler

class SpecificAPI:
    """Main API class for Specific multi-agent system."""

    def __init__(self, checkpointer=None):
        self.logger = logging.getLogger("specific_v4.api")
        self.start_time = datetime.now()

        # Store for later use
        self.create_initial_state = create_initial_state
        self.event_handler = event_handler

        # Initialize workflow with V4 architecture
        self.workflow = SpecificWorkflow(checkpointer=checkpointer)

        # Session tracking
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.total_sessions = 0

        self.logger.info("Specific API initialized successfully")
    
    async def chat(self, request: ChatRequest) -> ChatResponse:
        """Handle chat request."""
        try:
            # Generate or use existing session ID
            session_id = request.session_id or str(uuid.uuid4())

            self.logger.info(f"Processing chat request for session {session_id}")

            # Check if this is a new session
            if session_id not in self.active_sessions:
                # Create new session with extended information
                self.active_sessions[session_id] = {
                    "created_at": datetime.now(),
                    "user_id": request.user_id,
                    "task_id": request.task_id,
                    "sandbox_id": request.sandbox_id,
                    "event_webhook": request.event_webhook,
                    "extensions": request.extensions.dict() if request.extensions else None,
                    "message_count": 1,
                    "workflow_status": request.workflow_status,
                }
                self.total_sessions += 1

                # Create initial state with extended context
                initial_state = self.create_initial_state(
                    session_id=session_id,
                    user_input=request.message,
                    user_id=request.user_id or "anonymous",
                    task_id=request.task_id,
                    sandbox_id=request.sandbox_id,
                    event_webhook=request.event_webhook,
                    extensions=request.extensions,
                    report_dsl=request.report_dsl,
                    workflow_status=request.workflow_status
                )

                # Run workflow with streaming
                config = {"configurable": {"thread_id": session_id}}
                final_state = await self._process_workflow_stream(initial_state, config, initial_state)

            else:
                # Continue existing session
                self.active_sessions[session_id]["message_count"] += 1

                # Update session with new request data if provided
                if request.workflow_status:
                    self.active_sessions[session_id]["workflow_status"] = request.workflow_status

                # Get current state and update it with new data
                config = {"configurable": {"thread_id": session_id}}
                current_state = self.workflow.graph.get_state(config)
                state_for_events = current_state.values if current_state else {}

                # Add user message to continue conversation
                from langchain_core.messages import HumanMessage
                user_message = {"messages": [HumanMessage(content=request.message)]}

                # Update state with new report_dsl and workflow_status if provided
                if request.report_dsl or request.workflow_status:
                    update_data = {}
                    if request.report_dsl:
                        update_data["report_dsl"] = request.report_dsl
                    if request.workflow_status:
                        update_data["workflow_status"] = request.workflow_status

                    # Merge user message with state updates
                    user_message.update(update_data)

                # Continue workflow with streaming
                final_state = await self._process_workflow_stream(user_message, config, state_for_events)

            # 使用从astream获取的最终状态
            complete_state = final_state.values if final_state else {}

            # Generate response from complete state (simplified)
            response = self._generate_simple_response(complete_state, session_id, request)

            self.logger.info(f"Chat request completed for session {session_id}")
            return response
            
        except Exception as e:
            self.logger.error(f"Error processing chat request: {e}")
            
            error_response = ChatResponse(
                session_id=request.session_id or "error",
                response=f"抱歉，处理您的请求时发生了错误：{str(e)}",
                status="error",
                requires_feedback=False,
                task_id=request.task_id,
                sandbox_id=request.sandbox_id,
                event_webhook=request.event_webhook,
                extensions=request.extensions.dict() if request.extensions else None,
                metadata={"error": str(e)}
            )
            
            return error_response

    async def _process_workflow_stream(self, input_data, config, state_for_events):
        """处理工作流流式消息的公共方法"""
        final_state = None
        async for chunk in self.workflow.graph.astream(input_data, config=config, stream_mode="custom"):
            if "live_status_message" in chunk:
                # 显示实时状态
                self.event_handler.send_live_status(chunk['live_status_message'], state=state_for_events)
            elif "agent_message" in chunk:
                # 显示Agent消息
                self.event_handler.send_agent_message(state_for_events, chunk['agent_message'])
            elif "agent_message_with_file" in chunk:
                # 显示Agent消息
                self.event_handler.send_agent_message(
                    state_for_events,
                    chunk['agent_message_with_file']["content"],
                    attachments=chunk['agent_message_with_file']['attachments']
                )
            elif "human_feedback_message" in chunk:
                # 显示人工审批状态
                self.event_handler.send_agent_status(
                    status="idle",
                    brief="等待用户确认",
                    description=chunk['human_feedback_message'],
                    state=state_for_events
                )
            elif "agent_status_waiting" in chunk:
                # 显示等待状态
                self.event_handler.send_agent_status(
                    status="waiting",
                    brief="等待用户确认",
                    description=chunk['agent_status_waiting'],
                    state=state_for_events
                )

        # 获取最终状态
        return self.workflow.graph.get_state(config)





    def _generate_simple_response(self, state, session_id: str, request: ChatRequest = None) -> ChatResponse:
        """Generate simplified chat response from workflow state."""
        # Extract basic information from state (handle both dict and SpecificState)
        if isinstance(state, dict):
            workflow_status = state.get("workflow_status", WorkflowStatus.INITIALIZING)
            messages = state.get("messages", [])
        else:
            # SpecificState object
            workflow_status = getattr(state, "workflow_status", WorkflowStatus.INITIALIZING)
            messages = getattr(state, "messages", [])

        # Determine if feedback is required based on workflow status
        requires_feedback = workflow_status in [
            WorkflowStatus.CLARIFYING_INTENT,
            WorkflowStatus.CONFIRMING_PLAN,
            WorkflowStatus.WAITING_FOR_HUMAN
        ]

        # Find the latest message with content (AI or Human from system)
        response = "正在处理您的请求..."
        for msg in reversed(messages):
            if hasattr(msg, 'content') and msg.content.strip():
                response = msg.content
                break

        # Convert workflow status to string
        status_str = workflow_status.value if hasattr(workflow_status, 'value') else str(workflow_status)

        # 发送助手消息事件到第三方平台
        try:
            if response and response != "正在处理您的请求...":
                # self.event_handler.send_assistant_message(
                #     state=state,
                #     content=response
                # )
                # self.logger.debug(f"Sent assistant message event: {response[:50]}...")
                pass
        except Exception as e:
            self.logger.error(f"Error sending assistant message event: {e}")

        return ChatResponse(
            session_id=session_id,
            response=response,
            status=status_str,
            requires_feedback=requires_feedback,
            task_id=request.task_id if request else None,
            sandbox_id=request.sandbox_id if request else None,
            event_webhook=request.event_webhook if request else None,
            extensions=request.extensions.dict() if request and request.extensions else None,
            metadata={
                "updated_at": datetime.now().isoformat(),
                "intent_clarified": state.get("intent_clarified", False) if isinstance(state, dict) else getattr(state, "intent_clarified", False),
                "intent_approved": state.get("intent_approved", False) if isinstance(state, dict) else getattr(state, "intent_approved", False),
                "plan_approved": state.get("plan_approved", False) if isinstance(state, dict) else getattr(state, "plan_approved", False),
                "message_count": len(messages)
            }
        )

    def _generate_response(self, state: Dict[str, Any], session_id: str) -> ChatResponse:
        """Generate chat response from workflow state."""
        from src.models.state import WorkflowStatus

        workflow_status = state.get("workflow_status", WorkflowStatus.INITIALIZING)
        requires_feedback = state.get("human_approval_required", False)

        # Extract the latest AI message from the conversation
        messages = state.get("messages", [])
        response = "正在处理您的请求..."

        # Find the latest AI message
        for msg in reversed(messages):
            if hasattr(msg, 'content') and (str(type(msg)).find('AI') != -1 or
                                           hasattr(msg, '__class__') and 'AI' in msg.__class__.__name__):
                response = msg.content
                break

        # If no AI message found, generate based on status
        if response == "正在处理您的请求...":
            if workflow_status == WorkflowStatus.CLARIFYING_INTENT and requires_feedback:
                response = "我需要了解更多信息来更好地为您服务。请提供更详细的需求描述。"

            elif workflow_status == WorkflowStatus.PLANNING and requires_feedback:
                response = "我正在为您制定执行计划，请稍候..."

            elif workflow_status == WorkflowStatus.CONFIRMING_PLAN and requires_feedback:
                response = "我已经为您制定了执行计划，请查看并确认是否同意执行。"

            elif workflow_status == WorkflowStatus.EXECUTING:
                response = "正在执行任务中..."

            elif workflow_status == WorkflowStatus.SUMMARIZING:
                response = "正在生成任务总结..."

            elif workflow_status == WorkflowStatus.COMPLETED:
                response = "任务已完成！您可以查看执行结果，或者提出新的任务。"

            elif workflow_status == WorkflowStatus.FAILED:
                error_info = state.get("error_info", {})
                response = f"执行过程中遇到问题：{error_info.get('message', '未知错误')}"

            elif workflow_status == WorkflowStatus.WAITING_FOR_HUMAN:
                response = "等待您的确认或进一步指示。"

            elif workflow_status == WorkflowStatus.PAUSED:
                response = "任务已暂停，等待您的指示。"

        # Convert workflow status to string
        status_str = workflow_status.value if hasattr(workflow_status, 'value') else str(workflow_status)

        return ChatResponse(
            session_id=session_id,
            response=response,
            status=status_str,
            requires_feedback=requires_feedback,
            metadata={
                "updated_at": datetime.now().isoformat(),
                "intent_clarified": state.get("intent_clarified", False),
                "intent_approved": state.get("intent_approved", False),
                "plan_approved": state.get("plan_approved", False),
                "message_count": len(messages)
            }
        )
    
    async def get_session_info(self, session_id: str) -> Optional[SessionInfo]:
        """Get session information."""
        if session_id not in self.active_sessions:
            return None
        
        session_data = self.active_sessions[session_id]
        
        # Try to get state from workflow
        try:
            status_info = self.workflow.get_session_status(session_id)
            status = str(status_info.get("workflow_status", "unknown")) if status_info else "unknown"
            updated_at = session_data["created_at"]  # V4 doesn't track updated_at in state
        except:
            status = "unknown"
            updated_at = session_data["created_at"]
        
        return SessionInfo(
            session_id=session_id,
            user_id=session_data.get("user_id"),
            created_at=session_data["created_at"],
            updated_at=updated_at,
            status=status,
            message_count=session_data["message_count"],
            metadata={}
        )
    
    async def get_workflow_status(self, session_id: str) -> Optional[WorkflowStatus]:
        """Get workflow status for a session."""
        try:


            status_info = self.workflow.get_session_status(session_id)
            if not status_info:
                return None

            return status_info.get("workflow_status", WorkflowStatus.INITIALIZING)

        except Exception as e:
            self.logger.error(f"Error getting workflow status: {e}")
            return None
    
    async def get_system_status(self) -> SystemStatus:
        """Get system status."""
        uptime = datetime.now() - self.start_time
        uptime_str = f"{uptime.days}d {uptime.seconds//3600}h {(uptime.seconds%3600)//60}m"

        # V4 doesn't have a tool registry, so we estimate
        tools_available = 6  # Basic tools available in V4 including unified analysis

        return SystemStatus(
            status="healthy",
            version="0.4.0",
            uptime=uptime_str,
            active_sessions=len(self.active_sessions),
            total_sessions=self.total_sessions,
            tools_available=tools_available,
            agents_active=5  # Supervisor, Intent, Planning, Execution, Summary
        )
    
    async def get_available_tools(self) -> List[ToolInfo]:
        """Get list of available tools."""
        # V4 has simplified tool set with unified analysis
        tools = [
            ToolInfo(
                name="unified_message_analysis",
                description="统一用户消息分析和路由决策（一次LLM调用完成）",
                category="routing",
                available=True
            ),
            ToolInfo(
                name="intent_analysis",
                description="分析用户意图和需求",
                category="intent",
                available=True
            ),
            ToolInfo(
                name="clarification_generation",
                description="生成澄清问题",
                category="intent",
                available=True
            ),
            ToolInfo(
                name="plan_creation",
                description="创建执行计划",
                category="planning",
                available=True
            ),
            ToolInfo(
                name="task_execution",
                description="执行具体任务",
                category="execution",
                available=True
            ),
            ToolInfo(
                name="summary_generation",
                description="生成任务总结",
                category="summary",
                available=True
            )
        ]

        return tools
    
    async def get_agents_info(self) -> List[AgentInfo]:
        """Get information about available agents."""
        return [
            AgentInfo(
                name="supervisor",
                role="Supervisor Agent",
                description="负责协调和路由各个Agent，支持统一用户消息分析和智能路由决策",
                tools=["unified_message_analysis", "intelligent_routing", "coordination"],
                status="active"
            ),
            AgentInfo(
                name="intent_analysis",
                role="Intent Analysis Agent",
                description="负责澄清和理解用户意图",
                tools=["intent_analysis", "clarification_generation"],
                status="active"
            ),
            AgentInfo(
                name="planning",
                role="Planning Agent",
                description="负责制定任务执行计划",
                tools=["plan_creation"],
                status="active"
            ),
            AgentInfo(
                name="execution",
                role="Execution Agent",
                description="负责执行具体任务步骤",
                tools=["task_execution"],
                status="active"
            ),
            AgentInfo(
                name="summary",
                role="Summary Agent",
                description="负责生成任务总结和报告",
                tools=["summary_generation"],
                status="active"
            )
        ]
    
    async def health_check(self) -> HealthCheck:
        """Perform health check."""
        return HealthCheck(
            status="healthy",
            services={
                "workflow": "SpecificWorkflow",
                "supervisor": "SupervisorAgent",
                "agents": "BaseAgent"
            },
            version="0.4.0",
            tools_count=6,
            agents_count=5
        )
    async def cleanup_old_sessions(self, max_age_hours: int = 24):
        """Clean up old inactive sessions."""
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        
        sessions_to_remove = []
        for session_id, session_data in self.active_sessions.items():
            if session_data["created_at"] < cutoff_time:
                sessions_to_remove.append(session_id)
        
        for session_id in sessions_to_remove:
            del self.active_sessions[session_id]
            self.logger.info(f"Cleaned up old session: {session_id}")
        
        return len(sessions_to_remove)
