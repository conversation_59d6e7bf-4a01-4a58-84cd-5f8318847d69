"""FastAPI server for Specific V4 system."""

import logging
from typing import List, Optional

from dotenv import load_dotenv
from fastapi import FastAP<PERSON>, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

from src.api import SpecificAPI
from src.api.schemas import (
    ChatRequest, ChatResponse, SessionInfo, SystemStatus,
    WorkflowStatus, HealthCheck, ToolInfo, AgentInfo, ErrorResponse
)

load_dotenv()

# Global API instance
api_instance: Optional[SpecificAPI] = None


def get_api() -> SpecificAPI:
    """Get API instance."""
    global api_instance
    if api_instance is None:
        api_instance = SpecificAPI()
    return api_instance


def create_app() -> FastAPI:
    """Create FastAPI application."""
    
    app = FastAPI(
        title="Specific Multi-Agent System",
        description="A simplified multi-agent system built with LangGraph using supervisor pattern",
        version="0.1.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    @app.exception_handler(Exception)
    async def global_exception_handler(request, exc):
        """Global exception handler."""
        logging.error(f"Unhandled exception: {exc}")
        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                error_type="internal_error",
                message="Internal server error",
                details=str(exc),
                recoverable=False
            ).dict()
        )
    
    @app.post("/chat", response_model=ChatResponse)
    async def chat_endpoint(request: ChatRequest, api: SpecificAPI = Depends(get_api)):
        """Chat endpoint for multi-turn conversations."""
        try:
            return await api.chat(request)
        except Exception as e:
            logging.error(f"Chat endpoint error: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    @app.get("/session/{session_id}", response_model=SessionInfo)
    async def get_session_endpoint(session_id: str, api: SpecificAPI = Depends(get_api)):
        """Get session information."""
        session_info = await api.get_session_info(session_id)
        if session_info is None:
            raise HTTPException(status_code=404, detail="Session not found")
        return session_info

    @app.get("/session/{session_id}/status", response_model=WorkflowStatus)
    async def get_workflow_status_endpoint(session_id: str, api: SpecificAPI = Depends(get_api)):
        """Get workflow status for a session."""
        status = await api.get_workflow_status(session_id)
        if status is None:
            raise HTTPException(status_code=404, detail="Session not found or no workflow status available")
        return status

    @app.get("/system/status", response_model=SystemStatus)
    async def get_system_status_endpoint(api: SpecificAPI = Depends(get_api)):
        """Get system status."""
        return await api.get_system_status()

    @app.get("/system/tools", response_model=List[ToolInfo])
    async def get_tools_endpoint(api: SpecificAPI = Depends(get_api)):
        """Get available tools."""
        return await api.get_available_tools()

    @app.get("/system/agents", response_model=List[AgentInfo])
    async def get_agents_endpoint(api: SpecificAPI = Depends(get_api)):
        """Get agent information."""
        return await api.get_agents_info()

    @app.get("/health", response_model=HealthCheck)
    async def health_check_endpoint(api: SpecificAPI = Depends(get_api)):
        """Health check endpoint."""
        return await api.health_check()

    @app.post("/system/cleanup")
    async def cleanup_sessions_endpoint(
        max_age_hours: int = 24,
        api: SpecificAPI = Depends(get_api)
    ):
        """Clean up old sessions."""
        cleaned_count = await api.cleanup_old_sessions(max_age_hours)
        return {"message": f"Cleaned up {cleaned_count} old sessions"}

    @app.post("/report/generate")
    async def generate_report_endpoint(
        session_id: str,
        access_token: Optional[str] = None,
        api: SpecificAPI = Depends(get_api)
    ):
        """生成报告 - 基于session中的report_dsl数据，直接调用ReportAgent"""
        try:
            # 获取session状态
            session_state = api.workflow.get_session_status(session_id)
            if not session_state:
                raise HTTPException(status_code=404, detail="Session not found")

            # 获取完整的state数据
            state_values = session_state.get("values", {})

            # 如果提供了access_token，添加到extensions中
            if access_token:
                extensions = state_values.get("extensions", {})
                if not extensions:
                    extensions = {}
                if "tokens" not in extensions:
                    extensions["tokens"] = []
                extensions["tokens"].append({"access_token": access_token})
                state_values["extensions"] = extensions

            # 创建ReportAgent并执行
            from src.agents.report import ReportAgent
            from langchain_openai import ChatOpenAI

            llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
            report_agent = ReportAgent(llm=llm)

            # 创建一个简单的writer来收集消息
            messages = []
            def writer(data):
                messages.append(data)

            # 执行报告生成
            result = await report_agent.execute(state_values, writer)

            # 提取结果
            if result.update.get("workflow_status") == "completed":
                return {
                    "success": True,
                    "session_id": session_id,
                    "html_report": result.update.get("html_report"),
                    "final_report": result.update.get("final_report"),
                    "report_dsl": result.update.get("report_dsl"),
                    "summary_data": result.update.get("summary_data"),
                    "upload_result": result.update.get("upload_result"),
                    "messages": messages,
                    "message": "报告生成成功"
                }
            else:
                error_info = result.update.get("error_info", {})
                raise HTTPException(
                    status_code=500,
                    detail=f"报告生成失败: {error_info.get('message', 'Unknown error')}"
                )

        except HTTPException:
            raise
        except Exception as e:
            logging.error(f"Report generation endpoint error: {e}")
            raise HTTPException(status_code=500, detail=f"报告生成失败: {str(e)}")

    @app.post("/report/generate-with-dsl")
    async def generate_report_with_dsl_endpoint(
        report_dsl: dict,
        access_token: Optional[str] = None
    ):
        """直接使用提供的DSL数据生成报告"""
        try:
            # 创建ReportAgent
            from src.agents.report import ReportAgent
            from langchain_openai import ChatOpenAI

            llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
            report_agent = ReportAgent(llm=llm)

            # 构建state数据
            state_data = {
                "session_id": "direct_dsl_generation",
                "report_dsl_data": report_dsl,
                "report_dsl_status": "SUCCESS",  # 直接提供的DSL假设是成功的
                "report_dsl_message": None,
                "extensions": {
                    "tokens": [{"access_token": access_token}] if access_token else []
                }
            }

            # 创建一个简单的writer来收集消息
            messages = []
            def writer(data):
                messages.append(data)

            # 执行报告生成
            result = await report_agent.execute(state_data, writer)

            # 提取结果
            if result.update.get("workflow_status") == "completed":
                return {
                    "success": True,
                    "html_report": result.update.get("html_report"),
                    "final_report": result.update.get("final_report"),
                    "report_dsl": result.update.get("report_dsl"),
                    "summary_data": result.update.get("summary_data"),
                    "upload_result": result.update.get("upload_result"),
                    "messages": messages,
                    "message": "报告生成成功"
                }
            else:
                error_info = result.update.get("error_info", {})
                raise HTTPException(
                    status_code=500,
                    detail=f"报告生成失败: {error_info.get('message', 'Unknown error')}"
                )

        except HTTPException:
            raise
        except Exception as e:
            logging.error(f"Report generation with DSL endpoint error: {e}")
            raise HTTPException(status_code=500, detail=f"报告生成失败: {str(e)}")
    
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "message": "Specific V4 Multi-Agent System",
            "version": "0.4.0",
            "docs": "/docs",
            "health": "/health"
        }
    
    return app


# Create the app instance
app = create_app()


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "src.api.server:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
