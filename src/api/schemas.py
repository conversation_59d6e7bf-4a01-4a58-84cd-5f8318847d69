"""API schemas for Specific V3 system."""

from datetime import datetime
from typing import Any, Dict, List, Optional, Literal
from pydantic import BaseModel, Field
from src.models.state import WorkflowStatus


class ServiceToken(BaseModel):
    """Service token schema for IdaaS integration."""

    service_id: str = Field(..., description="服务ID（接入IdaaS）")
    access_token: str = Field(..., description="服务access_token")


class Extensions(BaseModel):
    """Extensions schema for additional features."""

    tokens: Optional[List[ServiceToken]] = Field(None, description="服务token列表")
    # 其他扩展字段可以在这里添加
    additional_fields: Dict[str, Any] = Field(default_factory=dict, description="其他扩展字段")


class ChatRequest(BaseModel):
    """Request schema for chat endpoint."""

    message: str = Field(..., description="用户发送的消息内容")
    session_id: Optional[str] = Field(None, description="会话ID")
    task_id: Optional[str] = Field(None, description="任务ID")
    sandbox_id: Optional[str] = Field(None, description="沙箱ID")
    event_webhook: Optional[str] = Field(None, description="上传事件回调地址")
    extensions: Optional[Extensions] = Field(None, description="扩展字段，字典类型，字段不规定")

    # 报表DSL字段
    report_dsl_data: Optional[Dict[str, Any]] = Field(None, description="报表DSL数据结构，用于生成报告")
    report_dsl_status: Optional[Literal["SUCCESS", "FAILED"]] = Field(None, description="报表DSL生成状态")
    report_dsl_message: Optional[str] = Field(None, description="报表DSL状态消息，FAILED时包含错误信息")

    # 工作流状态字段
    workflow_status: Optional[WorkflowStatus] = Field(None, description="期望的工作流状态，用于动态控制流程")

    # 保留原有字段以保持向后兼容
    user_id: Optional[str] = Field(None, description="用户ID（向后兼容）")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据（向后兼容）")


class ChatResponse(BaseModel):
    """Response schema for chat endpoint."""

    session_id: str = Field(..., description="会话ID")
    response: str = Field(..., description="系统响应")
    status: str = Field(..., description="当前工作流状态")
    requires_feedback: bool = Field(False, description="是否需要用户反馈")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="响应元数据")

    # 新增字段
    task_id: Optional[str] = Field(None, description="任务ID")
    sandbox_id: Optional[str] = Field(None, description="沙箱ID")
    event_webhook: Optional[str] = Field(None, description="事件回调地址")
    extensions: Optional[Dict[str, Any]] = Field(None, description="扩展字段")


class SessionInfo(BaseModel):
    """Session information schema."""
    
    session_id: str
    user_id: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    status: str
    message_count: int = 0
    metadata: Dict[str, Any] = Field(default_factory=dict)


class SystemStatus(BaseModel):
    """System status schema."""
    
    status: str = Field(..., description="System status")
    version: str = Field(..., description="System version")
    uptime: str = Field(..., description="System uptime")
    active_sessions: int = Field(..., description="Number of active sessions")
    total_sessions: int = Field(..., description="Total sessions created")
    tools_available: int = Field(..., description="Number of available tools")
    agents_active: int = Field(..., description="Number of active agents")


class ToolInfo(BaseModel):
    """Tool information schema."""
    
    name: str
    description: str
    category: str = "general"
    available: bool = True


class AgentInfo(BaseModel):
    """Agent information schema."""
    
    name: str
    role: str
    description: str
    tools: List[str] = Field(default_factory=list)
    status: str = "active"


class WorkflowStatusInfo(BaseModel):
    """Workflow status information schema."""

    session_id: str
    current_step: str
    progress: float = Field(ge=0.0, le=1.0)
    steps_completed: int = 0
    total_steps: int = 0
    estimated_time_remaining: Optional[str] = None


class ErrorResponse(BaseModel):
    """Error response schema."""
    
    error_type: str
    message: str
    details: Optional[str] = None
    timestamp: datetime = Field(default_factory=datetime.now)
    recoverable: bool = False


class HealthCheck(BaseModel):
    """Health check response schema."""

    status: str = "healthy"
    timestamp: datetime = Field(default_factory=datetime.now)
    services: Dict[str, str] = Field(default_factory=dict)
    version: str = "0.3.0"
    tools_count: int = 0
    agents_count: int = 0