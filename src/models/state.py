"""状态模型 - 使用LangGraph原生能力但保持清晰结构."""

from typing import Annotated, List, Optional, Dict, Any
from typing_extensions import TypedDict
from enum import Enum
from datetime import datetime
from pydantic import BaseModel, Field

from langchain_core.messages import BaseMessage, HumanMessage
from langgraph.graph.message import add_messages


class WorkflowStatus(str, Enum):
    """工作流状态枚举."""
    INITIALIZING = "initializing"
    CLARIFYING_INTENT = "clarifying_intent"
    PLANNING = "planning"
    CONFIRMING_PLAN = "confirming_plan"
    EXECUTING = "executing"
    SUMMARIZING = "summarizing"
    REPORT = "report"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"
    WAITING_FOR_HUMAN = "waiting_for_human"  # 等待人工干预


class ErrorInfo(BaseModel):
    """错误信息模型 - 简化版"""
    type: str = Field(description="错误类型")
    message: str = Field(description="错误消息")
    recoverable: bool = Field(default=True, description="是否可恢复")


class SpecificState(TypedDict):
    """
    简化的状态定义 - 去除冗余字段，利用messages作为历史记录

    设计原则：
    - 使用messages字段作为完整的对话历史
    - 只保留影响主流程的核心状态字段
    - 去除冗余的历史记录和评分字段
    """

    # === LangGraph原生消息管理 ===
    messages: Annotated[List[BaseMessage], add_messages]

    # === 会话信息 ===
    session_id: str
    user_id: Optional[str]
    task_id: Optional[str]  # 任务ID
    sandbox_id: Optional[str]  # 沙箱ID
    event_webhook: Optional[str]  # 事件回调地址
    extensions: Optional[Dict[str, Any]]  # 扩展字段

    # === 工作流状态 ===
    workflow_status: WorkflowStatus

    # === 用户输入和意图 ===
    user_input: str
    intent_clarified: bool
    intent_approved: bool  # 意图澄清是否已被人工审批
    intent_summary: Optional[str]  # 澄清完成后的意图总结
    clarification_round: int  # 澄清轮次

    # === 澄清结果详情 ===
    clarification_result: Optional[Dict[str, Any]]  # 完整的澄清结果信息

    # === 任务规划 ===
    task_plan: Optional[Dict[str, Any]]  # ExecutionPlan的字典形式
    plan_approved: bool
    planning_round: int  # 规划轮次

    # === 任务执行 ===
    current_step_index: int
    execution_started: bool
    execution_report: Optional[str]  # 执行报告

    # === 报告生成 ===
    final_report: Optional[str]  # 最终报告
    html_report: Optional[str]  # HTML格式报告
    report_dsl_data: Optional[Dict[str, Any]]  # 报表DSL数据结构
    report_dsl_status: Optional[str]  # 报表DSL生成状态：SUCCESS | FAILED
    report_dsl_message: Optional[str]  # 报表DSL状态消息，FAILED时包含错误信息
    summary_data: Optional[Dict[str, Any]]  # 解析后的结构化总结数据
    upload_result: Optional[Dict[str, Any]]  # S3上传结果

    # === 控制字段 ===
    # 移除了 human_approval_required 字段，改用 workflow_status 来控制流程

    # === 错误处理 ===
    error_info: Optional[Dict[str, Any]]

    # === 元数据 ===
    metadata: Dict[str, Any]


def create_initial_state(
    session_id: str,
    user_input: str,
    user_id: Optional[str] = None,
    task_id: Optional[str] = None,
    sandbox_id: Optional[str] = None,
    event_webhook: Optional[str] = None,
    extensions: Optional[Any] = None,
    report_dsl_data: Optional[Dict[str, Any]] = None,
    report_dsl_status: Optional[str] = None,
    report_dsl_message: Optional[str] = None,
    metadata: Optional[Dict[str, Any]] = None,
    workflow_status: Optional[WorkflowStatus] = None
) -> SpecificState:
    """创建初始状态 - 简化版本"""

    return SpecificState(
        # LangGraph原生消息
        messages=[HumanMessage(content=user_input)],

        # 会话信息
        session_id=session_id,
        user_id=user_id,
        task_id=task_id,
        sandbox_id=sandbox_id,
        event_webhook=event_webhook,
        extensions=extensions.dict() if extensions and hasattr(extensions, 'dict') else (extensions if isinstance(extensions, dict) else None),

        # 工作流状态
        workflow_status=workflow_status or WorkflowStatus.INITIALIZING,

        # 用户输入和意图
        user_input=user_input,
        intent_clarified=False,
        intent_approved=False,
        intent_summary=None,
        clarification_round=0,

        # 澄清结果详情
        clarification_result=None,

        # 任务规划
        task_plan=None,
        plan_approved=False,
        planning_round=0,

        # 任务执行
        current_step_index=0,
        execution_started=False,
        execution_report=None,

        # 报告生成
        final_report=None,
        html_report=None,
        report_dsl_data=report_dsl_data,
        report_dsl_status=report_dsl_status,
        report_dsl_message=report_dsl_message,
        summary_data=None,
        upload_result=None,

        # 控制字段
        # 移除了 human_approval_required 字段

        # 错误处理
        error_info=None,

        # 元数据
        metadata=metadata or {}
    )


# ==================== 简化版结构体定义 ====================

class ClarificationResultData(BaseModel):
    """澄清结果数据结构体 - 供其他节点使用"""
    event: Optional[str] = Field(default=None, description="舆情事件")
    platform: Optional[str] = Field(default=None, description="平台范围")
    time_range: Optional[str] = Field(default=None, description="时间范围")


class IntentClarificationResult(BaseModel):
    """意图澄清结果结构体 - 增强版，包含结构化澄清信息"""
    intent_clear: bool = Field(description="意图是否澄清完成")
    clarification_questions: List[str] = Field(description="澄清问题列表")
    clarification_result: Optional[ClarificationResultData] = Field(default=None, description="结构化的澄清结果数据")
    summary: str = Field(description="当前理解总结")
    response_message: str = Field(description="发送给用户的消息，根据意图是否清晰包含澄清问题或审批确认")


class PlanStep(BaseModel):
    """计划步骤结构体 - 简化版"""
    title: str = Field(description="步骤标题")
    description: str = Field(description="步骤描述")
    skip_execute: bool = Field(default=False, description="是否跳过执行")

class ExecutionPlan(BaseModel):
    """执行计划结构体 - 简化版"""
    title: str = Field(description="计划标题")
    steps: List[PlanStep] = Field(description="执行步骤")
    version: int = Field(default=1, description="计划版本号")

    def to_markdown(self) -> str:
        lines = []
        lines.append("")
        lines.append("─" * len(self.title))
        lines.append(f"{self.title}")
        lines.append("─" * len(self.title))
        lines.append("")

        for i, step in enumerate(self.steps, 1):
            lines.append(f"{i}. {step.title}")

            if step.description:
                lines.append(f"   {step.description}")

            lines.append("")

        return "\n".join(lines)

class UserIntentAnalysis(BaseModel):
    """用户意图分析结构体 - 简化版"""
    intent_type: str = Field(description="意图类型：agreement(同意), supplement(补充信息), rejection(拒绝)")
    next_action: str = Field(description="建议的下一步行动：continue(继续流程), retry(重新处理)")
    extracted_info: Optional[str] = Field(default="", description="从用户回复中提取的有用信息")
