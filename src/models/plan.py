"""计划模型 - 精简版结构体."""

from typing import List, Optional
from enum import Enum
from pydantic import BaseModel, Field


class TaskStatus(str, Enum):
    """任务状态枚举."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class PlanStep(BaseModel):
    """计划步骤模型."""
    title: str = Field(description="步骤标题")
    description: str = Field(description="步骤描述")
    skip_execute: bool = Field(default=False, description="是否跳过执行")


class Plan(BaseModel):
    """执行计划模型."""
    title: str = Field(description="计划标题")
    steps: List[PlanStep] = Field(default_factory=list, description="计划步骤列表")



