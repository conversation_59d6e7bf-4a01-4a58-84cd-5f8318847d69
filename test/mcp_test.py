import asyncio
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, MessagesState, START
from langgraph.prebuilt import ToolNode, tools_condition
from pydantic import SecretStr


async def setup_mcp_client():


    client = MultiServerMCPClient(
        {
            "search": {
                "transport": "streamable_http",
                "url": "http://172.21.65.95:5003/mcp/marketing",
                "enabled_tools": [],
                "add_to_agents": [
                    "researcher",
                    "coder"
                ],
                "headers": {
                    "sessionId": "1W6R16zohL64p6LT9irhm4",
                    "taskId": "0bjAfmLoenVoPIi296y754",
                    "sandbox_id": "cNzFt8F3LSWxLhblMsnhF",
                    "account": "<EMAIL>",
                    "sign": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************.HYqanRX9qt-W3W4d4yWscWmHs9fMemzAS2b0Wa1Ej4M",
                    "X-CHJ-GWToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************.HYqanRX9qt-W3W4d4yWscWmHs9fMemzAS2b0Wa1Ej4M"
                }
            }
        }
    )
    return client


async def run_demo():
    print("Setting up MCP client...")
    client = await setup_mcp_client()

    print("Getting tools...")

    tools = await client.get_tools()
    print(f"Found {len(tools)} tools")
    print(f"tool:{tools}")

    # model = init_chat_model("openai:gpt-4.1")
    model = ChatOpenAI(
        base_url='https://camel-ai.dev.fc.chj.cloud/agentops',
        model='azure-gpt-4_1',
        api_key=SecretStr('test'),
        temperature=0.0,
    )

    def call_model(state: MessagesState):
        response = model.bind_tools(tools).invoke(state["messages"])
        print("result",response)
        return {"messages": response}

    print("Building graph...")
    builder = StateGraph(MessagesState)
    builder.add_node(call_model)
    builder.add_node(ToolNode(tools))
    builder.add_edge(START, "call_model")
    builder.add_conditional_edges(
        "call_model",
        tools_condition,
    )
    builder.add_edge("tools", "call_model")
    graph = builder.compile()

    print("Running Graph..")
    browser_response = await graph.ainvoke(
        {"messages": [{"role": "user", "content": "#步骤: 收集理想汽车L9舆情数据\n#描述: 使用品牌MCP Tool，设置数据收集参数为：事件为'理想汽车L9'，时间范围为'近一个月'，平台范围为'全平台'。确保数据收集覆盖社交媒体、新闻网站、论坛等各类平台，记录相关的舆情数据，包括评论、文章、帖子等，并整理成结构化数据以便后续分析。"}]})
    print(f"Graph response: {browser_response}")


async def main():
    """Main entry point for the script."""
    print("Starting MCP client demo...")
    await run_demo()
    print("Demo completed.")


if __name__ == "__main__":
    """Execute the script when run directly."""
    asyncio.run(main())
