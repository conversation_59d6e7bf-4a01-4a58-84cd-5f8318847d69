import asyncio
from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_openai import Chat<PERSON>penAI
from langgraph.graph import StateGraph, MessagesState, START
from langgraph.prebuilt import ToolNode, tools_condition
from pydantic import SecretStr
import jwt
import datetime

# 配置信息（需与验证函数保持一致）
SECRET_KEY = "brand_event"  # 必须与验证函数相同
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 60  # Token有效期（48小时）

def create_access_token(user_id: str) -> str:
    """
    生成JWT Token（与verify_token_and_user配套）

    参数:
        user_id: 用户唯一标识，将作为'sub'存入Token

    返回:
        str: 签名的JWT Token
    """
    # 设置过期时间
    expire = datetime.datetime.now(datetime.timezone.utc) + datetime.timedelta(
        minutes=ACCESS_TOKEN_EXPIRE_MINUTES
    )

    # 构建Payload（必须包含sub字段以匹配验证逻辑）
    to_encode = {
        "sub": user_id,          # 用户ID（subject标准字段）
        "exp": expire,            # 过期时间
        "iat": datetime.datetime.now(datetime.timezone.utc),  # 签发时间
        "type": "access"          # 可选的Token类型标识
    }

    # 生成Token
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def setup_mcp_client():


    client = MultiServerMCPClient(
        {
            "search": {
                "transport": "streamable_http",
                "url": "http://172.21.65.95:5003/mcp/marketing",
                "enabled_tools": [],
                "add_to_agents": [
                    "researcher",
                    "coder"
                ],
                "headers": {
                    "sessionId": "1W6R16zohL64p6LT9irhm4",
                    "taskId": "0bjAfmLoenVoPIi296y754",
                    "sandbox_id": "cNzFt8F3LSWxLhblMsnhF",
                    "account": "<EMAIL>",
                    "sign": create_access_token("<EMAIL>"),
                }
            }
        }
    )
    return client


async def run_demo():
    print("Setting up MCP client...")
    client = await setup_mcp_client()

    print("Getting tools...")

    tools = await client.get_tools()
    print(f"Found {len(tools)} tools")
    print(f"tool:{tools}")

    # model = init_chat_model("openai:gpt-4.1")
    model = ChatOpenAI(
        base_url='https://camel-ai.dev.fc.chj.cloud/agentops',
        model='azure-gpt-4_1',
        api_key=SecretStr('test'),
        temperature=0.0,
    )

    def call_model(state: MessagesState):
        response = model.bind_tools(tools).invoke(state["messages"])
        print("result",response)
        return {"messages": response}

    print("Building graph...")
    builder = StateGraph(MessagesState)
    builder.add_node(call_model)
    builder.add_node(ToolNode(tools))
    builder.add_edge(START, "call_model")
    builder.add_conditional_edges(
        "call_model",
        tools_condition,
    )
    builder.add_edge("tools", "call_model")
    graph = builder.compile()

    print("Running Graph..")
    browser_response = await graph.ainvoke(
        {"messages": [{"role": "user", "content": "佳伟饿了的舆情观点总结"}]})
    print(f"Graph response: {browser_response}")


async def main():
    """Main entry point for the script."""
    print("Starting MCP client demo...")
    await run_demo()
    print("Demo completed.")


if __name__ == "__main__":
    """Execute the script when run directly."""
    asyncio.run(main())
