#!/usr/bin/env python3
"""
测试SupervisorAgent的流式消息功能
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))

from src.core.workflow import SpecificWorkflow
from src.models.state import SpecificState, WorkflowStatus, create_initial_state
from langchain_core.messages import HumanMessage, AIMessage

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class TestStreamWriter:
    """测试用的StreamWriter，记录所有消息"""
    
    def __init__(self):
        self.messages = []
    
    def __call__(self, data):
        """记录流式消息"""
        self.messages.append(data)
        print(f"📡 StreamWriter: {data}")
    
    def get_messages_by_type(self, message_type):
        """按类型获取消息"""
        return [msg for msg in self.messages if message_type in msg]
    
    def clear(self):
        """清空消息"""
        self.messages = []


def create_test_state(
    user_input: str,
    workflow_status: WorkflowStatus = WorkflowStatus.INITIALIZING,
    **kwargs
) -> SpecificState:
    """创建测试状态"""
    state = create_initial_state(
        session_id="test_session",
        user_input=user_input,
        user_id="test_user"
    )

    # 更新状态
    state["workflow_status"] = workflow_status
    for key, value in kwargs.items():
        state[key] = value

    return state


def test_supervisor_streaming_messages():
    """测试SupervisorAgent的流式消息"""
    print("🧪 测试SupervisorAgent流式消息功能")
    print("=" * 50)

    # 创建工作流实例
    workflow = SpecificWorkflow()
    writer = TestStreamWriter()

    # 测试场景1：初始状态路由
    print("\n1. 测试初始状态路由的流式消息")
    state1 = create_test_state(
        user_input="请分析京东外卖品牌舆情",
        workflow_status=WorkflowStatus.INITIALIZING
    )

    try:
        result1 = workflow.supervisor_agent.execute(state1, writer)
        
        print(f"✅ 路由结果: {result1.goto}")
        
        # 验证流式消息
        live_messages = writer.get_messages_by_type("live_status_message")
        agent_messages = writer.get_messages_by_type("agent_message")
        
        print(f"📊 消息统计: 状态消息 {len(live_messages)} 条, Agent消息 {len(agent_messages)} 条")
        
        # 验证关键消息内容
        assert len(live_messages) > 0, "应该有实时状态消息"
        assert len(agent_messages) > 0, "应该有Agent消息"
        
        # 检查消息内容
        status_keywords = ["分析", "状态", "路由"]
        agent_keywords = ["分析", "处理", "需求"]
        
        found_status = any(any(keyword in str(msg) for keyword in status_keywords) for msg in live_messages)
        found_agent = any(any(keyword in str(msg) for keyword in agent_keywords) for msg in agent_messages)
        
        assert found_status, "状态消息应该包含相关关键词"
        assert found_agent, "Agent消息应该包含相关关键词"
        
        print("✓ 初始状态路由流式消息测试通过")
        
    except Exception as e:
        print(f"❌ 初始状态路由测试失败: {e}")
        return False

    # 清空消息记录
    writer.clear()

    # 测试场景2：意图澄清完成后路由
    print("\n2. 测试意图澄清完成后路由的流式消息")
    state2 = create_test_state(
        user_input="分析京东外卖品牌在近30天的舆情表现",
        workflow_status=WorkflowStatus.CLARIFYING_INTENT,
        intent_clarified=True,
        intent_approved=True,
        intent_summary="分析京东外卖品牌在近30天的舆情表现"
    )

    try:
        result2 = workflow.supervisor_agent.execute(state2, writer)
        
        print(f"✅ 路由结果: {result2.goto}")
        
        # 验证流式消息
        live_messages = writer.get_messages_by_type("live_status_message")
        agent_messages = writer.get_messages_by_type("agent_message")
        
        print(f"📊 消息统计: 状态消息 {len(live_messages)} 条, Agent消息 {len(agent_messages)} 条")
        
        assert len(live_messages) > 0, "应该有实时状态消息"
        assert len(agent_messages) > 0, "应该有Agent消息"
        
        # 检查是否有计划相关的消息
        planning_keywords = ["计划", "制定", "规划"]
        found_planning = any(any(keyword in str(msg) for keyword in planning_keywords) for msg in agent_messages)
        
        if result2.goto == "planning":
            assert found_planning, "路由到planning时应该有计划相关消息"
        
        print("✓ 意图澄清完成路由流式消息测试通过")
        
    except Exception as e:
        print(f"❌ 意图澄清完成路由测试失败: {e}")
        return False

    # 清空消息记录
    writer.clear()

    # 测试场景3：用户消息分析
    print("\n3. 测试用户消息分析的流式消息")
    
    test_messages = [
        {"content": "你好", "expected_type": "greeting"},
        {"content": "好的，我同意这个计划", "expected_type": "agreement"},
        {"content": "请增加竞品分析", "expected_type": "supplement"}
    ]

    for i, test_msg in enumerate(test_messages, 1):
        print(f"\n3.{i} 测试消息: {test_msg['content']}")
        
        messages = [HumanMessage(content=test_msg["content"])]
        state3 = create_test_state(
            user_input="初始输入",
            workflow_status=WorkflowStatus.CLARIFYING_INTENT,
            messages=messages
        )

        try:
            result3 = workflow.supervisor_agent.execute(state3, writer)
            
            print(f"   ✅ 路由结果: {result3.goto}")
            
            # 验证流式消息
            live_messages = writer.get_messages_by_type("live_status_message")
            agent_messages = writer.get_messages_by_type("agent_message")
            
            print(f"   📊 消息统计: 状态消息 {len(live_messages)} 条, Agent消息 {len(agent_messages)} 条")
            
            assert len(live_messages) > 0, f"消息 '{test_msg['content']}' 应该有实时状态消息"
            assert len(agent_messages) > 0, f"消息 '{test_msg['content']}' 应该有Agent消息"
            
            print(f"   ✓ 消息 '{test_msg['content']}' 流式消息测试通过")
            
        except Exception as e:
            print(f"   ❌ 消息 '{test_msg['content']}' 测试失败: {e}")
            return False
        
        # 清空消息记录
        writer.clear()

    return True


def test_supervisor_message_content():
    """测试SupervisorAgent消息内容的质量"""
    print("\n🧪 测试SupervisorAgent消息内容质量")
    print("=" * 50)

    workflow = SpecificWorkflow()
    writer = TestStreamWriter()

    # 测试不同路由场景的消息内容
    test_scenarios = [
        {
            "name": "初始状态",
            "state": create_test_state(
                user_input="分析品牌舆情",
                workflow_status=WorkflowStatus.INITIALIZING
            ),
            "expected_route": "intent_clarification"
        },
        {
            "name": "计划审批完成",
            "state": create_test_state(
                user_input="分析品牌舆情",
                workflow_status=WorkflowStatus.PLANNING,
                intent_clarified=True,
                intent_approved=True,
                plan_approved=True,
                execution_started=True
            ),
            "expected_route": "execution"
        }
    ]

    for scenario in test_scenarios:
        print(f"\n测试场景: {scenario['name']}")
        
        try:
            result = workflow.supervisor_agent.execute(scenario["state"], writer)
            
            # 验证路由正确性
            assert result.goto == scenario["expected_route"], f"路由应该是 {scenario['expected_route']}"
            
            # 验证消息质量
            live_messages = writer.get_messages_by_type("live_status_message")
            agent_messages = writer.get_messages_by_type("agent_message")
            
            # 检查消息是否友好和有意义
            for msg in agent_messages:
                content = str(msg)
                assert len(content) > 10, "Agent消息应该有足够的内容"
                assert any(word in content for word in ["我", "您", "为您", "让我"]), "Agent消息应该是友好的"
            
            # 检查状态消息是否描述性强
            for msg in live_messages:
                content = str(msg)
                assert len(content) > 5, "状态消息应该有描述性内容"
            
            print(f"✅ 场景 '{scenario['name']}' 消息质量测试通过")
            
        except Exception as e:
            print(f"❌ 场景 '{scenario['name']}' 测试失败: {e}")
            return False
        
        # 清空消息记录
        writer.clear()

    return True


def main():
    """主测试函数"""
    print("🚀 SupervisorAgent流式消息测试套件")
    print("=" * 60)

    tests = [
        ("SupervisorAgent流式消息功能", test_supervisor_streaming_messages),
        ("SupervisorAgent消息内容质量", test_supervisor_message_content)
    ]

    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 运行: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))

    # 汇总结果
    print(f"\n📊 测试结果:")
    print("-" * 50)
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1

    print(f"\n🎯 总结: {passed}/{len(results)} 测试通过")

    if passed == len(results):
        print("🎉 所有SupervisorAgent流式消息测试通过！")
        print("\n💡 SupervisorAgent流式消息特性:")
        print("✅ 支持live_status_message实时状态更新")
        print("✅ 支持agent_message友好用户沟通")
        print("✅ 智能路由决策过程透明化")
        print("✅ 用户消息分析过程可视化")
        print("✅ 错误处理过程用户友好")
    else:
        print("⚠️ 部分测试失败")

    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
