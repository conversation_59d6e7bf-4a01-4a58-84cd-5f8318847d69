#!/usr/bin/env python3
"""
运行所有Agent测试的综合测试套件
"""

import sys
import os
import asyncio
import subprocess
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))


def run_test_file(test_file: str, description: str) -> tuple[bool, str, float]:
    """运行单个测试文件"""
    print(f"\n{'='*60}")
    print(f"🧪 运行测试: {description}")
    print(f"📁 文件: {test_file}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # 运行测试文件
        result = subprocess.run(
            [sys.executable, test_file],
            capture_output=True,
            text=True,
            timeout=300  # 5分钟超时
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 输出测试结果
        print(result.stdout)
        
        if result.stderr:
            print("⚠️ 错误输出:")
            print(result.stderr)
        
        success = result.returncode == 0
        status = "✅ 通过" if success else "❌ 失败"
        
        print(f"\n📊 测试结果: {status}")
        print(f"⏱️ 执行时间: {duration:.2f}秒")
        
        return success, result.stdout + result.stderr, duration
        
    except subprocess.TimeoutExpired:
        end_time = time.time()
        duration = end_time - start_time
        print(f"⏰ 测试超时 (>{duration:.0f}秒)")
        return False, "测试超时", duration
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"💥 测试异常: {e}")
        return False, str(e), duration


async def run_async_test_file(test_file: str, description: str) -> tuple[bool, str, float]:
    """运行异步测试文件"""
    print(f"\n{'='*60}")
    print(f"🧪 运行异步测试: {description}")
    print(f"📁 文件: {test_file}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # 运行异步测试文件
        process = await asyncio.create_subprocess_exec(
            sys.executable, test_file,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await asyncio.wait_for(
            process.communicate(), 
            timeout=300  # 5分钟超时
        )
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 输出测试结果
        if stdout:
            print(stdout.decode())
        
        if stderr:
            print("⚠️ 错误输出:")
            print(stderr.decode())
        
        success = process.returncode == 0
        status = "✅ 通过" if success else "❌ 失败"
        
        print(f"\n📊 测试结果: {status}")
        print(f"⏱️ 执行时间: {duration:.2f}秒")
        
        return success, (stdout + stderr).decode(), duration
        
    except asyncio.TimeoutError:
        end_time = time.time()
        duration = end_time - start_time
        print(f"⏰ 异步测试超时 (>{duration:.0f}秒)")
        return False, "异步测试超时", duration
        
    except Exception as e:
        end_time = time.time()
        duration = end_time - start_time
        print(f"💥 异步测试异常: {e}")
        return False, str(e), duration


def check_test_files():
    """检查测试文件是否存在"""
    test_files = [
        "test/test_intent_clarification_node.py",
        "test/test_planning_node.py", 
        "test/test_execution_node.py",
        "test/test_summary_node.py",
        "test/test_supervisor_node.py"
    ]
    
    missing_files = []
    for test_file in test_files:
        if not os.path.exists(test_file):
            missing_files.append(test_file)
    
    if missing_files:
        print("❌ 缺少测试文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ 所有测试文件都存在")
    return True


async def main():
    """主测试函数"""
    print("🚀 品牌特定Agent测试套件")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python版本: {sys.version}")
    print(f"📂 工作目录: {os.getcwd()}")
    
    # 检查测试文件
    if not check_test_files():
        print("\n❌ 测试文件检查失败，退出测试")
        return
    
    # 定义测试用例
    test_cases = [
        {
            "file": "test/test_supervisor_node.py",
            "description": "SupervisorAgent - 监督和路由逻辑",
            "async": False
        },
        {
            "file": "test/test_intent_clarification_node.py", 
            "description": "IntentAnalysisAgent - 意图澄清和审批",
            "async": False
        },
        {
            "file": "test/test_planning_node.py",
            "description": "PlanningAgent - 计划制定和审批",
            "async": False
        },
        {
            "file": "test/test_execution_node.py",
            "description": "ExecutionAgent - 任务执行和结果处理",
            "async": True
        },
        {
            "file": "test/test_summary_node.py",
            "description": "SummaryAgent - 结果总结和质量分析",
            "async": False
        }
    ]
    
    # 运行测试
    results = []
    total_start_time = time.time()
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🔄 进度: {i}/{len(test_cases)}")
        
        if test_case["async"]:
            success, output, duration = await run_async_test_file(
                test_case["file"], 
                test_case["description"]
            )
        else:
            success, output, duration = run_test_file(
                test_case["file"], 
                test_case["description"]
            )
        
        results.append({
            "name": test_case["description"],
            "file": test_case["file"],
            "success": success,
            "duration": duration,
            "output": output
        })
    
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    # 生成测试报告
    print(f"\n{'='*80}")
    print("📊 测试结果汇总")
    print(f"{'='*80}")
    
    passed = 0
    failed = 0
    
    for i, result in enumerate(results, 1):
        status = "✅ 通过" if result["success"] else "❌ 失败"
        print(f"{i}. {result['name']}")
        print(f"   状态: {status}")
        print(f"   时间: {result['duration']:.2f}秒")
        print(f"   文件: {result['file']}")
        
        if result["success"]:
            passed += 1
        else:
            failed += 1
        print()
    
    # 总结统计
    print(f"📈 统计信息:")
    print(f"   ✅ 通过: {passed}")
    print(f"   ❌ 失败: {failed}")
    print(f"   📊 成功率: {(passed/(passed+failed)*100):.1f}%")
    print(f"   ⏱️ 总时间: {total_duration:.2f}秒")
    print(f"   ⚡ 平均时间: {total_duration/len(results):.2f}秒/测试")
    
    # 失败详情
    if failed > 0:
        print(f"\n❌ 失败测试详情:")
        for result in results:
            if not result["success"]:
                print(f"\n🔍 {result['name']}:")
                print(f"   文件: {result['file']}")
                print(f"   错误信息: {result['output'][-500:]}")  # 显示最后500字符
    
    # 测试建议
    print(f"\n💡 测试建议:")
    if passed == len(results):
        print("🎉 所有测试都通过了！Agent实现质量很好。")
        print("✨ 建议:")
        print("   - 可以考虑添加更多边界情况测试")
        print("   - 可以添加性能基准测试")
        print("   - 可以添加集成测试")
    else:
        print("⚠️ 有测试失败，建议:")
        print("   - 检查失败的测试用例")
        print("   - 修复相关的Agent实现")
        print("   - 重新运行测试验证修复")
    
    # 保存测试报告
    report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    try:
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"品牌特定Agent测试报告\n")
            f.write(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"通过: {passed}, 失败: {failed}, 成功率: {(passed/(passed+failed)*100):.1f}%\n\n")
            
            for result in results:
                f.write(f"测试: {result['name']}\n")
                f.write(f"状态: {'通过' if result['success'] else '失败'}\n")
                f.write(f"时间: {result['duration']:.2f}秒\n")
                f.write(f"文件: {result['file']}\n")
                if not result['success']:
                    f.write(f"错误: {result['output']}\n")
                f.write("\n" + "="*50 + "\n\n")
        
        print(f"\n📄 测试报告已保存: {report_file}")
    except Exception as e:
        print(f"\n⚠️ 保存测试报告失败: {e}")
    
    print(f"\n🏁 测试完成!")
    return passed == len(results)


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试运行器异常: {e}")
        sys.exit(1)
