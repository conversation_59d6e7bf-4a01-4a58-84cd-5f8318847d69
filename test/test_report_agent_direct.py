#!/usr/bin/env python3
"""
直接测试ReportAgent - 就像您说的那样简单
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))

from src.core.workflow import SpecificWorkflow
from src.models.state import create_initial_state, WorkflowStatus, SpecificState


def get_test_stream_writer():
    """获取测试用的StreamWriter"""
    def test_writer(data):
        print(f"📡 {data}")
    return test_writer


# 真实的DSL数据 - 基于您提供的示例格式
REAL_DSL_DATA = {
    "section1": {
        "type": "section",
        "title": "基本信息",
        "description": "",
        "content": [
            {
                "type": "descriptions",
                "data": [
                    {"id": 1, "label": "统计时间", "value": "截止5月16日16:00"},
                    {"id": 2, "label": "内容量", "value": 14069},
                    {"id": 3, "label": "口碑指数", "value": "80.15%"},
                    {"id": 4, "label": "正面情感比例", "value": "80.40%"},
                    {"id": 5, "label": "负面情感比例", "value": "0.25%"}
                ]
            }
        ]
    },
    "section2": {
        "type": "section",
        "title": "AI总结",
        "description": "【整体情况】\\n权威背书强势，理想汽车通过央视新闻和明星（朱广权、岳云鹏）的参与获得了强有力的品牌背书，展现强技术口碑。\\n【核心亮点】\\n权威背书强势...展现强技术口碑。\\n【风险预警】\\n预算管理...业形象的长尾影响。\\n",
        "content": []
    },
    "section3": {
        "type": "section",
        "title": "观点分类",
        "description": "",
        "content": [
            {
                "type": "pie",
                "option": {
                    "series": [
                        {
                            "data": [
                                {"value": 67.0, "name": "权威背书 67%"},
                                {"value": 21.0, "name": "趣味互动 21%"},
                                {"value": 5.0, "name": "预算管理争议 5%"},
                                {"value": 4.0, "name": "家庭需求 4%"}
                            ]
                        }
                    ]
                }
            },
            {
                "type": "bar:negative",
                "option": {
                    "title": {"text": "观点内容情感分布"},
                    "legend": {"data": ["负向", "正向"]},
                    "xAxis": {"type": "value"},
                    "yAxis": {
                        "type": "category",
                        "data": ["权威背书", "趣味互动", "预算管理争议", "家庭需求"]
                    },
                    "series": [
                        {"name": "负向", "data": [0, 0, 4, 0]},
                        {"name": "正向", "data": [83, 76, 58, 72]}
                    ]
                }
            },
            {
                "type": "table",
                "title": "观点详情",
                "description": "各观点详细信息",
                "columns": [
                    {"prop": "viewpoint", "label": "观点"},
                    {"prop": "explain", "label": "解释说明"},
                    {"prop": "positive", "label": "正向"},
                    {"prop": "negative", "label": "负向"}
                ],
                "data": [
                    {
                        "id": 1,
                        "viewpoint": "权威背书",
                        "explain": "央视新闻和明星（如岳云鹏、朱广权）的参与和认可能为理想汽车提供了权威背书，增强了品牌公信力，并展示其技术领先性。",
                        "positive": "-朱广权点赞理想MEGA的\\\"公路高铁级\\\"静谧性\\n-岳云鹏称车内安静得像魔法\\n-央视新闻为理想汽车技术实力提供有力背书\\n-朱广权肯定激光雷达实现的全天候主动安全能力\\n-岳云鹏演示车载语音助手\\\"理想同学\\\"识别方言",
                        "negative": "-理想汽车与央视合作被质疑噱头大于价值"
                    },
                    {
                        "id": 2,
                        "viewpoint": "趣味互动",
                        "explain": "直播中的幽默互动（如理想同学的接梗能力、岳云鹏和朱广权的表现）受到网友广泛好评，认为形式生动有趣且展现了科技魅力。",
                        "positive": "-李想、朱广权、岳云鹏，这阵容太豪华！\\n-车载语音助手今晚报位出道！\\n-直播期间车载语音助手直接封神！\\n-理想同学比朱广权老师还有梗的！\\n-这直播太有意思了！岳云鹏和朱广权的组合太绝了",
                        "negative": ""
                    },
                    {
                        "id": 3,
                        "viewpoint": "预算管理争议",
                        "explain": "李想严格审批预算的习惯引发讨论，部分网友认可其节约作风，但也有人质疑管理方式，同时存在单纯赞美（如\\\"666\\\"）的声音。",
                        "positive": "-谨慎就对了不要不怎么经营好企业\\n-只有这样做，理想汽车才会上走得更高更远的巅峰[哇]\\n-预算把控严格程度上来说绝对是为消费者负责\\n-公司大了，各个环节稍微松一松，很多钱就出去了。相当轻微紧一紧，也能节省很多钱\\n-理想的活动邀请基本看不到两类汽车博主",
                        "negative": "-市值两千多亿的企业，两万元的费用都要老板自己批\\n-2万块都要亲自审批，有李想这样的老板打工人享福了\\n-难怪呢，其实我之前参加各品牌的活动，就注意到一个现象\\n-抠厂实至名归\\n-年度\\\"最抠\\\"？某新势力CEO：2万元都自己审批"
                    },
                    {
                        "id": 4,
                        "viewpoint": "家庭需求",
                        "explain": "网友高度评价理想汽车对家庭用户（如儿童安全、舒适性和智能化功能）的重视，认为其是理想的家庭用车选择。",
                        "positive": "-理想汽车创造幸福的家\\n-理想L9的舒适性能满足母亲需要\\n-理想汽车真的很注重家庭\\n-理想汽车是家的定位，更加注重家人的完全\\n-理想汽车对孩子对家人的保护绝对是首屈一指的",
                        "negative": ""
                    }
                ]
            }
        ]
    }
}


def create_real_test_state(
        user_input: str = "生成理想汽车品牌舆情分析报告",
        session_id: str = "real_test_session",
        workflow_status: WorkflowStatus = WorkflowStatus.REPORT,
        report_dsl: dict = None
) -> SpecificState:
    """创建真实测试状态"""

    state = create_initial_state(
        session_id=session_id,
        user_input=user_input,
        user_id="real_test_user",
        report_dsl=report_dsl or REAL_DSL_DATA
    )

    # 更新状态
    state["workflow_status"] = workflow_status

    return state


def test_report_agent_sync():
    """同步测试函数"""
    print("=== 直接测试ReportAgent ===")
    
    # 创建工作流和writer
    workflow = SpecificWorkflow()
    writer = get_test_stream_writer()
    
    # 创建测试状态 - 就这么简单！
    state1 = create_real_test_state(user_input="帮我生成品牌舆情分析报告")
    
    print(f"输入: {state1['user_input']}")
    print(f"状态: {state1['workflow_status']}")
    
    # 运行async函数
    async def run_test():
        print("\n🚀 开始执行ReportAgent...")
        result1 = await workflow.report_agent.execute(state1, writer)
        
        print(f"\n📊 执行结果:")
        print(f"路由目标: {result1.goto}")
        print(f"工作流状态: {result1.update.get('workflow_status')}")
        
        if result1.update.get('workflow_status') == WorkflowStatus.COMPLETED:
            print("✅ 成功!")
            
            final_report = result1.update.get('final_report', '')
            html_report = result1.update.get('html_report', '')
            
            print(f"📝 文本报告: {len(final_report)} 字符")
            print(f"📄 HTML报告: {len(html_report)} 字符")
            
            if final_report:
                print(f"\n📖 报告预览:")
                preview = final_report[:200] + "..." if len(final_report) > 200 else final_report
                print(preview)
                
        else:
            print("❌ 失败!")
            error_info = result1.update.get('error_info', {})
            print(f"错误: {error_info.get('message', 'Unknown error')}")
    
    # 运行测试
    asyncio.run(run_test())
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    test_report_agent_sync()
