#!/usr/bin/env python3
"""
ExecutionAgent简单测试 - 直接调用workflow.execution_agent.execute
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))


def create_brand_task_plan():
    """创建品牌舆情分析任务计划"""
    return {
        "title": "京东外卖品牌舆情分析执行计划",
        "steps": [
            {
                "title": "品牌数据收集阶段",
                "description": "调用品牌MCP Tool获取品牌事件数据，这是长时间异步任务，MCP Server会异步执行并在完成后回调LangGraph流程"
            },
            {
                "title": "获取品牌事件图表DSL配置", 
                "description": "获取品牌事件图表DSL配置，为报告生成做准备"
            },
            {
                "title": "报告生成阶段",
                "description": "根据品牌事件图表DSL调用报表服务Tool生成完整的品牌舆情分析报告"
            },
            {
                "title": "整理和格式化最终输出结果",
                "description": "整理和格式化最终输出结果，确保交付质量"
            }
        ]
    }


def create_test_state():
    """创建测试状态"""
    from src.models.state import WorkflowStatus
    
    return {
        "session_id": "execution_test_001",
        "user_input": "请分析京东外卖品牌在近30天的舆情表现，包括用户情感分析、热点事件识别和竞品对比",
        "task_plan": create_brand_task_plan(),
        "current_step_index": 0,
        "execution_started": False,
        "execution_results": [],
        "workflow_status": WorkflowStatus.EXECUTING,
        "messages": []
    }


async def test_execution_agent():
    """测试ExecutionAgent"""
    print("🚀 测试ExecutionAgent - 品牌舆情分析")
    print("=" * 50)
    
    try:
        from src.core.workflow import SpecificWorkflow
        
        # 创建workflow
        workflow = SpecificWorkflow()
        print("✅ Workflow创建成功")
        
        # 检查execution_agent
        print(f"✅ ExecutionAgent类型: {type(workflow.execution_agent)}")
        print(f"✅ ExecutionAgent能力: {len(workflow.execution_agent.get_capabilities())} 项")
        
        # 创建测试状态
        test_state = create_test_state()
        task_plan = test_state["task_plan"]
        
        print(f"\n📋 任务计划: {task_plan['title']}")
        print(f"📊 步骤数量: {len(task_plan['steps'])}")
        
        # 显示步骤
        print(f"\n📝 执行步骤:")
        for i, step in enumerate(task_plan['steps']):
            print(f"  {i+1}. {step['title']}")
            print(f"     {step['description'][:60]}...")
        
        # 执行任务
        print(f"\n⚡ 开始执行任务...")
        result = await workflow.execution_agent.execute(test_state)
        
        print(f"✅ 执行完成")
        print(f"🎯 路由目标: {result.goto}")
        print(f"📦 更新字段: {list(result.update.keys())}")
        
        # 检查执行结果
        if "execution_results" in result.update:
            results = result.update["execution_results"]
            print(f"\n📈 执行统计:")
            print(f"  - 总步骤: {len(results)}")
            print(f"  - 成功步骤: {len([r for r in results if not r.get('error', False)])}")
            
            # 显示每个步骤结果
            for i, step_result in enumerate(results):
                status = "✅" if not step_result.get('error', False) else "❌"
                print(f"  {status} 步骤 {i+1}: {step_result['step_title']}")
        
        if "execution_report" in result.update:
            report = result.update["execution_report"]
            print(f"\n📝 执行报告: {len(report)} 字符")
            
            # 检查关键信息
            key_terms = ["品牌数据收集", "DSL配置", "报告生成", "异步"]
            found = [term for term in key_terms if term in report]
            print(f"📄 包含关键信息: {found}")
        
        print(f"\n🎉 ExecutionAgent测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_single_step():
    """测试单步执行"""
    print(f"\n🎯 测试单步执行...")
    
    try:
        from src.core.workflow import SpecificWorkflow
        
        workflow = SpecificWorkflow()
        
        # 测试单步执行
        step_result = await workflow.execution_agent.execute_step(
            user_input="请分析京东外卖品牌舆情",
            plan_title="品牌舆情分析",
            current_step=0,
            step_details={
                "title": "品牌数据收集阶段",
                "description": "调用品牌MCP Tool获取品牌事件数据"
            }
        )
        
        print(f"✅ 单步执行成功")
        print(f"📝 结果长度: {len(step_result)} 字符")
        print(f"📄 结果预览: {step_result[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 单步测试失败: {e}")
        return False


async def test_agent_info():
    """测试Agent信息"""
    print(f"\n📋 测试Agent信息...")
    
    try:
        from src.core.workflow import SpecificWorkflow
        
        workflow = SpecificWorkflow()
        
        # 获取能力
        capabilities = workflow.execution_agent.get_capabilities()
        print(f"💪 Agent能力 ({len(capabilities)} 项):")
        for i, cap in enumerate(capabilities, 1):
            print(f"  {i}. {cap}")
        
        # 获取角色信息
        role_info = workflow.execution_agent.get_role_info()
        print(f"\n🎭 角色信息:")
        print(f"  角色名称: {role_info['role_name']}")
        print(f"  系统提示: {role_info['system_prompt'][:80]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent信息测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🧪 ExecutionAgent测试套件")
    print("=" * 60)
    
    tests = [
        ("Agent信息测试", test_agent_info),
        ("单步执行测试", test_single_step),
        ("完整执行测试", test_execution_agent)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 运行: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n📊 测试结果:")
    print("-" * 40)
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总结: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！")
        print("\n💡 ExecutionAgent特性:")
        print("✅ React Agent集成")
        print("✅ MCP工具支持")
        print("✅ 异步执行能力")
        print("✅ 品牌舆情分析")
        print("✅ DSL配置生成")
        print("✅ 报告生成服务")
    else:
        print("⚠️ 部分测试失败")


if __name__ == "__main__":
    asyncio.run(main())
