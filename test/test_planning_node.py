#!/usr/bin/env python3
"""
测试规划节点的真实测试用例
支持不同分支的状态测试
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))

from src.core.workflow import SpecificWorkflow
from src.models.state import SpecificState, WorkflowStatus, create_initial_state
from langchain_core.messages import HumanMessage, AIMessage
# StreamWriter处理
def get_test_stream_writer():
    """获取测试用的StreamWriter"""
    def test_writer(data):
        print(f"📡 StreamWriter: {data}")
    return test_writer

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


def create_test_state(
    user_input: str,
    intent_summary: str = "",
    planning_round: int = 0,
    plan_approved: bool = False,
    task_plan: dict = None,
    messages: list = None,
    workflow_status: WorkflowStatus = WorkflowStatus.PLANNING
) -> SpecificState:
    """创建测试状态"""
    state = create_initial_state(
        session_id="test_session",
        user_input=user_input,
        user_id="test_user"
    )

    # 更新状态
    state["intent_summary"] = intent_summary
    state["planning_round"] = planning_round
    state["plan_approved"] = plan_approved
    state["workflow_status"] = workflow_status
    state["intent_clarified"] = True
    state["intent_approved"] = True

    if task_plan:
        state["task_plan"] = task_plan

    if messages:
        state["messages"] = messages

    return state


def test_planning_node():
    """测试规划节点的各种场景"""
    print("=== 测试规划节点 ===")

    # 创建工作流实例
    workflow = SpecificWorkflow()

    # 测试场景1：首次制定计划
    print("\n1. 测试首次制定计划")
    test_first_time_planning(workflow)

    # 测试场景2：用户确认计划
    print("\n2. 测试用户确认计划")
    test_user_approve_plan(workflow)

    # 测试场景3：用户要求修改计划
    print("\n3. 测试用户要求修改计划")
    test_user_modify_plan(workflow)

    # 测试场景4：计划修改后再次确认
    print("\n4. 测试计划修改后再次确认")
    test_plan_revision_approval(workflow)

    print("\n=== 测试完成 ===")


def test_first_time_planning(workflow):
    """测试首次制定计划的场景"""
    try:
        writer = get_test_stream_writer()
        
        # 创建首次规划的状态
        state = create_test_state(
            user_input="分析京东外卖品牌在近30天的舆情表现",
            intent_summary="分析京东外卖品牌在近30天的舆情表现，包括用户情感分析、热点事件识别和竞品对比",
            planning_round=0
        )

        print(f"用户需求: {state['user_input']}")
        print(f"意图总结: {state['intent_summary']}")
        print(f"规划轮次: {state['planning_round']}")

        # 直接调用规划Agent
        result = workflow.planning_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")
        print(f"更新数据键: {list(result.update.keys())}")

        # 验证结果
        assert result.goto == "__end__", "首次规划应该路由到__end__等待用户确认"
        assert "task_plan" in result.update, "应该包含任务计划"
        assert result.update["plan_approved"] == False, "计划应该未审批"
        assert "messages" in result.update, "应该包含计划展示消息"

        # 验证计划内容
        task_plan = result.update["task_plan"]
        assert "title" in task_plan, "计划应该有标题"
        assert "steps" in task_plan, "计划应该有步骤"
        assert len(task_plan["steps"]) > 0, "计划应该有具体步骤"

        print(f"计划标题: {task_plan['title']}")
        print(f"计划步骤数: {len(task_plan['steps'])}")

        # 注意：使用真实StreamWriter时，消息会直接发送，无法在测试中验证
        # 这里只验证核心业务逻辑
        print("✓ 流式消息已通过StreamWriter发送")
        print("✓ 首次制定计划测试通过")

    except Exception as e:
        print(f"✗ 首次制定计划测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_user_approve_plan(workflow):
    """测试用户确认计划的场景"""
    try:
        writer = get_stream_writer()
        
        # 创建用户确认计划的状态
        existing_plan = {
            "title": "京东外卖品牌舆情分析执行计划",
            "steps": [
                {"title": "数据收集", "description": "收集相关数据"},
                {"title": "数据分析", "description": "分析收集的数据"}
            ]
        }

        messages = [
            HumanMessage(content="分析京东外卖品牌在近30天的舆情表现"),
            AIMessage(content="我已为您制定了详细的执行计划..."),
            HumanMessage(content="计划很好，请按照这个计划执行")  # 用户确认
        ]

        state = create_test_state(
            user_input="分析京东外卖品牌在近30天的舆情表现",
            intent_summary="分析京东外卖品牌在近30天的舆情表现",
            planning_round=1,
            task_plan=existing_plan,
            messages=messages
        )

        print(f"用户反馈: {messages[-1].content}")
        print(f"规划轮次: {state['planning_round']}")

        # 直接调用规划Agent
        result = workflow.planning_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")

        # 验证结果
        assert result.goto == "execution", "用户确认后应该路由到execution"
        assert result.update["plan_approved"] == True, "计划应该被标记为已审批"
        assert result.update["execution_started"] == True, "应该标记执行开始"
        assert result.update["human_approval_required"] == False, "应该清除人工审批标志"

        # 注意：使用真实StreamWriter时，消息会直接发送，无法在测试中验证
        print("✓ 流式消息已通过StreamWriter发送")

        print("✓ 用户确认计划测试通过")

    except Exception as e:
        print(f"✗ 用户确认计划测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_user_modify_plan(workflow):
    """测试用户要求修改计划的场景"""
    try:
        writer = get_stream_writer()
        
        # 创建用户要求修改计划的状态
        existing_plan = {
            "title": "京东外卖品牌舆情分析执行计划",
            "steps": [
                {"title": "数据收集", "description": "收集相关数据"},
                {"title": "数据分析", "description": "分析收集的数据"}
            ]
        }

        messages = [
            HumanMessage(content="分析京东外卖品牌在近30天的舆情表现"),
            AIMessage(content="我已为您制定了详细的执行计划..."),
            HumanMessage(content="计划需要调整，请增加竞品对比分析的步骤")  # 用户要求修改
        ]

        state = create_test_state(
            user_input="分析京东外卖品牌在近30天的舆情表现",
            intent_summary="分析京东外卖品牌在近30天的舆情表现",
            planning_round=1,
            task_plan=existing_plan,
            messages=messages
        )

        print(f"用户反馈: {messages[-1].content}")
        print(f"规划轮次: {state['planning_round']}")

        # 直接调用规划Agent
        result = workflow.planning_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")

        # 验证结果
        assert result.goto == "__end__", "修改计划后应该路由到__end__等待再次确认"
        assert "task_plan" in result.update, "应该包含修改后的计划"
        assert result.update["plan_approved"] == False, "修改后的计划应该未审批"
        assert result.update["planning_round"] == 2, "规划轮次应该递增"

        # 验证计划被修改
        new_plan = result.update["task_plan"]
        assert new_plan["title"] != existing_plan["title"] or len(new_plan["steps"]) != len(existing_plan["steps"]), "计划应该被修改"

        print(f"新计划标题: {new_plan['title']}")
        print(f"新计划步骤数: {len(new_plan['steps'])}")

        # 注意：使用真实StreamWriter时，消息会直接发送，无法在测试中验证
        print("✓ 流式消息已通过StreamWriter发送")

        print("✓ 用户要求修改计划测试通过")

    except Exception as e:
        print(f"✗ 用户要求修改计划测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_plan_revision_approval(workflow):
    """测试计划修改后再次确认的场景"""
    try:
        writer = get_stream_writer()
        
        # 创建计划修改后再次确认的状态
        revised_plan = {
            "title": "京东外卖品牌舆情分析执行计划（修订版）",
            "steps": [
                {"title": "数据收集", "description": "收集相关数据"},
                {"title": "数据分析", "description": "分析收集的数据"},
                {"title": "竞品对比", "description": "与竞品进行对比分析"}
            ]
        }

        messages = [
            HumanMessage(content="分析京东外卖品牌在近30天的舆情表现"),
            AIMessage(content="我已为您制定了详细的执行计划..."),
            HumanMessage(content="计划需要调整，请增加竞品对比分析的步骤"),
            AIMessage(content="我已根据您的反馈调整了执行计划..."),
            HumanMessage(content="现在的计划很完善，请执行")  # 用户确认修订版
        ]

        state = create_test_state(
            user_input="分析京东外卖品牌在近30天的舆情表现",
            intent_summary="分析京东外卖品牌在近30天的舆情表现",
            planning_round=2,
            task_plan=revised_plan,
            messages=messages
        )

        print(f"用户反馈: {messages[-1].content}")
        print(f"规划轮次: {state['planning_round']}")

        # 直接调用规划Agent
        result = workflow.planning_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")

        # 验证结果
        assert result.goto == "execution", "确认修订版后应该路由到execution"
        assert result.update["plan_approved"] == True, "修订版计划应该被标记为已审批"
        assert result.update["execution_started"] == True, "应该标记执行开始"

        print("✓ 计划修改后再次确认测试通过")

    except Exception as e:
        print(f"✗ 计划修改后再次确认测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_planning_agent_capabilities():
    """测试PlanningAgent的能力"""
    print("\n=== 测试PlanningAgent能力 ===")

    workflow = SpecificWorkflow()

    try:
        capabilities = workflow.planning_agent.get_capabilities()
        print("PlanningAgent能力:")
        for i, capability in enumerate(capabilities, 1):
            print(f"   {i}. {capability}")

        role_info = workflow.planning_agent.get_role_info()
        print(f"\n角色信息: {role_info['role_name']}")
        print(f"描述: {role_info.get('description', 'N/A')}")

    except Exception as e:
        print(f"获取能力失败: {e}")


if __name__ == "__main__":
    test_planning_node()
    test_planning_agent_capabilities()
