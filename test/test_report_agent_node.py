#!/usr/bin/env python3
"""
测试ReportAgent的真实测试用例
参考test_intent_clarification_node.py的风格，直接运行真实的工作流
"""

import sys
import os
import logging
import asyncio
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))

from src.core.workflow import SpecificWorkflow
from src.models.state import SpecificState, WorkflowStatus, create_initial_state
from langchain_core.messages import HumanMessage, AIMessage

# StreamWriter处理
def get_test_stream_writer():
    """获取测试用的StreamWriter"""
    def test_writer(data):
        print(f"📡 StreamWriter: {data}")
    return test_writer

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# 示例DSL数据
SAMPLE_DSL = {
    "section1": {
        "type": "section",
        "title": "基本信息",
        "description": "",
        "content": [
            {
                "type": "descriptions",
                "data": [
                    {"id": 1, "label": "统计时间", "value": "截止5月16日16:00"},
                    {"id": 2, "label": "内容量", "value": 14069},
                    {"id": 3, "label": "口碑指数", "value": "80.15%"},
                    {"id": 4, "label": "正面情感比例", "value": "80.40%"},
                    {"id": 5, "label": "负面情感比例", "value": "0.25%"}
                ]
            }
        ]
    },
    "section2": {
        "type": "section",
        "title": "AI总结",
        "description": "【整体情况】\\n权威背书强势，理想汽车通过央视新闻和明星（朱广权、岳云鹏）的参与获得了强有力的品牌背书，展现了技术实力和产品优势。\\n【核心亮点】\\n权威背书强势：央视新闻为理想汽车技术实力提供有力背书，明星认可进一步增强品牌公信力。\\n趣味互动受好评：直播中的幽默互动和车载语音助手的表现获得网友广泛好评。\\n家庭定位明确：理想汽车在家庭用车方面的优势得到用户认可。\\n【风险预警】\\n预算管理争议：李想严格审批预算的做法引发部分争议，需要平衡节约与企业形象的关系。",
        "content": []
    },
    "section3": {
        "type": "section",
        "title": "观点分类",
        "description": "",
        "content": [
            {
                "type": "pie",
                "option": {
                    "series": [
                        {
                            "data": [
                                {"value": 67.0, "name": "权威背书 67%"},
                                {"value": 21.0, "name": "趣味互动 21%"},
                                {"value": 5.0, "name": "预算管理争议 5%"},
                                {"value": 4.0, "name": "家庭需求 4%"}
                            ]
                        }
                    ]
                }
            },
            {
                "type": "table",
                "title": "观点详情",
                "data": [
                    {
                        "id": 1,
                        "viewpoint": "权威背书",
                        "explain": "央视新闻和明星（如岳云鹏、朱广权）的参与和认可能为理想汽车提供了权威背书，增强了品牌公信力，并展示其技术领先性。",
                        "positive": "朱广权点赞理想MEGA的\"公路高铁级\"静谧性\\n岳云鹏称车内安静得像魔法\\n央视新闻为理想汽车技术实力提供有力背书",
                        "negative": "理想汽车与央视合作被质疑噱头大于价值"
                    },
                    {
                        "id": 2,
                        "viewpoint": "趣味互动",
                        "explain": "直播中的幽默互动（如理想同学的接梗能力、岳云鹏和朱广权的表现）受到网友广泛好评，认为形式生动有趣且展现了科技魅力。",
                        "positive": "李想、朱广权、岳云鹏，这阵容太豪华！\\n车载语音助手今晚报位出道！\\n直播期间车载语音助手直接封神！",
                        "negative": ""
                    }
                ]
            }
        ]
    }
}


def create_test_state(
    user_input: str = "生成品牌舆情分析报告",
    session_id: str = "test_session",
    workflow_status: WorkflowStatus = WorkflowStatus.REPORT,
    report_dsl: dict = None,
    extensions: dict = None
) -> SpecificState:
    """创建测试状态"""
    state = create_initial_state(
        session_id=session_id,
        user_input=user_input,
        user_id="test_user",
        report_dsl=report_dsl or SAMPLE_DSL,
        extensions=extensions
    )

    # 更新状态
    state["workflow_status"] = workflow_status

    return state


def test_report_agent_node():
    """测试ReportAgent节点的各种场景"""
    print("=== 测试ReportAgent节点 ===")

    # 创建工作流实例
    workflow = SpecificWorkflow()

    # 测试场景1：基本DSL解析功能
    print("\n1. 测试基本DSL解析功能")
    test_dsl_parsing(workflow)

    # 测试场景2：文本总结生成
    print("\n2. 测试文本总结生成")
    test_text_summary_generation(workflow)

    # 测试场景3：品牌分析DSL创建
    print("\n3. 测试品牌分析DSL创建")
    test_brand_dsl_creation(workflow)

    # 测试场景4：完整execute流程（模拟外部API）
    print("\n4. 测试完整execute流程（模拟外部API）")
    asyncio.run(test_execute_flow_mock(workflow))

    # 测试场景5：无DSL数据的默认处理
    print("\n5. 测试无DSL数据的默认处理")
    asyncio.run(test_execute_without_dsl(workflow))

    print("\n=== 测试完成 ===")


def test_dsl_parsing(workflow):
    """测试DSL解析功能"""
    try:
        print(f"输入DSL节数: {len(SAMPLE_DSL)}")

        # 直接调用ReportAgent的解析方法
        summary_data = workflow.report_agent.parse_dsl_summary(SAMPLE_DSL)

        print(f"返回类型: {type(summary_data)}")
        print(f"解析结果: {json.dumps(summary_data, indent=2, ensure_ascii=False)}")

        # 验证解析结果
        assert summary_data is not None, "解析结果不应为空"
        assert "ai_summary" in summary_data, "应该包含AI总结"
        assert "key_metrics" in summary_data, "应该包含关键指标"
        assert "viewpoints" in summary_data, "应该包含观点分析"
        assert "charts_info" in summary_data, "应该包含图表信息"

        # 验证AI总结
        assert len(summary_data["ai_summary"]) > 0, "AI总结不应为空"
        assert "权威背书强势" in summary_data["ai_summary"], "AI总结应包含关键内容"

        # 验证关键指标
        assert len(summary_data["key_metrics"]) == 5, "应该有5个关键指标"
        assert summary_data["key_metrics"][0]["label"] == "统计时间", "第一个指标应该是统计时间"

        # 验证观点分析
        assert len(summary_data["viewpoints"]) == 2, "应该有2个观点"
        assert summary_data["viewpoints"][0]["viewpoint"] == "权威背书", "第一个观点应该是权威背书"

        # 验证图表信息
        assert len(summary_data["charts_info"]) >= 1, "应该至少有1个图表"
        assert summary_data["charts_info"][0]["type"] == "pie", "第一个图表应该是饼图"

        print(f"AI总结长度: {len(summary_data['ai_summary'])} 字符")
        print(f"关键指标数量: {len(summary_data['key_metrics'])} 个")
        print(f"观点分析数量: {len(summary_data['viewpoints'])} 个")
        print(f"图表信息数量: {len(summary_data['charts_info'])} 个")

        # 详细显示图表信息
        for i, chart in enumerate(summary_data['charts_info']):
            print(f"  图表{i+1}: {chart['type']} - {chart['title']}")
        print("✓ DSL解析功能测试通过")

    except Exception as e:
        print(f"✗ DSL解析功能测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_text_summary_generation(workflow):
    """测试文本总结生成"""
    try:
        # 先解析DSL
        summary_data = workflow.report_agent.parse_dsl_summary(SAMPLE_DSL)

        # 生成文本总结
        text_summary = workflow.report_agent.generate_text_summary(summary_data)

        print(f"返回类型: {type(text_summary)}")
        print(f"文本总结长度: {len(text_summary)} 字符")

        # 验证文本总结
        assert text_summary is not None, "文本总结不应为空"
        assert len(text_summary) > 0, "文本总结应有内容"
        assert "# 品牌舆情分析报告" in text_summary, "应包含报告标题"
        assert "## AI智能总结" in text_summary, "应包含AI总结部分"
        assert "## 关键指标" in text_summary, "应包含关键指标部分"
        assert "## 观点分析" in text_summary, "应包含观点分析部分"
        assert "## 可视化图表" in text_summary, "应包含图表信息部分"

        # 验证内容包含
        assert "权威背书强势" in text_summary, "应包含AI总结内容"
        assert "统计时间" in text_summary, "应包含关键指标"
        assert "权威背书" in text_summary, "应包含观点分析"

        print(f"文本总结预览: {text_summary[:200]}...")
        print("✓ 文本总结生成测试通过")

    except Exception as e:
        print(f"✗ 文本总结生成测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_brand_dsl_creation(workflow):
    """测试品牌分析DSL创建"""
    try:
        # 创建默认DSL
        default_dsl = workflow.report_agent.create_brand_analysis_dsl()

        print(f"默认DSL节数: {len(default_dsl)}")
        print(f"默认品牌: 京东")

        # 验证默认DSL
        assert default_dsl is not None, "默认DSL不应为空"
        assert "01" in default_dsl, "应包含第一节"
        assert "02" in default_dsl, "应包含第二节"
        assert "03" in default_dsl, "应包含第三节"
        assert default_dsl["01"]["title"] == "品牌舆情分析概览", "第一节标题应正确"
        assert "京东" in default_dsl["01"]["description"], "应包含默认品牌名称"

        # 创建自定义DSL
        custom_analysis_data = {
            "sentiment_score": 85,
            "mention_count": 2000,
            "positive_ratio": 0.75,
            "negative_ratio": 0.15,
            "neutral_ratio": 0.10
        }

        custom_dsl = workflow.report_agent.create_brand_analysis_dsl(
            brand_name="理想汽车",
            event_id="test_event_123",
            analysis_data=custom_analysis_data
        )

        print(f"自定义DSL节数: {len(custom_dsl)}")
        print(f"自定义品牌: 理想汽车")

        # 验证自定义DSL
        assert "理想汽车" in custom_dsl["01"]["description"], "应包含自定义品牌名称"
        assert custom_dsl["01"]["content"][0]["data"][0]["value"] == "理想汽车", "品牌名称应正确"
        assert custom_dsl["01"]["content"][0]["data"][2]["value"] == "85分", "舆情评分应正确"

        print("✓ 品牌分析DSL创建测试通过")

    except Exception as e:
        print(f"✗ 品牌分析DSL创建测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_execute_flow_mock(workflow):
    """测试完整execute流程（模拟外部API）"""
    try:
        from unittest.mock import patch, MagicMock

        writer = get_test_stream_writer()

        # 创建包含DSL的状态
        state = create_test_state(
            user_input="生成理想汽车品牌舆情分析报告",
            session_id="test_session_001",
            report_dsl=SAMPLE_DSL,
            extensions={
                "tokens": [{"access_token": "test_token"}]
            }
        )

        print(f"输入: {state['user_input']}")
        print(f"会话ID: {state['session_id']}")
        print(f"DSL节数: {len(state['report_dsl'])}")
        print(f"访问令牌: 已提供")

        # Mock外部API调用
        with patch('httpx.AsyncClient') as mock_client:
            # Mock报告生成响应
            mock_report_response = MagicMock()
            mock_report_response.status_code = 200
            mock_report_response.text = """
<!DOCTYPE html>
<html lang="zh-CN">
<head><title>品牌舆情分析报告</title></head>
<body>
    <h1>理想汽车品牌舆情分析报告</h1>
    <div class="summary">
        <h2>AI总结</h2>
        <p>权威背书强势，理想汽车通过央视新闻和明星的参与获得了强有力的品牌背书...</p>
    </div>
    <div class="metrics">
        <h2>关键指标</h2>
        <ul>
            <li>统计时间: 截止5月16日16:00</li>
            <li>内容量: 14069</li>
            <li>口碑指数: 80.15%</li>
        </ul>
    </div>
</body>
</html>
"""

            # Mock S3上传响应
            mock_upload_response = MagicMock()
            mock_upload_response.status_code = 200
            mock_upload_response.json.return_value = {
                "url": "https://s3.amazonaws.com/xuanji-reports/report-test_session_001-20240116-143022.html",
                "key": "report-test_session_001-20240116-143022.html",
                "bucket": "xuanji-reports",
                "size": 1024,
                "contentType": "text/html"
            }

            # 设置mock客户端返回不同的响应
            mock_client_instance = mock_client.return_value.__aenter__.return_value
            mock_client_instance.post.side_effect = [mock_report_response, mock_upload_response]

            # 直接调用ReportAgent的execute方法
            result = await workflow.report_agent.execute(state, writer)

            print(f"返回类型: {type(result)}")
            print(f"路由目标: {result.goto}")
            print(f"更新数据键: {list(result.update.keys())}")

            # 验证结果
            assert result.goto == "__end__", "应该路由到结束"
            assert result.update["workflow_status"] == WorkflowStatus.COMPLETED, "工作流状态应该是完成"
            assert "final_report" in result.update, "应该包含最终报告"
            assert "html_report" in result.update, "应该包含HTML报告"
            assert "summary_data" in result.update, "应该包含解析数据"
            assert "upload_result" in result.update, "应该包含上传结果"

            # 验证final_report内容
            final_report = result.update["final_report"]
            assert "# 品牌舆情分析报告" in final_report, "应包含报告标题"
            assert "## AI智能总结" in final_report, "应包含AI总结"
            assert "权威背书强势" in final_report, "应包含AI总结内容"

            # 验证HTML报告
            html_report = result.update["html_report"]
            assert len(html_report) > 0, "HTML报告不应为空"
            assert "理想汽车品牌舆情分析报告" in html_report, "HTML应包含报告标题"

            # 验证summary_data
            summary_data = result.update["summary_data"]
            assert len(summary_data["ai_summary"]) > 0, "AI总结不应为空"
            assert len(summary_data["key_metrics"]) == 5, "应该有5个关键指标"
            assert len(summary_data["viewpoints"]) == 2, "应该有2个观点"

            # 验证upload_result
            upload_result = result.update["upload_result"]
            assert upload_result["success"] is True, "上传应该成功"
            assert "url" in upload_result["upload_result"], "应该包含访问URL"

            print(f"工作流状态: {result.update['workflow_status']}")
            print(f"HTML报告长度: {len(result.update['html_report'])} 字符")
            print(f"文本报告长度: {len(result.update['final_report'])} 字符")
            print(f"上传状态: {upload_result['success']}")
            print(f"访问链接: {upload_result['upload_result']['url']}")
            print("✓ 完整execute流程测试通过")

    except Exception as e:
        print(f"✗ 完整execute流程测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_execute_without_dsl(workflow):
    """测试无DSL数据的默认处理"""
    try:
        from unittest.mock import patch, MagicMock

        writer = get_test_stream_writer()

        # 创建没有DSL的状态
        state = create_test_state(
            user_input="生成品牌舆情分析报告",
            session_id="test_session_002",
            report_dsl=None  # 没有DSL数据
        )

        print(f"输入: {state['user_input']}")
        print(f"会话ID: {state['session_id']}")
        print(f"DSL数据: 无")

        # Mock外部API调用
        with patch('httpx.AsyncClient') as mock_client:
            # Mock报告生成响应
            mock_report_response = MagicMock()
            mock_report_response.status_code = 200
            mock_report_response.text = "<html><head><title>默认报告</title></head><body><h1>京东品牌舆情分析报告</h1></body></html>"

            # Mock S3上传响应
            mock_upload_response = MagicMock()
            mock_upload_response.status_code = 200
            mock_upload_response.json.return_value = {
                "url": "https://s3.amazonaws.com/xuanji-reports/default-report.html",
                "key": "default-report.html"
            }

            mock_client_instance = mock_client.return_value.__aenter__.return_value
            mock_client_instance.post.side_effect = [mock_report_response, mock_upload_response]

            # 直接调用ReportAgent的execute方法
            result = await workflow.report_agent.execute(state, writer)

            print(f"返回类型: {type(result)}")
            print(f"路由目标: {result.goto}")

            # 验证结果
            assert result.goto == "__end__", "应该路由到结束"
            assert result.update["workflow_status"] == WorkflowStatus.COMPLETED, "工作流状态应该是完成"

            # 验证使用了默认DSL
            report_dsl = result.update["report_dsl"]

            # 检查DSL结构（可能是"01"或"section1"格式）
            if "01" in report_dsl:
                # 新格式
                assert "品牌舆情分析概览" in report_dsl["01"]["title"], "应该使用默认标题"
                assert "京东" in report_dsl["01"]["description"], "应该使用默认品牌"
                print(f"使用DSL格式: 新格式 (01, 02, 03)")
            elif "section1" in report_dsl:
                # 旧格式（测试数据格式）
                print(f"使用DSL格式: 旧格式 (section1, section2, section3)")
                print(f"注意: 测试使用了SAMPLE_DSL而不是默认DSL")
            else:
                # 其他格式
                print(f"DSL键名: {list(report_dsl.keys())}")

            print(f"默认DSL节数: {len(report_dsl)}")
            print("✓ 无DSL数据默认处理测试通过")

    except Exception as e:
        print(f"✗ 无DSL数据默认处理测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_agent_capabilities():
    """测试ReportAgent的能力"""
    print("\n=== 测试ReportAgent能力 ===")

    workflow = SpecificWorkflow()

    # 测试ReportAgent
    print("\n1. ReportAgent能力:")
    try:
        capabilities = workflow.report_agent.get_capabilities()
        for i, capability in enumerate(capabilities, 1):
            print(f"   {i}. {capability}")
    except Exception as e:
        print(f"   获取能力失败: {e}")

    # 测试Agent角色信息
    print("\n2. ReportAgent角色信息:")
    try:
        report_info = workflow.report_agent.get_role_info()
        print(f"   角色名称: {report_info['role_name']}")
        print(f"   系统提示: {report_info['system_prompt'][:100]}...")
    except Exception as e:
        print(f"   获取角色信息失败: {e}")

    # 测试配置信息
    print("\n3. ReportAgent配置信息:")
    try:
        print(f"   报告服务URL: {workflow.report_agent.base_url}")
        print(f"   上传服务URL: {workflow.report_agent.upload_url}")
        print(f"   请求超时: {workflow.report_agent.timeout}秒")
    except Exception as e:
        print(f"   获取配置信息失败: {e}")


if __name__ == "__main__":
    test_report_agent_node()
    test_agent_capabilities()
