#!/usr/bin/env python3
"""
运行ReportAgent测试的简化脚本
"""

import sys
import os
import asyncio
from unittest.mock import MagicMock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from langchain_openai import ChatOpenAI
from src.agents.report import ReportAgent
from src.models.state import WorkflowStatus


# 示例DSL数据
SAMPLE_DSL = {
    "section1": {
        "type": "section",
        "title": "基本信息",
        "content": [
            {
                "type": "descriptions",
                "data": [
                    {"id": 1, "label": "统计时间", "value": "截止5月16日16:00"},
                    {"id": 2, "label": "内容量", "value": 14069},
                    {"id": 3, "label": "口碑指数", "value": "80.15%"}
                ]
            }
        ]
    },
    "section2": {
        "type": "section",
        "title": "AI总结",
        "description": "【整体情况】\\n权威背书强势，理想汽车通过央视新闻和明星的参与获得了强有力的品牌背书。\\n【核心亮点】\\n权威背书强势，趣味互动受好评，家庭定位明确。",
        "content": []
    }
}

SAMPLE_HTML = """
<!DOCTYPE html>
<html><head><title>测试报告</title></head>
<body><h1>品牌舆情分析报告</h1><p>测试内容</p></body></html>
"""


async def test_basic_functions():
    """测试基础功能"""
    print("🧪 测试ReportAgent基础功能...")
    
    # 创建ReportAgent
    llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
    agent = ReportAgent(llm=llm)
    
    # 1. 测试DSL解析
    print("\n1️⃣ 测试DSL解析...")
    summary_data = agent.parse_dsl_summary(SAMPLE_DSL)
    
    assert summary_data is not None
    assert len(summary_data["ai_summary"]) > 0
    assert len(summary_data["key_metrics"]) == 3
    print(f"   ✅ AI总结长度: {len(summary_data['ai_summary'])}")
    print(f"   ✅ 关键指标数: {len(summary_data['key_metrics'])}")
    
    # 2. 测试文本总结生成
    print("\n2️⃣ 测试文本总结生成...")
    text_summary = agent.generate_text_summary(summary_data)
    
    assert "# 品牌舆情分析报告" in text_summary
    assert "## AI智能总结" in text_summary
    print(f"   ✅ 文本总结长度: {len(text_summary)}")
    
    # 3. 测试DSL创建
    print("\n3️⃣ 测试DSL创建...")
    brand_dsl = agent.create_brand_analysis_dsl("理想汽车")
    
    assert "理想汽车" in brand_dsl["01"]["description"]
    print(f"   ✅ 创建品牌DSL: 理想汽车")
    print(f"   ✅ DSL节数: {len(brand_dsl)}")


async def test_api_functions_mock():
    """测试API功能（使用Mock）"""
    print("\n🌐 测试API功能（Mock）...")
    
    # 创建ReportAgent
    llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
    agent = ReportAgent(llm=llm)
    
    # 4. 测试报告生成API
    print("\n4️⃣ 测试报告生成API...")
    with patch('httpx.AsyncClient') as mock_client:
        # Mock响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.text = SAMPLE_HTML
        
        mock_client.return_value.__aenter__.return_value.post.return_value = mock_response
        
        result = await agent.generate_report(SAMPLE_DSL)
        
        assert result["success"] is True
        assert "html_content" in result
        print(f"   ✅ 报告生成成功")
        print(f"   ✅ HTML长度: {len(result['html_content'])}")
    
    # 5. 测试S3上传API
    print("\n5️⃣ 测试S3上传API...")
    with patch('httpx.AsyncClient') as mock_client:
        # Mock响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "url": "https://s3.amazonaws.com/bucket/test.html",
            "key": "test.html"
        }
        
        mock_client.return_value.__aenter__.return_value.post.return_value = mock_response
        
        upload_result = await agent.upload_html_to_s3(SAMPLE_HTML, "test.html")
        
        assert upload_result["success"] is True
        assert "upload_result" in upload_result
        print(f"   ✅ S3上传成功")
        print(f"   ✅ 文件名: {upload_result['filename']}")


async def test_execute_flow():
    """测试完整执行流程"""
    print("\n🔄 测试完整执行流程...")
    
    # 创建ReportAgent
    llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
    agent = ReportAgent(llm=llm)
    
    # 创建mock writer
    messages = []
    def mock_writer(data):
        messages.append(data)
        print(f"   📢 {data.get('agent_message', data)}")
    
    # 准备state数据
    state_data = {
        "session_id": "test_session_001",
        "report_dsl": SAMPLE_DSL
    }
    
    # 6. 测试完整execute流程
    print("\n6️⃣ 测试execute完整流程...")
    with patch('httpx.AsyncClient') as mock_client:
        # Mock报告生成响应
        mock_report_response = MagicMock()
        mock_report_response.status_code = 200
        mock_report_response.text = SAMPLE_HTML
        
        # Mock S3上传响应
        mock_upload_response = MagicMock()
        mock_upload_response.status_code = 200
        mock_upload_response.json.return_value = {
            "url": "https://s3.amazonaws.com/bucket/report.html",
            "key": "report.html"
        }
        
        # 设置mock返回
        mock_client_instance = mock_client.return_value.__aenter__.return_value
        mock_client_instance.post.side_effect = [mock_report_response, mock_upload_response]
        
        # 执行
        result = await agent.execute(state_data, mock_writer)
        
        # 验证结果
        assert result.goto == "__end__"
        assert result.update["workflow_status"] == WorkflowStatus.COMPLETED
        assert "final_report" in result.update
        assert "html_report" in result.update
        assert "summary_data" in result.update
        assert "upload_result" in result.update
        
        print(f"   ✅ 执行状态: {result.update['workflow_status']}")
        print(f"   ✅ HTML报告: {len(result.update['html_report'])} 字符")
        print(f"   ✅ 文本报告: {len(result.update['final_report'])} 字符")
        print(f"   ✅ 上传成功: {result.update['upload_result']['success']}")
        print(f"   ✅ Writer消息: {len(messages)} 条")


async def test_error_handling():
    """测试错误处理"""
    print("\n❌ 测试错误处理...")
    
    # 创建ReportAgent
    llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
    agent = ReportAgent(llm=llm)
    
    # 创建mock writer
    messages = []
    def mock_writer(data):
        messages.append(data)
    
    # 准备state数据
    state_data = {
        "session_id": "test_session_error",
        "report_dsl": SAMPLE_DSL
    }
    
    # 7. 测试API失败处理
    print("\n7️⃣ 测试API失败处理...")
    with patch('httpx.AsyncClient') as mock_client:
        # Mock失败响应
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        
        mock_client.return_value.__aenter__.return_value.post.return_value = mock_response
        
        # 执行
        result = await agent.execute(state_data, mock_writer)
        
        # 验证失败结果
        assert result.goto == "__end__"
        assert result.update["workflow_status"] == WorkflowStatus.FAILED
        assert "error_info" in result.update
        
        print(f"   ✅ 错误处理正确")
        print(f"   ✅ 失败状态: {result.update['workflow_status']}")
        print(f"   ✅ 错误信息: {result.update['error_info']['message']}")


async def main():
    """主测试函数"""
    print("🎯 开始ReportAgent测试\n")
    
    try:
        # 运行所有测试
        await test_basic_functions()
        await test_api_functions_mock()
        await test_execute_flow()
        await test_error_handling()
        
        print("\n🎉 所有测试通过!")
        print("\n📊 测试总结:")
        print("  ✅ DSL解析功能")
        print("  ✅ 文本总结生成")
        print("  ✅ 品牌DSL创建")
        print("  ✅ 报告生成API")
        print("  ✅ S3上传API")
        print("  ✅ 完整执行流程")
        print("  ✅ 错误处理机制")
        
    except Exception as e:
        print(f"\n💥 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
