# 测试目录说明

本目录包含了Specific V4工作流的各个节点测试用例，参考了`test_intent_clarification_node.py`的风格，直接运行真实的工作流节点进行测试。

## 📁 测试文件结构

```
test/
├── README.md                           # 本说明文件
├── run_all_tests.py                   # 测试运行器
├── test_coordinator_node.py           # 协调节点测试
├── test_supervisor.py                 # Supervisor路由测试
├── test_supervisor_unified_analysis.py # Supervisor统一分析测试
├── test_planning_node.py              # 规划节点测试
├── test_execution_node.py             # 执行节点测试
├── test_summary_node.py               # 总结节点测试
├── test_report_agent_node.py          # ReportAgent节点测试（新增）
├── run_report_agent_test.py           # ReportAgent简化测试（新增）
├── test_workflow_integration.py       # 工作流集成测试
└── example_supervisor_unified_analysis.py # 统一分析演示示例
```

## 🚀 快速开始

### 运行所有测试
```bash
cd test
python run_all_tests.py
```

### 运行特定测试
```bash
# 运行协调节点测试
python run_all_tests.py test_coordinator_node

# 运行规划节点测试  
python run_all_tests.py test_planning_node.py

# 直接运行测试文件
python test_coordinator_node.py
```

### 查看帮助
```bash
python run_all_tests.py --help
```

## 📋 测试内容说明

### 1. test_coordinator_node.py - 协调节点测试
- **问候类输入处理**: 测试"你好"、"hi"等问候语的识别和回复
- **任务需求输入处理**: 测试品牌舆情分析需求的识别和路由
- **非初始状态路由**: 测试不同工作流状态下的路由逻辑
- **错误处理**: 测试空输入、异常长输入等边界情况

**测试场景**:
```python
# 问候语测试
"你好" -> 路由到 __end__ (友好回复)
"分析苹果iPhone15" -> 路由到 intent_clarification

# 状态路由测试
CLARIFYING_INTENT -> intent_clarification
PLANNING -> intent_clarification (当前简化实现)
```

### 2. test_planning_node.py - 规划节点测试
- **首次规划**: 测试基于用户需求生成执行计划
- **用户同意计划**: 测试用户确认计划后的流程
- **用户要求修改计划**: 测试计划修改和多轮规划
- **多轮规划**: 测试连续的计划修改过程

**测试场景**:
```python
# 首次规划
intent_summary: "分析iPhone15传播情况" -> 生成计划 -> 等待确认

# 用户同意
"好的，我同意这个计划" -> 路由到 execution

# 用户修改
"增加抖音平台分析" -> 重新生成计划 -> 等待确认
```

### 3. test_execution_node.py - 执行节点测试
- **正常执行流程**: 测试完整的计划执行过程
- **无效计划处理**: 测试缺少必要字段的计划
- **空计划处理**: 测试空步骤列表的处理
- **单步骤计划**: 测试简单计划的执行

**测试场景**:
```python
# 正常执行
3步骤计划 -> 逐步执行 -> 生成执行报告 -> 路由到 summary

# 错误处理
无效计划 -> 路由到 __end__ (FAILED状态)
空计划 -> 正常处理或错误处理
```

### 4. test_summary_node.py - 总结节点测试
- **正常总结流程**: 测试基于执行结果生成总结
- **部分失败总结**: 测试包含失败步骤的总结
- **缺少执行结果**: 测试降级总结生成
- **质量分析**: 测试执行质量评分算法

**测试场景**:
```python
# 正常总结
完整执行结果 -> 生成总结 + 质量分析 -> 路由到 __end__ (COMPLETED)

# 部分失败
混合执行结果 -> 反映失败情况的总结 -> 成功率 < 100%

# 降级处理
缺少数据 -> 降级总结 -> 包含错误信息
```

### 5. test_supervisor_unified_analysis.py - Supervisor统一分析测试
- **提示词结构验证**: 测试统一分析提示词的完整性
- **消息类型分析**: 测试6种消息类型的识别（问候、确认、补充、拒绝、详细描述、简短回复）
- **路由决策逻辑**: 测试基于消息分析的路由决策
- **数据模型集成**: 测试UserMessageAnalysis和RouterDecisionWithAnalysis模型
- **统一架构验证**: 测试一次LLM调用完成分析和决策的架构

**测试场景**:
```python
# 消息类型分析
"你好" -> message_type: "greeting" -> route: "__end__"
"好的，确认" -> message_type: "agreement" -> route: "intent_clarification"
"我想分析理想汽车..." -> message_type: "supplement" -> route: "intent_clarification"
"不对，重新来" -> message_type: "rejection" -> route: "planning"

# 路由决策逻辑
意图未澄清 -> route: "intent_clarification"
计划已批准 -> route: "execution"
执行已完成 -> route: "summary"
```

### 6. example_supervisor_unified_analysis.py - 统一分析演示示例
- **功能演示**: 展示统一分析的完整功能
- **使用示例**: 提供实际使用的代码示例
- **改进对比**: 展示改进前后的对比
- **架构说明**: 详细说明新架构的优势

### 7. test_workflow_integration.py - 工作流集成测试
- **完整工作流程**: 测试从协调到总结的完整流程
- **工作流初始化**: 测试所有组件的正确初始化
- **节点路由**: 测试不同状态下的路由逻辑
- **状态管理**: 测试状态字段的创建和更新
- **错误场景**: 测试各种异常情况的处理
- **性能指标**: 测试各节点的执行时间

## 🎯 测试特点

### 1. 真实环境测试
- **直接调用工作流节点**: 不使用Mock，直接测试真实的节点逻辑
- **真实LLM交互**: 测试与语言模型的实际交互（可能需要配置）
- **完整状态管理**: 测试真实的状态传递和更新

### 2. 全面的场景覆盖
- **正常流程**: 测试预期的成功路径
- **边界情况**: 测试空输入、无效数据等边界条件
- **错误处理**: 测试异常情况的处理和恢复
- **性能测试**: 测试执行时间和资源使用

### 3. 清晰的测试结构
- **统一的测试格式**: 所有测试文件遵循相同的结构
- **详细的断言**: 验证路由目标、状态更新、数据完整性
- **友好的输出**: 清晰的测试结果和错误信息

## 📊 测试输出示例

```
=== 测试协调节点 ===

1. 测试问候类输入
测试问候语: '你好'
  路由目标: __end__
  工作流状态: WorkflowStatus.COMPLETED
  回复消息: 您好！我是Specific品牌舆情分析助手...
  ✓ 问候语处理正确

2. 测试任务需求输入
测试任务需求: '分析苹果公司iPhone15的网络传播情况'
  路由目标: intent_clarification
  工作流状态: WorkflowStatus.CLARIFYING_INTENT
  ✓ 任务需求路由正确

=== 测试完成 ===
```

## 🔧 配置要求

### 环境依赖
- Python 3.8+
- 已安装项目依赖包
- 正确配置的LLM（如果需要真实调用）

### 运行前准备
1. 确保项目根目录在Python路径中
2. 检查LLM配置（API密钥等）
3. 确认所有依赖包已安装

### 故障排除
- **导入错误**: 检查Python路径和依赖包
- **LLM调用失败**: 检查API配置和网络连接
- **测试超时**: 调整超时设置或检查LLM响应速度

## 🎉 测试最佳实践

1. **定期运行**: 在代码修改后运行相关测试
2. **全面测试**: 使用`run_all_tests.py`进行完整测试
3. **性能监控**: 关注测试执行时间，及时优化
4. **错误分析**: 仔细分析失败的测试，找出根本原因
5. **持续改进**: 根据测试结果不断完善代码质量

## 📈 扩展测试

如需添加新的测试用例：

1. **创建新测试文件**: 遵循`test_xxx_node.py`的命名规范
2. **参考现有结构**: 使用相同的测试函数结构
3. **更新运行器**: 在`run_all_tests.py`中添加新测试文件
4. **文档更新**: 在本README中添加新测试的说明

## 📊 ReportAgent测试说明

### 新增测试文件
- `test_report_agent_node.py` - ReportAgent真实工作流测试（参考intent_clarification风格）
- `test_report_agent_minimal.py` - 最简单的ReportAgent测试（推荐）
- `test_report_agent_simple.py` - 完整的ReportAgent测试
- `test_report_agent_real.py` - 真实外部API测试
- `run_report_agent_test.py` - ReportAgent简化测试脚本

### 测试覆盖范围
- ✅ **DSL解析功能** - 测试从DSL中提取AI总结、关键指标、观点分析
- ✅ **文本总结生成** - 测试基于解析数据生成Markdown格式报告
- ✅ **品牌分析DSL创建** - 测试动态创建品牌分析DSL数据
- ✅ **报告生成API** - 测试调用外部报告服务（Mock）
- ✅ **S3上传功能** - 测试HTML报告上传到云存储（Mock）
- ✅ **完整执行流程** - 测试execute方法的完整工作流
- ✅ **错误处理机制** - 测试各种失败场景的处理

### 运行ReportAgent测试
```bash
# 推荐：最简单的测试（直接调用execute方法）
python test/test_report_agent_minimal.py

# 完整测试（包含多种场景）
python test/test_report_agent_simple.py

# 真实工作流测试（参考intent_clarification风格）
python test/test_report_agent_node.py

# 真实外部API测试（需要access_token）
export REPORT_ACCESS_TOKEN='your_token'
python test/test_report_agent_real.py

# 简化测试脚本
python test/run_report_agent_test.py
```

### 测试特点
- **真实工作流测试** - 直接调用workflow.report_agent.execute()方法，不使用Mock
- **Mock外部API** - 使用Mock模拟报告生成和S3上传，避免网络依赖
- **完整流程测试** - 测试从DSL解析到报告生成的完整流程
- **多种场景覆盖** - 测试有DSL、无DSL、不同配置等场景
- **参考现有风格** - 遵循test_intent_clarification_node.py的测试风格

### 8. test_report_agent_node.py - ReportAgent节点测试
- **DSL解析功能**: 测试从复杂DSL结构中提取AI总结、关键指标、观点分析
- **文本总结生成**: 测试基于解析数据生成Markdown格式的结构化报告
- **品牌分析DSL创建**: 测试动态创建默认和自定义品牌分析DSL
- **完整execute流程**: 测试报告生成、S3上传、DSL解析的完整流程（Mock外部API）
- **默认DSL处理**: 测试无DSL数据时的默认处理逻辑

**测试场景**:
```python
# DSL解析测试
SAMPLE_DSL -> parse_dsl_summary() -> 提取AI总结、指标、观点、图表

# 文本总结生成
summary_data -> generate_text_summary() -> Markdown格式报告

# 完整execute流程
state + DSL -> execute() -> HTML报告 + S3上传 + 文本总结

# 默认处理
无DSL状态 -> execute() -> 使用默认京东品牌DSL
```

---

💡 **提示**: 这些测试用例设计为既能验证功能正确性，又能作为代码使用示例，帮助理解各个节点的工作原理。
