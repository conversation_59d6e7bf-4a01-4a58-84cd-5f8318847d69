#!/usr/bin/env python3
"""
简单直接的ReportAgent测试 - 参考intent_clarification的风格
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))

from src.core.workflow import SpecificWorkflow
from src.models.state import create_initial_state, WorkflowStatus

# StreamWriter处理
def get_test_stream_writer():
    """获取测试用的StreamWriter"""
    def test_writer(data):
        print(f"📡 StreamWriter: {data}")
    return test_writer

# 真实的DSL数据
REAL_DSL_DATA = {
    "section1": {
        "type": "section",
        "title": "基本信息",
        "description": "",
        "content": [
            {
                "type": "descriptions",
                "data": [
                    {"id": 1, "label": "统计时间", "value": "截止5月16日16:00"},
                    {"id": 2, "label": "内容量", "value": 14069},
                    {"id": 3, "label": "口碑指数", "value": "80.15%"},
                    {"id": 4, "label": "正面情感比例", "value": "80.40%"},
                    {"id": 5, "label": "负面情感比例", "value": "0.25%"}
                ]
            }
        ]
    },
    "section2": {
        "type": "section",
        "title": "AI总结",
        "description": "【整体情况】\\n权威背书强势，理想汽车通过央视新闻和明星（朱广权、岳云鹏）的参与获得了强有力的品牌背书，展现强技术口碑。\\n【核心亮点】\\n权威背书强势：央视新闻为理想汽车技术实力提供有力背书，明星认可进一步增强品牌公信力。\\n趣味互动受好评：直播中的幽默互动和车载语音助手的表现获得网友广泛好评。\\n家庭定位明确：理想汽车在家庭用车方面的优势得到用户认可。\\n【风险预警】\\n预算管理争议：李想严格审批预算的做法引发部分争议，需要平衡节约与企业形象的关系。",
        "content": []
    },
    "section3": {
        "type": "section",
        "title": "观点分类",
        "description": "",
        "content": [
            {
                "type": "pie",
                "option": {
                    "series": [
                        {
                            "data": [
                                {"value": 67.0, "name": "权威背书 67%"},
                                {"value": 21.0, "name": "趣味互动 21%"},
                                {"value": 5.0, "name": "预算管理争议 5%"},
                                {"value": 4.0, "name": "家庭需求 4%"}
                            ]
                        }
                    ]
                }
            },
            {
                "type": "table",
                "title": "观点详情",
                "data": [
                    {
                        "id": 1,
                        "viewpoint": "权威背书",
                        "explain": "央视新闻和明星（如岳云鹏、朱广权）的参与和认可能为理想汽车提供了权威背书，增强了品牌公信力，并展示其技术领先性。",
                        "positive": "朱广权点赞理想MEGA的\"公路高铁级\"静谧性\\n岳云鹏称车内安静得像魔法\\n央视新闻为理想汽车技术实力提供有力背书",
                        "negative": "理想汽车与央视合作被质疑噱头大于价值"
                    },
                    {
                        "id": 2,
                        "viewpoint": "趣味互动",
                        "explain": "直播中的幽默互动（如理想同学的接梗能力、岳云鹏和朱广权的表现）受到网友广泛好评，认为形式生动有趣且展现了科技魅力。",
                        "positive": "李想、朱广权、岳云鹏，这阵容太豪华！\\n车载语音助手今晚报位出道！\\n直播期间车载语音助手直接封神！",
                        "negative": ""
                    }
                ]
            }
        ]
    }
}


def create_test_state(user_input, session_id="test_session", report_dsl=None):
    """创建测试状态"""
    state = create_initial_state(
        session_id=session_id,
        user_input=user_input,
        user_id="test_user",
        report_dsl=report_dsl
    )
    
    # 设置为REPORT状态，这样ReportAgent就会被调用
    state["workflow_status"] = WorkflowStatus.REPORT
    
    return state


async def test_report_agent():
    """测试ReportAgent"""
    print("=== 测试ReportAgent ===")
    
    # 创建工作流
    workflow = SpecificWorkflow()
    writer = get_test_stream_writer()
    
    # 测试1：有DSL数据的情况
    print("\n1. 测试有DSL数据的情况")
    state1 = create_test_state(
        user_input="请生成理想汽车的品牌舆情分析报告",
        session_id="test_001",
        report_dsl=REAL_DSL_DATA
    )
    
    print(f"输入: {state1['user_input']}")
    print(f"DSL节数: {len(state1['report_dsl'])}")
    
    # 直接调用ReportAgent.execute
    result1 = await workflow.report_agent.execute(state1, writer)
    
    print(f"返回类型: {type(result1)}")
    print(f"路由目标: {result1.goto}")
    print(f"工作流状态: {result1.update.get('workflow_status')}")
    
    if result1.update.get('workflow_status') == WorkflowStatus.COMPLETED:
        print("✅ 测试1通过 - 报告生成成功")
        
        # 显示结果
        final_report = result1.update.get('final_report', '')
        html_report = result1.update.get('html_report', '')
        summary_data = result1.update.get('summary_data', {})
        upload_result = result1.update.get('upload_result', {})
        
        print(f"📝 文本报告长度: {len(final_report)} 字符")
        print(f"📄 HTML报告长度: {len(html_report)} 字符")
        print(f"🧠 AI总结长度: {len(summary_data.get('ai_summary', ''))} 字符")
        print(f"📊 关键指标数: {len(summary_data.get('key_metrics', []))}")
        print(f"💭 观点分析数: {len(summary_data.get('viewpoints', []))}")
        print(f"☁️ 上传状态: {upload_result.get('success', False)}")
        
        # 显示AI总结预览
        ai_summary = summary_data.get('ai_summary', '')
        if ai_summary:
            print(f"🤖 AI总结预览: {ai_summary[:100]}...")
            
    else:
        print("❌ 测试1失败")
        error_info = result1.update.get('error_info', {})
        print(f"错误: {error_info.get('message', 'Unknown error')}")
    
    # 测试2：没有DSL数据的情况
    print("\n2. 测试没有DSL数据的情况")
    state2 = create_test_state(
        user_input="请生成品牌舆情分析报告",
        session_id="test_002",
        report_dsl=None  # 没有DSL
    )
    
    print(f"输入: {state2['user_input']}")
    print(f"DSL数据: 无")
    
    # 直接调用ReportAgent.execute
    result2 = await workflow.report_agent.execute(state2, writer)
    
    print(f"返回类型: {type(result2)}")
    print(f"路由目标: {result2.goto}")
    print(f"工作流状态: {result2.update.get('workflow_status')}")
    
    if result2.update.get('workflow_status') == WorkflowStatus.COMPLETED:
        print("✅ 测试2通过 - 使用默认DSL生成报告")
        
        # 检查是否使用了默认DSL
        report_dsl = result2.update.get('report_dsl', {})
        print(f"📋 生成的DSL节数: {len(report_dsl)}")
        
        # 检查DSL格式
        if "01" in report_dsl:
            print(f"📊 DSL格式: 标准格式 (01, 02, 03)")
            if "京东" in str(report_dsl.get("01", {})):
                print(f"🏢 默认品牌: 京东")
        else:
            print(f"📊 DSL键名: {list(report_dsl.keys())}")
            
    else:
        print("❌ 测试2失败")
        error_info = result2.update.get('error_info', {})
        print(f"错误: {error_info.get('message', 'Unknown error')}")
    
    # 测试3：测试DSL解析功能（不调用外部API）
    print("\n3. 测试DSL解析功能")
    
    # 直接调用解析方法
    summary_data = workflow.report_agent.parse_dsl_summary(REAL_DSL_DATA)
    
    print(f"解析结果类型: {type(summary_data)}")
    print(f"AI总结长度: {len(summary_data['ai_summary'])} 字符")
    print(f"关键指标数: {len(summary_data['key_metrics'])}")
    print(f"观点分析数: {len(summary_data['viewpoints'])}")
    print(f"图表信息数: {len(summary_data['charts_info'])}")
    
    # 显示关键指标
    print("📈 关键指标:")
    for metric in summary_data['key_metrics']:
        print(f"  - {metric['label']}: {metric['value']}")
    
    # 显示观点
    print("💭 观点分析:")
    for viewpoint in summary_data['viewpoints']:
        print(f"  - {viewpoint['viewpoint']}")
    
    print("✅ 测试3通过 - DSL解析功能正常")
    
    print("\n=== 所有测试完成 ===")


if __name__ == "__main__":
    asyncio.run(test_report_agent())
