#!/usr/bin/env python3
"""
测试ExecutionAgent节点的真实测试用例
基于品牌舆情分析任务计划，测试React Agent和MCP工具集成
"""

import sys
import os
import logging
import asyncio
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))

from src.core.workflow import SpecificWorkflow
from src.models.state import SpecificState, WorkflowStatus, create_initial_state
from langchain_core.messages import HumanMessage, AIMessage

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# StreamWriter处理
def get_test_stream_writer():
    """获取测试用的StreamWriter"""
    def test_writer(data):
        print(f"📡 StreamWriter: {data}")
    return test_writer


def create_brand_sentiment_task_plan():
    """创建品牌舆情分析任务计划"""
    return {
        "title": "京东外卖品牌舆情分析执行计划",
        "description": "基于MCP工具的品牌舆情分析，包含异步数据收集和报告生成",
        "steps": [
            {
                "title": "品牌数据收集阶段",
                "description": "调用品牌MCP Tool获取近期关于理想汽车L9汽车的网络声量舆情数据",
                "tasks": [
                    {"title": "调用品牌MCP Tool获取近期关于理想汽车L9汽车的网络声量舆情数据", "status": "pending"},
                ],
                "estimated_duration": "5-10分钟",
                "is_async": True,
                "tools_required": ["brand_mcp_tool", "async_monitor"]
            }
        ],
        "total_estimated_duration": "11-20分钟",
        "architecture": "事件驱动的异步架构",
        "notes": [
            "第一阶段是异步执行，需要等待MCP Server完成数据收集",
            "第二阶段依赖第一阶段的DSL输出结果",
            "整个流程采用事件驱动的异步架构"
        ]
    }


def create_execution_test_state(
    user_input: str,
    task_plan: dict = None,
    current_step_index: int = 0,
    execution_started: bool = False,
    execution_results: list = None,
    messages: list = None,
    workflow_status: WorkflowStatus = WorkflowStatus.EXECUTING
) -> SpecificState:
    """创建执行测试状态"""
    state = create_initial_state(
        session_id="execution_test_session",
        user_input=user_input,
        user_id="test_user"
    )

    # 更新状态
    state["task_plan"] = task_plan or create_brand_sentiment_task_plan()
    state["current_step_index"] = current_step_index
    state["execution_started"] = execution_started
    state["execution_results"] = execution_results or []
    state["workflow_status"] = workflow_status

    if messages:
        state["messages"] = messages

    return state


def test_execution_agent_node():
    """测试ExecutionAgent节点的各种场景"""
    print("=== 测试ExecutionAgent节点（React Agent + MCP工具）===")

    # 创建工作流实例
    workflow = SpecificWorkflow()

    # 测试场景1：品牌舆情分析完整执行
    print("\n1. 测试品牌舆情分析完整执行")
    test_brand_sentiment_execution(workflow)
    #
    # # 测试场景2：异步MCP工具执行
    # print("\n2. 测试异步MCP工具执行")
    # test_async_mcp_execution(workflow)
    #
    # # 测试场景3：DSL配置生成
    # print("\n3. 测试DSL配置生成")
    # test_dsl_generation(workflow)
    #
    # # 测试场景4：报告生成服务
    # print("\n4. 测试报告生成服务")
    # test_report_generation(workflow)

    print("\n=== 测试完成 ===")


def test_brand_sentiment_execution(workflow):
    """测试品牌舆情分析完整执行"""
    try:
        # 创建品牌舆情分析任务状态
        task_plan = create_brand_sentiment_task_plan()
        state = create_execution_test_state(
            user_input="请分析京东外卖品牌在近30天的舆情表现，包括用户情感分析、热点事件识别和竞品对比",
            task_plan=task_plan
        )

        print(f"任务计划: {task_plan['title']}")
        print(f"步骤数量: {len(task_plan['steps'])}")
        print(f"预计耗时: {task_plan['total_estimated_duration']}")
        print(f"架构类型: {task_plan['architecture']}")

        # 显示步骤详情
        print("\n执行步骤:")
        for i, step in enumerate(task_plan['steps']):
            print(f"  {i+1}. {step['title']}")
            print(f"     描述: {step['description']}")
            print(f"     预计耗时: {step['estimated_duration']}")
            if step.get('is_async'):
                print(f"     ⚠️ 异步任务")
            if step.get('tools_required'):
                print(f"     🔧 所需工具: {', '.join(step['tools_required'])}")

        # 直接调用ExecutionAgent
        print(f"\n⚡ 开始执行品牌舆情分析任务...")

        # 由于ExecutionAgent.execute是async方法，需要在async环境中运行
        async def run_execution():
            writer = get_test_stream_writer()
            return await workflow.execution_agent.execute(state, writer)

        result = asyncio.run(run_execution())

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")

        # 验证结果
        assert result.goto in ["summary", "__end__"], f"意外的路由目标: {result.goto}"
        assert "execution_results" in result.update, "应该包含执行结果"

        execution_results = result.update["execution_results"]
        print(f"\n📈 执行统计:")
        print(f"  - 总步骤: {len(execution_results)}")
        print(f"  - 成功步骤: {len([r for r in execution_results if not r.get('error', False)])}")
        print(f"  - 失败步骤: {len([r for r in execution_results if r.get('error', False)])}")

        # 验证每个步骤的执行结果
        expected_steps = ["品牌数据收集阶段", "获取品牌事件图表DSL配置", "报告生成阶段", "整理和格式化最终输出结果"]
        for i, expected_title in enumerate(expected_steps):
            if i < len(execution_results):
                step_result = execution_results[i]
                print(f"  ✅ 步骤 {i+1}: {step_result['step_title']}")
                
                # 验证步骤结果包含关键信息
                result_content = step_result["result"]
                assert "执行内容" in result_content or "执行报告" in result_content, f"步骤 {i+1} 缺少执行内容"

        # 验证执行报告
        if "execution_report" in result.update:
            execution_report = result.update["execution_report"]
            print(f"\n📝 执行报告生成成功 ({len(execution_report)} 字符)")
            
            # 检查报告关键信息
            key_terms = ["品牌数据收集", "DSL配置", "报告生成", "异步任务"]
            found_terms = [term for term in key_terms if term in execution_report]
            print(f"📄 报告包含关键信息: {len(found_terms)}/{len(key_terms)} 项")

        print("✓ 品牌舆情分析完整执行测试通过")

    except Exception as e:
        print(f"✗ 品牌舆情分析执行测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_async_mcp_execution(workflow):
    """测试异步MCP工具执行"""
    try:
        # 创建只包含异步数据收集步骤的任务
        async_task_plan = {
            "title": "异步MCP工具测试",
            "steps": [
                {
                    "title": "品牌数据收集阶段",
                    "description": "调用品牌MCP Tool获取品牌事件数据，测试异步执行能力",
                    "is_async": True,
                    "tools_required": ["brand_mcp_tool", "async_monitor"]
                }
            ]
        }

        state = create_execution_test_state(
            user_input="测试异步MCP工具执行能力",
            task_plan=async_task_plan
        )

        print(f"任务: {async_task_plan['title']}")
        print(f"异步步骤: {async_task_plan['steps'][0]['title']}")

        # 执行异步MCP任务
        async def run_async_execution():
            writer = get_test_stream_writer()
            return await workflow.execution_agent.execute(state, writer)

        result = asyncio.run(run_async_execution())

        print(f"路由目标: {result.goto}")

        # 验证异步执行结果
        if "execution_results" in result.update:
            execution_results = result.update["execution_results"]
            if len(execution_results) > 0:
                async_result = execution_results[0]
                result_content = async_result["result"]
                
                # 检查是否包含异步相关信息
                async_keywords = ["异步", "MCP", "Tool", "回调"]
                found_keywords = [kw for kw in async_keywords if kw in result_content]
                print(f"异步执行关键词: {found_keywords}")
                
                assert len(found_keywords) > 0, "应该包含异步执行相关信息"

        print("✓ 异步MCP工具执行测试通过")

    except Exception as e:
        print(f"✗ 异步MCP工具执行测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_dsl_generation(workflow):
    """测试DSL配置生成"""
    try:
        # 创建DSL配置生成任务
        dsl_task_plan = {
            "title": "DSL配置生成测试",
            "steps": [
                {
                    "title": "获取品牌事件图表DSL配置",
                    "description": "生成品牌事件图表DSL配置，测试配置生成能力",
                    "tools_required": ["dsl_generator", "config_validator"]
                }
            ]
        }

        state = create_execution_test_state(
            user_input="生成京东外卖品牌事件图表DSL配置",
            task_plan=dsl_task_plan
        )

        print(f"任务: {dsl_task_plan['title']}")
        print(f"DSL步骤: {dsl_task_plan['steps'][0]['title']}")

        # 执行DSL生成任务
        async def run_dsl_generation():
            writer = get_test_stream_writer()
            return await workflow.execution_agent.execute(state, writer)

        result = asyncio.run(run_dsl_generation())

        print(f"路由目标: {result.goto}")

        # 验证DSL生成结果
        if "execution_results" in result.update:
            execution_results = result.update["execution_results"]
            if len(execution_results) > 0:
                dsl_result = execution_results[0]
                result_content = dsl_result["result"]
                
                # 检查是否包含DSL相关信息
                dsl_keywords = ["DSL", "配置", "图表", "生成"]
                found_keywords = [kw for kw in dsl_keywords if kw in result_content]
                print(f"DSL生成关键词: {found_keywords}")
                
                assert len(found_keywords) > 0, "应该包含DSL生成相关信息"

        print("✓ DSL配置生成测试通过")

    except Exception as e:
        print(f"✗ DSL配置生成测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_report_generation(workflow):
    """测试报告生成服务"""
    try:
        # 创建报告生成任务
        report_task_plan = {
            "title": "报告生成服务测试",
            "steps": [
                {
                    "title": "报告生成阶段",
                    "description": "根据品牌事件图表DSL调用报表服务Tool生成完整报告",
                    "tools_required": ["report_service_tool", "chart_generator", "template_engine"]
                }
            ]
        }

        state = create_execution_test_state(
            user_input="生成京东外卖品牌舆情分析报告",
            task_plan=report_task_plan
        )

        print(f"任务: {report_task_plan['title']}")
        print(f"报告步骤: {report_task_plan['steps'][0]['title']}")

        # 执行报告生成任务
        async def run_report_generation():
            writer = get_test_stream_writer()
            return await workflow.execution_agent.execute(state, writer)

        result = asyncio.run(run_report_generation())

        print(f"路由目标: {result.goto}")

        # 验证报告生成结果
        if "execution_results" in result.update:
            execution_results = result.update["execution_results"]
            if len(execution_results) > 0:
                report_result = execution_results[0]
                result_content = report_result["result"]
                
                # 检查是否包含报告生成相关信息
                report_keywords = ["报告", "生成", "图表", "模板"]
                found_keywords = [kw for kw in report_keywords if kw in result_content]
                print(f"报告生成关键词: {found_keywords}")
                
                assert len(found_keywords) > 0, "应该包含报告生成相关信息"

        print("✓ 报告生成服务测试通过")

    except Exception as e:
        print(f"✗ 报告生成服务测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_execution_agent_capabilities():
    """测试ExecutionAgent的能力"""
    print("\n=== 测试ExecutionAgent能力 ===")

    workflow = SpecificWorkflow()

    # 测试ExecutionAgent
    print("\n1. ExecutionAgent能力:")
    try:
        capabilities = workflow.execution_agent.get_capabilities()
        for i, capability in enumerate(capabilities, 1):
            print(f"   {i}. {capability}")
    except Exception as e:
        print(f"   获取能力失败: {e}")

    # 测试Agent角色信息
    print("\n2. ExecutionAgent角色信息:")
    try:
        execution_info = workflow.execution_agent.get_role_info()
        print(f"   角色名称: {execution_info['role_name']}")
        print(f"   系统提示: {execution_info['system_prompt'][:100]}...")
    except Exception as e:
        print(f"   获取角色信息失败: {e}")


if __name__ == "__main__":
    test_execution_agent_node()
    test_execution_agent_capabilities()
