#!/usr/bin/env python3
"""
测试意图澄清节点的真实测试用例 - 更新版本
支持新的审批流程和重构后的架构
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))

from src.core.workflow import SpecificWorkflow
from src.models.state import SpecificState, WorkflowStatus, create_initial_state
from langchain_core.messages import HumanMessage, AIMessage

# StreamWriter处理
def get_test_stream_writer():
    """获取测试用的StreamWriter"""
    def test_writer(data):
        print(f"📡 StreamWriter: {data}")
    return test_writer

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_test_state(
    user_input: str,
    clarification_round: int = 0,
    intent_clarified: bool = False,
    intent_approved: bool = False,
    human_approval_required: bool = False,
    messages: list = None,
    workflow_status: WorkflowStatus = WorkflowStatus.CLARIFYING_INTENT
) -> SpecificState:
    """创建测试状态"""
    state = create_initial_state(
        session_id="test_session",
        user_input=user_input,
        user_id="test_user"
    )

    # 更新状态
    state["clarification_round"] = clarification_round
    state["intent_clarified"] = intent_clarified
    state["intent_approved"] = intent_approved
    state["human_approval_required"] = human_approval_required
    state["workflow_status"] = workflow_status

    if messages:
        state["messages"] = messages

    return state

def test_intent_clarification_node():
    """测试意图澄清节点的各种场景"""
    print("=== 测试意图澄清节点（新审批流程）===")

    # 创建工作流实例
    workflow = SpecificWorkflow()

    # 测试场景1：首次澄清 - 需求不够清晰
    print("\n1. 测试首次澄清 - 需求不够清晰")
    test_unclear_intent(workflow)

    # 测试场景2：首次澄清 - 需求清晰（需要审批）
    print("\n2. 测试首次澄清 - 需求清晰（需要审批）")
    test_clear_intent_with_approval(workflow)

    # 测试场景3：用户提供补充信息
    print("\n3. 测试用户提供补充信息")
    test_user_supplement(workflow)

    # 测试场景4：用户审批确认
    print("\n4. 测试用户审批确认")
    test_user_approval(workflow)

    # 测试场景5：补充信息后直接进入审批
    print("\n5. 测试补充信息后直接进入审批")
    test_supplement_to_approval(workflow)

    print("\n=== 测试完成 ===")

def test_unclear_intent(workflow):
    """测试需求不够清晰的场景"""
    try:
        writer = get_test_stream_writer()

        # 创建模糊需求的状态
        state = create_test_state(
            user_input="帮我分析一下舆情",  # 缺少品牌名称和具体事件
            clarification_round=0
        )

        print(f"输入: {state['user_input']}")
        print(f"澄清轮次: {state['clarification_round']}")

        # 直接调用意图澄清Agent
        result = workflow.intent_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"更新数据: {result}")

        # 验证结果
        # 移除了 human_approval_required 字段的检查
        assert result["clarification_round"] == 1, "澄清轮次应该递增"
        assert "messages" in result, "应该包含澄清消息"
        assert result.get("intent_clarified", False) == False, "意图应该未澄清"

        # 检查澄清消息内容
        clarification_message = result["messages"][0].content
        print(f"澄清消息: {clarification_message[:100]}...")

        assert "品牌" in clarification_message or "事件" in clarification_message or "分析" in clarification_message, "澄清消息应该询问具体信息"

        print("✓ 模糊需求澄清测试通过")

    except Exception as e:
        print(f"✗ 模糊需求澄清测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_clear_intent_with_approval(workflow):
    """测试需求清晰但需要审批的场景"""
    try:
        writer = get_test_stream_writer()

        # 创建清晰需求的状态
        state = create_test_state(
            user_input="分析星环OS近一个月的网络传播情况",  # 品牌明确、需求明确
            clarification_round=0
        )

        print(f"输入: {state['user_input']}")
        print(f"澄清轮次: {state['clarification_round']}")

        # 直接调用意图澄清Agent
        result = workflow.intent_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"更新数据: {result}")

        # 验证结果 - 新流程中即使意图清晰也需要审批
        assert result["intent_clarified"] == True, "意图应该被标记为已澄清"
        # 移除了 human_approval_required 字段的检查
        assert result.get("intent_approved", False) == False, "意图应该未审批"
        assert "intent_summary" in result, "应该包含意图总结"
        assert "messages" in result, "应该包含审批确认消息"

        # 检查审批消息内容
        approval_message = result["messages"][0].content
        print(f"审批消息: {approval_message[:100]}...")

        assert "确认" in approval_message or "理解" in approval_message, "审批消息应该包含确认内容"

        print(f"意图总结: {result['intent_summary']}")
        print("✓ 清晰需求审批测试通过")

    except Exception as e:
        print(f"✗ 清晰需求审批测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_user_supplement(workflow):
    """测试用户提供补充信息的场景"""
    try:
        writer = get_test_stream_writer()

        # 创建有用户回复的状态
        messages = [
            HumanMessage(content="帮我分析一下舆情"),
            AIMessage(content="为了为您提供精准的分析服务，我需要了解几个关键信息：\n1. 请提供您要分析的具体品牌或产品名称\n2. 请说明您希望分析的具体事件或传播内容"),
            HumanMessage(content="我想分析苹果公司iPhone15的发布情况")  # 用户补充信息
        ]

        state = create_test_state(
            user_input="帮我分析一下舆情",
            clarification_round=1,  # 已经进行过一轮澄清
            messages=messages
        )

        print(f"原始输入: {state['user_input']}")
        print(f"澄清轮次: {state['clarification_round']}")
        print(f"最新用户回复: {messages[-1].content}")

        # 直接调用意图澄清Agent
        result = workflow.intent_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"更新数据: {result}")

        # 验证结果 - 补充信息后重新分析
        assert "user_input" in result, "应该更新用户输入"
        assert "补充信息" in result["user_input"], "用户输入应该包含补充信息"

        # 检查补充信息后的处理结果
        if result.get("intent_clarified", False):
            # 补充信息足够，进入审批阶段
            assert result["human_approval_required"] == True, "应该需要人工审批"
            assert "messages" in result, "应该包含审批消息"
            print("✓ 补充信息足够，进入审批阶段")
        else:
            # 补充信息不足，继续澄清
            assert result["human_approval_required"] == True, "应该需要进一步澄清"
            assert result["clarification_round"] == 2, "澄清轮次应该递增"
            print("✓ 补充信息不足，继续澄清")

        print(f"更新后的用户输入: {result['user_input'][:100]}...")
        print("✓ 用户补充信息测试通过")

    except Exception as e:
        print(f"✗ 用户补充信息测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_user_approval(workflow):
    """测试用户审批确认的场景"""
    try:
        writer = get_test_stream_writer()

        # 创建用户审批确认的状态
        messages = [
            HumanMessage(content="分析星环OS近一个月的网络传播情况"),
            AIMessage(content="## 意图澄清完成\n\n**理解总结**：您希望分析星环OS在近一个月的网络传播情况\n\n**请确认**：以上理解是否正确？"),
            HumanMessage(content="确认，理解正确")  # 用户审批确认
        ]

        state = create_test_state(
            user_input="分析星环OS近一个月的网络传播情况",
            clarification_round=1,  # 已经进行过一轮澄清
            intent_clarified=True,  # 意图已澄清
            intent_approved=False,  # 但未审批
            # 移除了 human_approval_required 字段
            messages=messages
        )

        print(f"原始输入: {state['user_input']}")
        print(f"澄清轮次: {state['clarification_round']}")
        print(f"意图已澄清: {state['intent_clarified']}")
        print(f"意图已审批: {state['intent_approved']}")
        print(f"最新用户回复: {messages[-1].content}")

        # 直接调用意图澄清Agent
        result = workflow.intent_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"更新数据: {result}")

        # 验证结果
        assert result["intent_clarified"] == True, "意图应该保持已澄清状态"
        assert result["intent_approved"] == True, "意图应该被标记为已审批"
        assert result["human_approval_required"] == False, "应该清除人工审批标志"
        assert "用户确认" in result.get("intent_summary", ""), "应该包含用户确认信息"

        print(f"意图总结: {result.get('intent_summary', 'N/A')}")
        print("✓ 用户审批确认测试通过")

    except Exception as e:
        print(f"✗ 用户审批确认测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_supplement_to_approval(workflow):
    """测试补充信息后直接进入审批的场景"""
    try:
        writer = get_test_stream_writer()

        # 创建补充信息后直接进入审批的状态
        messages = [
            HumanMessage(content="帮我分析一下舆情"),
            AIMessage(content="为了为您提供精准的分析服务，我需要了解几个关键信息：\n1. 请提供您要分析的具体品牌或产品名称"),
            HumanMessage(content="分析苹果公司iPhone 15发布会在2024年9月的网络传播效果")  # 详细补充信息
        ]

        state = create_test_state(
            user_input="帮我分析一下舆情",
            clarification_round=1,
            messages=messages
        )

        print(f"原始输入: {state['user_input']}")
        print(f"最新用户回复: {messages[-1].content}")

        # 直接调用意图澄清Agent
        result = workflow.intent_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"更新数据: {result}")

        # 验证结果 - 补充信息详细，应该直接进入审批
        assert "user_input" in result, "应该更新用户输入"
        assert "补充信息" in result["user_input"], "用户输入应该包含补充信息"

        if result.get("intent_clarified", False):
            # 补充信息足够详细，直接进入审批
            # 移除了 human_approval_required 字段的检查
            assert result.get("intent_approved", False) == False, "意图应该未审批"
            assert "messages" in result, "应该包含审批确认消息"

            approval_message = result["messages"][0].content
            assert "确认" in approval_message, "应该包含审批确认内容"

            print("✓ 补充信息详细，直接进入审批阶段")
        else:
            print("ℹ️  补充信息仍需进一步澄清")

        print(f"更新后的用户输入: {result['user_input'][:100]}...")
        print("✓ 补充信息到审批测试通过")

    except Exception as e:
        print(f"✗ 补充信息到审批测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_agent_capabilities():
    """测试Agent的能力"""
    print("\n=== 测试Agent能力 ===")

    workflow = SpecificWorkflow()

    # 测试IntentAnalysisAgent
    print("\n1. IntentAnalysisAgent能力:")
    try:
        capabilities = workflow.intent_agent.get_capabilities()
        for i, capability in enumerate(capabilities, 1):
            print(f"   {i}. {capability}")
    except Exception as e:
        print(f"   获取能力失败: {e}")

    # 测试PlanningAgent
    print("\n2. PlanningAgent能力:")
    try:
        capabilities = workflow.planning_agent.get_capabilities()
        for i, capability in enumerate(capabilities, 1):
            print(f"   {i}. {capability}")
    except Exception as e:
        print(f"   获取能力失败: {e}")

    # 测试Agent角色信息
    print("\n3. Agent角色信息:")
    try:
        intent_info = workflow.intent_agent.get_role_info()
        planning_info = workflow.planning_agent.get_role_info()

        print(f"   IntentAnalysisAgent: {intent_info['role_name']}")
        print(f"   PlanningAgent: {planning_info['role_name']}")
    except Exception as e:
        print(f"   获取角色信息失败: {e}")

def test_new_approval_workflow():
    """测试新的审批工作流程"""
    print("\n=== 测试新审批工作流程 ===")

    workflow = SpecificWorkflow()

    # 完整流程测试：模糊需求 -> 补充信息 -> 审批确认
    print("\n完整流程测试：")

    writer = get_test_stream_writer()

    # 步骤1：模糊需求
    print("1. 提交模糊需求...")
    state1 = create_test_state(user_input="帮我分析一下舆情")
    result1 = workflow.intent_agent.execute(state1, writer)
    print(f"   结果：需要澄清 = {not result1.get('intent_clarified', False)}")

    # 步骤2：补充信息
    print("2. 提供补充信息...")
    messages = [
        HumanMessage(content="帮我分析一下舆情"),
        AIMessage(content="请提供具体信息..."),
        HumanMessage(content="分析理想汽车L9的市场反响")
    ]
    state2 = create_test_state(
        user_input="帮我分析一下舆情",
        clarification_round=1,
        messages=messages
    )
    result2 = workflow.intent_agent.execute(state2, writer)
    print(f"   结果：意图澄清 = {result2.get('intent_clarified', False)}, 需要审批 = {result2.get('human_approval_required', False)}")

    # 步骤3：审批确认
    if result2.get('intent_clarified', False):
        print("3. 用户审批确认...")
        messages.extend([
            AIMessage(content="理解总结...请确认"),
            HumanMessage(content="确认，理解正确")
        ])
        state3 = create_test_state(
            user_input=result2.get('user_input', ''),
            clarification_round=2,
            intent_clarified=True,
            intent_approved=False,
            # 移除了 human_approval_required 字段
            messages=messages
        )
        result3 = workflow.intent_agent.execute(state3, writer)
        print(f"   结果：审批通过 = {result3.get('intent_approved', False)}")

        if result3.get('intent_approved', False):
            print("✓ 完整审批流程测试通过")
        else:
            print("✗ 审批流程未完成")
    else:
        print("✗ 未进入审批阶段")

if __name__ == "__main__":
    test_intent_clarification_node()
    test_agent_capabilities()
    test_new_approval_workflow()
