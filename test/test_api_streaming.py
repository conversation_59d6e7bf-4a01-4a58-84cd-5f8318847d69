#!/usr/bin/env python3
"""
测试API流式消息功能
"""

import sys
import os
import asyncio
from unittest.mock import Mock, patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))


class MockEventHandler:
    """模拟EventHandler"""
    
    def __init__(self):
        self.live_status_messages = []
        self.assistant_messages = []
    
    def send_live_status(self, text: str, state=None):
        """记录实时状态消息"""
        self.live_status_messages.append(text)
        print(f"🔄 Live Status: {text}")
    
    def send_assistant_message(self, state, content: str, attachments=None):
        """记录助手消息"""
        self.assistant_messages.append(content)
        print(f"🤖 Assistant: {content}")


class MockWorkflowGraph:
    """模拟WorkflowGraph"""
    
    def __init__(self):
        self.state_data = {}
    
    async def astream(self, input_data, config=None, stream_mode=None):
        """模拟astream方法，生成流式消息"""
        session_id = config.get("configurable", {}).get("thread_id", "test")
        
        # 模拟流式消息序列
        streaming_messages = [
            {"live_status_message": "正在分析用户意图..."},
            {"agent_message": "我正在分析您的需求，确保我完全理解您想要的舆情分析内容。"},
            {"live_status_message": "正在制定执行计划..."},
            {"agent_message": "很好！现在让我根据您确认的需求制定详细的执行计划。"},
            {"live_status_message": "正在执行第1步..."},
            {"agent_message": "接下来我将执行第1步：品牌数据收集阶段。"},
            {"live_status_message": "第1步执行完成"},
            {"agent_message": "我已完成第1步：品牌数据收集阶段。执行结果已生成，正在为下一步做准备。"},
            {"live_status_message": "正在生成总结报告..."},
            {"agent_message": "任务执行已完成！现在让我为您生成详细的总结报告。"}
        ]
        
        # 逐个yield流式消息
        for message in streaming_messages:
            yield message
            await asyncio.sleep(0.1)  # 模拟处理时间
    
    def get_state(self, config):
        """模拟get_state方法"""
        class MockState:
            def __init__(self, values):
                self.values = values
        
        return MockState({
            "session_id": config.get("configurable", {}).get("thread_id", "test"),
            "workflow_status": "completed",
            "messages": [],
            "user_input": "测试用户输入"
        })


class MockWorkflow:
    """模拟Workflow"""
    
    def __init__(self):
        self.graph = MockWorkflowGraph()
    
    def get_session_status(self, session_id):
        return {"workflow_status": "completed"}


async def test_api_streaming():
    """测试API流式消息功能"""
    print("🚀 测试API流式消息功能")
    print("=" * 50)
    
    try:
        from src.api.main import BrandSpecificAgentAPI
        from src.api.models import ChatRequest
        
        # 创建模拟的event_handler和workflow
        mock_event_handler = MockEventHandler()
        mock_workflow = MockWorkflow()
        
        # 创建API实例
        api = BrandSpecificAgentAPI()
        api.event_handler = mock_event_handler
        api.workflow = mock_workflow
        
        print("✅ API实例创建成功")
        
        # 创建测试请求
        test_request = ChatRequest(
            message="请分析京东外卖品牌舆情",
            user_id="test_user",
            task_id="test_task",
            sandbox_id="test_sandbox"
        )
        
        print(f"📝 测试请求: {test_request.message}")
        
        # 执行聊天请求
        print(f"\n⚡ 开始执行聊天请求...")
        response = await api.chat(test_request)
        
        print(f"✅ 聊天请求执行完成")
        print(f"🎯 响应状态: {response.status}")
        print(f"📝 响应内容: {response.response[:100]}...")
        
        # 验证流式消息
        print(f"\n📡 流式消息验证:")
        print(f"  - 实时状态消息: {len(mock_event_handler.live_status_messages)} 条")
        print(f"  - 助手消息: {len(mock_event_handler.assistant_messages)} 条")
        
        # 显示收到的消息
        print(f"\n🔄 实时状态消息:")
        for i, msg in enumerate(mock_event_handler.live_status_messages, 1):
            print(f"  {i}. {msg}")
        
        print(f"\n🤖 助手消息:")
        for i, msg in enumerate(mock_event_handler.assistant_messages, 1):
            print(f"  {i}. {msg[:60]}...")
        
        # 验证消息数量
        assert len(mock_event_handler.live_status_messages) > 0, "应该有实时状态消息"
        assert len(mock_event_handler.assistant_messages) > 0, "应该有助手消息"
        
        # 验证消息内容
        status_keywords = ["分析", "制定", "执行", "完成", "生成"]
        found_status_keywords = []
        for keyword in status_keywords:
            for msg in mock_event_handler.live_status_messages:
                if keyword in msg:
                    found_status_keywords.append(keyword)
                    break
        
        agent_keywords = ["分析", "制定", "执行", "完成"]
        found_agent_keywords = []
        for keyword in agent_keywords:
            for msg in mock_event_handler.assistant_messages:
                if keyword in msg:
                    found_agent_keywords.append(keyword)
                    break
        
        print(f"\n✅ 消息验证:")
        print(f"  - 状态关键词匹配: {len(found_status_keywords)}/{len(status_keywords)}")
        print(f"  - 助手关键词匹配: {len(found_agent_keywords)}/{len(agent_keywords)}")
        
        print(f"\n🎉 API流式消息测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_streaming_message_flow():
    """测试流式消息流程"""
    print(f"\n📋 测试流式消息流程...")
    
    try:
        # 模拟astream的流式消息
        mock_messages = [
            {"live_status_message": "正在思考中..."},
            {"agent_message": "我正在为您分析需求"},
            {"live_status_message": "分析完成"},
            {"agent_message": "分析结果已生成，请查看"}
        ]
        
        mock_event_handler = MockEventHandler()
        
        # 模拟处理流式消息
        for chunk in mock_messages:
            if "live_status_message" in chunk:
                mock_event_handler.send_live_status(chunk['live_status_message'])
            elif "agent_message" in chunk:
                mock_event_handler.send_assistant_message({}, chunk['agent_message'])
        
        # 验证消息处理
        assert len(mock_event_handler.live_status_messages) == 2, "应该有2条状态消息"
        assert len(mock_event_handler.assistant_messages) == 2, "应该有2条助手消息"
        
        print(f"✅ 流式消息流程测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 流式消息流程测试失败: {e}")
        return False


async def test_api_structure():
    """测试API结构"""
    print(f"\n🏗️ 测试API结构...")
    
    try:
        from src.api.main import BrandSpecificAgentAPI
        
        # 创建API实例
        api = BrandSpecificAgentAPI()
        
        # 验证API结构
        assert hasattr(api, 'workflow'), "API应该有workflow属性"
        assert hasattr(api, 'event_handler'), "API应该有event_handler属性"
        assert hasattr(api, 'chat'), "API应该有chat方法"
        
        # 验证chat方法是async
        import inspect
        assert inspect.iscoroutinefunction(api.chat), "chat方法应该是async"
        
        print(f"✅ API结构验证通过")
        return True
        
    except Exception as e:
        print(f"❌ API结构测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🧪 API流式消息测试套件")
    print("=" * 60)
    
    tests = [
        ("API结构测试", test_api_structure),
        ("流式消息流程测试", test_streaming_message_flow),
        ("API流式消息测试", test_api_streaming)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 运行: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n📊 测试结果:")
    print("-" * 50)
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总结: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有API流式消息测试通过！")
        print("\n💡 API流式消息特性:")
        print("✅ 移除了_run_workflow_stream方法")
        print("✅ 直接使用workflow.graph.astream处理流式消息")
        print("✅ 支持live_status_message实时状态更新")
        print("✅ 支持agent_message助手消息")
        print("✅ 使用event_handler发送事件到第三方平台")
        print("✅ 简化的流式处理逻辑")
    else:
        print("⚠️ 部分测试失败")


if __name__ == "__main__":
    asyncio.run(main())
