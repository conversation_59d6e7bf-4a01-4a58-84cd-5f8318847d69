#!/usr/bin/env python3
"""
测试所有Agent的流式消息功能
"""

import sys
import os
import asyncio
from unittest.mock import Mock

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))


class MockStreamWriter:
    """模拟StreamWriter"""
    
    def __init__(self):
        self.messages = []
    
    def __call__(self, data):
        """记录流式消息"""
        self.messages.append(data)
        print(f"📡 {data}")
    
    def get_messages_by_type(self, message_type):
        """按类型获取消息"""
        return [msg for msg in self.messages if message_type in msg]
    
    def clear(self):
        """清空消息"""
        self.messages = []


def create_test_states():
    """创建各种测试状态"""
    from src.models.state import WorkflowStatus
    
    # IntentAnalysisAgent测试状态
    intent_state = {
        "session_id": "intent_test_001",
        "user_input": "请分析京东外卖品牌舆情",
        "clarification_round": 0,
        "intent_clarified": False,
        "intent_approved": False,
        "workflow_status": WorkflowStatus.CLARIFYING_INTENT,
        "messages": []
    }
    
    # PlanningAgent测试状态
    planning_state = {
        "session_id": "planning_test_001",
        "user_input": "请分析京东外卖品牌在近30天的舆情表现",
        "intent_summary": "分析京东外卖品牌在近30天的舆情表现，包括用户情感分析、热点事件识别和竞品对比",
        "intent_clarified": True,
        "intent_approved": True,
        "planning_round": 0,
        "workflow_status": WorkflowStatus.PLANNING,
        "messages": []
    }
    
    # ExecutionAgent测试状态
    execution_state = {
        "session_id": "execution_test_001",
        "user_input": "请分析京东外卖品牌在近30天的舆情表现",
        "task_plan": {
            "title": "京东外卖品牌舆情分析执行计划",
            "steps": [
                {"title": "品牌数据收集", "description": "收集品牌相关数据"},
                {"title": "数据分析", "description": "分析收集到的数据"}
            ]
        },
        "workflow_status": WorkflowStatus.EXECUTING,
        "messages": []
    }
    
    # SummaryAgent测试状态
    summary_state = {
        "session_id": "summary_test_001",
        "user_input": "请分析京东外卖品牌在近30天的舆情表现",
        "intent_summary": "分析京东外卖品牌在近30天的舆情表现",
        "task_plan": {
            "title": "京东外卖品牌舆情分析执行计划",
            "steps": [
                {"title": "品牌数据收集", "description": "收集品牌相关数据"},
                {"title": "数据分析", "description": "分析收集到的数据"}
            ]
        },
        "execution_results": [
            {"step_index": 0, "step_title": "品牌数据收集", "result": "成功收集了品牌数据"},
            {"step_index": 1, "step_title": "数据分析", "result": "完成了数据分析"}
        ],
        "workflow_status": WorkflowStatus.SUMMARIZING,
        "messages": []
    }
    
    return intent_state, planning_state, execution_state, summary_state


async def test_intent_agent_streaming():
    """测试IntentAnalysisAgent流式消息"""
    print("\n🤖 测试IntentAnalysisAgent流式消息")
    print("-" * 50)
    
    try:
        from src.core.workflow import SpecificWorkflow
        
        workflow = SpecificWorkflow()
        mock_writer = MockStreamWriter()
        
        intent_state, _, _, _ = create_test_states()
        
        # 测试意图分析
        result = workflow.intent_agent.execute(intent_state, writer=mock_writer)
        
        print(f"✅ IntentAnalysisAgent执行成功")
        print(f"🎯 路由目标: {result.goto}")
        
        # 分析消息
        live_messages = mock_writer.get_messages_by_type("live_status_message")
        agent_messages = mock_writer.get_messages_by_type("agent_message")
        
        print(f"📊 消息统计: 实时状态 {len(live_messages)} 条, Agent消息 {len(agent_messages)} 条")
        
        # 验证关键消息
        expected_keywords = ["分析", "需求", "理解"]
        found_keywords = []
        for msg in mock_writer.messages:
            content = str(msg.values())
            for keyword in expected_keywords:
                if keyword in content:
                    found_keywords.append(keyword)
                    break
        
        print(f"🔍 关键词匹配: {len(found_keywords)}/{len(expected_keywords)}")
        return True
        
    except Exception as e:
        print(f"❌ IntentAnalysisAgent测试失败: {e}")
        return False


async def test_planning_agent_streaming():
    """测试PlanningAgent流式消息"""
    print("\n📋 测试PlanningAgent流式消息")
    print("-" * 50)
    
    try:
        from src.core.workflow import SpecificWorkflow
        
        workflow = SpecificWorkflow()
        mock_writer = MockStreamWriter()
        
        _, planning_state, _, _ = create_test_states()
        
        # 测试计划制定
        result = workflow.planning_agent.execute(planning_state, writer=mock_writer)
        
        print(f"✅ PlanningAgent执行成功")
        print(f"🎯 路由目标: {result.goto}")
        
        # 分析消息
        live_messages = mock_writer.get_messages_by_type("live_status_message")
        agent_messages = mock_writer.get_messages_by_type("agent_message")
        
        print(f"📊 消息统计: 实时状态 {len(live_messages)} 条, Agent消息 {len(agent_messages)} 条")
        
        # 验证关键消息
        expected_keywords = ["计划", "制定", "执行"]
        found_keywords = []
        for msg in mock_writer.messages:
            content = str(msg.values())
            for keyword in expected_keywords:
                if keyword in content:
                    found_keywords.append(keyword)
                    break
        
        print(f"🔍 关键词匹配: {len(found_keywords)}/{len(expected_keywords)}")
        return True
        
    except Exception as e:
        print(f"❌ PlanningAgent测试失败: {e}")
        return False


async def test_execution_agent_streaming():
    """测试ExecutionAgent流式消息"""
    print("\n⚡ 测试ExecutionAgent流式消息")
    print("-" * 50)
    
    try:
        from src.core.workflow import SpecificWorkflow
        
        workflow = SpecificWorkflow()
        mock_writer = MockStreamWriter()
        
        _, _, execution_state, _ = create_test_states()
        
        # 测试任务执行
        result = await workflow.execution_agent.execute(execution_state, writer=mock_writer)
        
        print(f"✅ ExecutionAgent执行成功")
        print(f"🎯 路由目标: {result.goto}")
        
        # 分析消息
        live_messages = mock_writer.get_messages_by_type("live_status_message")
        agent_messages = mock_writer.get_messages_by_type("agent_message")
        
        print(f"📊 消息统计: 实时状态 {len(live_messages)} 条, Agent消息 {len(agent_messages)} 条")
        
        # 验证关键消息
        expected_keywords = ["执行", "步骤", "完成"]
        found_keywords = []
        for msg in mock_writer.messages:
            content = str(msg.values())
            for keyword in expected_keywords:
                if keyword in content:
                    found_keywords.append(keyword)
                    break
        
        print(f"🔍 关键词匹配: {len(found_keywords)}/{len(expected_keywords)}")
        return True
        
    except Exception as e:
        print(f"❌ ExecutionAgent测试失败: {e}")
        return False


async def test_summary_agent_streaming():
    """测试SummaryAgent流式消息"""
    print("\n📝 测试SummaryAgent流式消息")
    print("-" * 50)
    
    try:
        from src.core.workflow import SpecificWorkflow
        
        workflow = SpecificWorkflow()
        mock_writer = MockStreamWriter()
        
        _, _, _, summary_state = create_test_states()
        
        # 测试总结生成
        result = workflow.summary_agent.execute(summary_state, writer=mock_writer)
        
        print(f"✅ SummaryAgent执行成功")
        print(f"🎯 路由目标: {result.goto}")
        
        # 分析消息
        live_messages = mock_writer.get_messages_by_type("live_status_message")
        agent_messages = mock_writer.get_messages_by_type("agent_message")
        
        print(f"📊 消息统计: 实时状态 {len(live_messages)} 条, Agent消息 {len(agent_messages)} 条")
        
        # 验证关键消息
        expected_keywords = ["总结", "报告", "完成"]
        found_keywords = []
        for msg in mock_writer.messages:
            content = str(msg.values())
            for keyword in expected_keywords:
                if keyword in content:
                    found_keywords.append(keyword)
                    break
        
        print(f"🔍 关键词匹配: {len(found_keywords)}/{len(expected_keywords)}")
        return True
        
    except Exception as e:
        print(f"❌ SummaryAgent测试失败: {e}")
        return False


async def test_message_types():
    """测试消息类型定义"""
    print("\n📋 测试消息类型定义")
    print("-" * 50)
    
    mock_writer = MockStreamWriter()
    
    # 测试两种消息类型
    test_messages = [
        {"live_status_message": "正在思考中..."},
        {"agent_message": "我正在为您分析需求"},
        {"live_status_message": "分析完成"},
        {"agent_message": "分析结果已生成，请查看"}
    ]
    
    for msg in test_messages:
        mock_writer(msg)
    
    live_messages = mock_writer.get_messages_by_type("live_status_message")
    agent_messages = mock_writer.get_messages_by_type("agent_message")
    
    print(f"✅ 消息类型测试:")
    print(f"  - 实时状态消息: {len(live_messages)} 条")
    print(f"  - Agent消息: {len(agent_messages)} 条")
    
    assert len(live_messages) == 2, "应该有2条实时状态消息"
    assert len(agent_messages) == 2, "应该有2条Agent消息"
    
    print(f"✅ 消息类型验证通过")
    return True


async def main():
    """主测试函数"""
    print("🧪 所有Agent流式消息测试套件")
    print("=" * 70)
    
    tests = [
        ("消息类型定义测试", test_message_types),
        ("IntentAnalysisAgent流式消息", test_intent_agent_streaming),
        ("PlanningAgent流式消息", test_planning_agent_streaming),
        ("ExecutionAgent流式消息", test_execution_agent_streaming),
        ("SummaryAgent流式消息", test_summary_agent_streaming)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 运行: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n📊 测试结果汇总:")
    print("=" * 70)
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总结: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有Agent流式消息测试通过！")
        print("\n💡 流式消息功能已完成:")
        print("✅ IntentAnalysisAgent: 意图分析过程实时反馈")
        print("✅ PlanningAgent: 计划制定过程实时反馈")
        print("✅ ExecutionAgent: 任务执行过程实时反馈")
        print("✅ SummaryAgent: 总结生成过程实时反馈")
        print("\n🔄 消息类型:")
        print("  📡 live_status_message: 实时状态更新")
        print("  🤖 agent_message: Agent与用户沟通")
    else:
        print("⚠️ 部分测试失败，请检查实现")


if __name__ == "__main__":
    asyncio.run(main())
