#!/usr/bin/env python3
"""
测试StreamWriter的使用
验证所有测试文件都正确使用了get_stream_writer()
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))

from langgraph.config import get_stream_writer


def test_stream_writer_import():
    """测试StreamWriter导入"""
    print("🧪 测试StreamWriter导入")
    
    try:
        writer = get_stream_writer()
        print(f"✅ StreamWriter获取成功: {type(writer)}")
        return True
    except Exception as e:
        print(f"❌ StreamWriter获取失败: {e}")
        return False


def test_stream_writer_usage():
    """测试StreamWriter使用"""
    print("\n🧪 测试StreamWriter使用")
    
    try:
        writer = get_stream_writer()
        
        # 测试不同类型的消息
        test_messages = [
            {"live_status_message": "正在测试..."},
            {"agent_message": "这是一条测试消息"},
            {"human_feedback_message": "需要用户确认"}
        ]
        
        for msg in test_messages:
            try:
                writer(msg)
                print(f"✅ 消息发送成功: {list(msg.keys())[0]}")
            except Exception as e:
                print(f"❌ 消息发送失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ StreamWriter使用测试失败: {e}")
        return False


def check_test_files_imports():
    """检查测试文件的导入"""
    print("\n🧪 检查测试文件导入")
    
    test_files = [
        "test/test_intent_clarification_node.py",
        "test/test_planning_node.py",
        "test/test_execution_node.py", 
        "test/test_summary_node.py"
    ]
    
    results = []
    
    for test_file in test_files:
        if not os.path.exists(test_file):
            print(f"❌ 文件不存在: {test_file}")
            results.append(False)
            continue
            
        try:
            with open(test_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 检查是否导入了get_stream_writer
            has_import = "from langgraph.config import get_stream_writer" in content
            
            # 检查是否移除了MockStreamWriter
            has_mock = "MockStreamWriter" in content
            
            # 检查是否使用了get_stream_writer()
            has_usage = "writer = get_stream_writer()" in content
            
            if has_import and not has_mock and has_usage:
                print(f"✅ {test_file}: 正确使用StreamWriter")
                results.append(True)
            else:
                print(f"❌ {test_file}: StreamWriter使用有问题")
                if not has_import:
                    print(f"   - 缺少导入: from langgraph.config import get_stream_writer")
                if has_mock:
                    print(f"   - 仍然包含MockStreamWriter")
                if not has_usage:
                    print(f"   - 缺少使用: writer = get_stream_writer()")
                results.append(False)
                
        except Exception as e:
            print(f"❌ 检查文件失败 {test_file}: {e}")
            results.append(False)
    
    return all(results)


def test_agent_execute_with_writer():
    """测试Agent的execute方法接受writer参数"""
    print("\n🧪 测试Agent execute方法")
    
    try:
        from src.core.workflow import SpecificWorkflow
        from src.models.state import create_initial_state
        
        workflow = SpecificWorkflow()
        writer = get_stream_writer()
        
        # 创建测试状态
        state = create_initial_state(
            session_id="test",
            user_input="测试输入",
            user_id="test_user"
        )
        
        # 测试IntentAnalysisAgent
        try:
            result = workflow.intent_agent.execute(state, writer)
            print("✅ IntentAnalysisAgent.execute(state, writer) 成功")
        except TypeError as e:
            if "writer" in str(e):
                print(f"❌ IntentAnalysisAgent writer参数问题: {e}")
                return False
            else:
                print(f"✅ IntentAnalysisAgent.execute(state, writer) 成功 (其他错误: {e})")
        
        # 测试PlanningAgent
        try:
            state["intent_clarified"] = True
            state["intent_approved"] = True
            result = workflow.planning_agent.execute(state, writer)
            print("✅ PlanningAgent.execute(state, writer) 成功")
        except TypeError as e:
            if "writer" in str(e):
                print(f"❌ PlanningAgent writer参数问题: {e}")
                return False
            else:
                print(f"✅ PlanningAgent.execute(state, writer) 成功 (其他错误: {e})")
        
        # 测试SummaryAgent
        try:
            state["task_plan"] = {"title": "测试计划", "steps": []}
            state["execution_results"] = []
            result = workflow.summary_agent.execute(state, writer)
            print("✅ SummaryAgent.execute(state, writer) 成功")
        except TypeError as e:
            if "writer" in str(e):
                print(f"❌ SummaryAgent writer参数问题: {e}")
                return False
            else:
                print(f"✅ SummaryAgent.execute(state, writer) 成功 (其他错误: {e})")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent execute测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 StreamWriter使用测试套件")
    print("=" * 50)
    
    tests = [
        ("StreamWriter导入测试", test_stream_writer_import),
        ("StreamWriter使用测试", test_stream_writer_usage),
        ("测试文件导入检查", check_test_files_imports),
        ("Agent execute方法测试", test_agent_execute_with_writer)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 运行: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n📊 测试结果:")
    print("-" * 50)
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总结: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有StreamWriter使用测试通过！")
        print("\n💡 StreamWriter使用正确:")
        print("✅ 移除了MockStreamWriter")
        print("✅ 导入了get_stream_writer")
        print("✅ 正确使用writer = get_stream_writer()")
        print("✅ Agent.execute方法接受writer参数")
        print("✅ 流式消息通过真实StreamWriter发送")
    else:
        print("⚠️ 部分测试失败，请检查StreamWriter使用")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
