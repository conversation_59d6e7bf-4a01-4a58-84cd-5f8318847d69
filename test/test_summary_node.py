#!/usr/bin/env python3
"""
测试总结节点的真实测试用例
支持不同分支的状态测试
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))

from src.core.workflow import SpecificWorkflow
from src.models.state import SpecificState, WorkflowStatus, create_initial_state
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.config import get_stream_writer

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


def create_test_state(
    user_input: str,
    intent_summary: str,
    task_plan: dict,
    execution_results: list,
    messages: list = None,
    workflow_status: WorkflowStatus = WorkflowStatus.SUMMARIZING
) -> SpecificState:
    """创建测试状态"""
    state = create_initial_state(
        session_id="test_session",
        user_input=user_input,
        user_id="test_user"
    )

    # 更新状态
    state["intent_summary"] = intent_summary
    state["task_plan"] = task_plan
    state["execution_results"] = execution_results
    state["workflow_status"] = workflow_status
    state["execution_started"] = True
    state["plan_approved"] = True
    state["intent_clarified"] = True
    state["intent_approved"] = True

    if messages:
        state["messages"] = messages

    return state


def test_summary_node():
    """测试总结节点的各种场景"""
    print("=== 测试总结节点 ===")

    # 创建工作流实例
    workflow = SpecificWorkflow()

    # 测试场景1：成功执行的总结
    print("\n1. 测试成功执行的总结")
    test_successful_execution_summary(workflow)

    # 测试场景2：部分失败的总结
    print("\n2. 测试部分失败的总结")
    test_partial_failure_summary(workflow)

    # 测试场景3：完全失败的总结
    print("\n3. 测试完全失败的总结")
    test_complete_failure_summary(workflow)

    # 测试场景4：复杂执行结果的总结
    print("\n4. 测试复杂执行结果的总结")
    test_complex_execution_summary(workflow)

    print("\n=== 测试完成 ===")


def test_successful_execution_summary(workflow):
    """测试成功执行的总结场景"""
    try:
        writer = get_test_stream_writer()
        
        # 创建成功执行的状态
        task_plan = {
            "title": "京东外卖品牌舆情分析执行计划",
            "steps": [
                {"title": "数据收集", "description": "收集品牌相关数据"},
                {"title": "数据分析", "description": "分析收集的数据"}
            ]
        }

        execution_results = [
            {
                "step_index": 0,
                "step_title": "数据收集",
                "result": "成功收集了京东外卖品牌在近30天的相关数据，包括用户评论1000条，新闻报道50篇，社交媒体提及500次。数据质量良好，覆盖面广泛。",
                "error": False,
                "execution_time": 120
            },
            {
                "step_index": 1,
                "step_title": "数据分析",
                "result": "完成了数据分析，发现用户情感整体偏正面（70%正面，20%中性，10%负面），主要关注点集中在配送速度和食品质量上。识别出3个热点话题。",
                "error": False,
                "execution_time": 180
            }
        ]

        state = create_test_state(
            user_input="分析京东外卖品牌在近30天的舆情表现",
            intent_summary="分析京东外卖品牌在近30天的舆情表现，包括用户情感分析、热点事件识别",
            task_plan=task_plan,
            execution_results=execution_results
        )

        print(f"用户需求: {state['user_input']}")
        print(f"执行结果数量: {len(execution_results)}")
        print(f"成功步骤: {len([r for r in execution_results if not r.get('error', False)])}")

        # 直接调用总结Agent
        result = workflow.summary_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")
        print(f"更新数据键: {list(result.update.keys())}")

        # 验证结果
        assert result.goto == "__end__", "总结完成后应该路由到__end__"
        assert result.update["workflow_status"] == WorkflowStatus.COMPLETED, "状态应该更新为已完成"
        assert "summary" in result.update, "应该包含总结内容"
        assert "quality_analysis" in result.update, "应该包含质量分析"
        assert "messages" in result.update, "应该包含总结消息"

        # 验证总结内容
        summary = result.update["summary"]
        assert len(summary) > 100, "总结内容应该足够详细"
        assert "京东外卖" in summary, "总结应该包含品牌名称"
        assert "数据收集" in summary or "数据分析" in summary, "总结应该包含执行步骤"

        # 验证质量分析
        quality_analysis = result.update["quality_analysis"]
        assert "overall_score" in quality_analysis, "应该包含总体评分"
        assert quality_analysis["overall_score"] > 80, "成功执行的总体评分应该较高"
        assert quality_analysis["successful_steps"] == 2, "成功步骤数应该正确"
        assert quality_analysis["failed_steps"] == 0, "失败步骤数应该为0"

        print(f"总结长度: {len(summary)} 字符")
        print(f"总体评分: {quality_analysis['overall_score']}")
        print(f"总结预览: {summary[:100]}...")

        # 注意：使用真实StreamWriter时，消息会直接发送，无法在测试中验证
        print("✓ 流式消息已通过StreamWriter发送")
        print("✓ 成功执行总结测试通过")

    except Exception as e:
        print(f"✗ 成功执行总结测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_partial_failure_summary(workflow):
    """测试部分失败的总结场景"""
    try:
        writer = get_test_stream_writer()
        
        # 创建部分失败的状态
        task_plan = {
            "title": "品牌舆情分析执行计划",
            "steps": [
                {"title": "数据收集", "description": "收集品牌数据"},
                {"title": "数据分析", "description": "分析数据"},
                {"title": "报告生成", "description": "生成报告"}
            ]
        }

        execution_results = [
            {
                "step_index": 0,
                "step_title": "数据收集",
                "result": "成功收集了部分数据，但由于API限制，只获取了60%的预期数据量。",
                "error": False,
                "execution_time": 90
            },
            {
                "step_index": 1,
                "step_title": "数据分析",
                "result": "数据分析过程中遇到错误：数据格式不一致导致部分分析失败。",
                "error": True,
                "execution_time": 45
            },
            {
                "step_index": 2,
                "step_title": "报告生成",
                "result": "基于可用数据生成了简化版报告，包含基础分析结果。",
                "error": False,
                "execution_time": 60
            }
        ]

        state = create_test_state(
            user_input="分析品牌舆情表现",
            intent_summary="分析品牌舆情表现",
            task_plan=task_plan,
            execution_results=execution_results
        )

        print(f"用户需求: {state['user_input']}")
        print(f"执行结果数量: {len(execution_results)}")
        print(f"成功步骤: {len([r for r in execution_results if not r.get('error', False)])}")
        print(f"失败步骤: {len([r for r in execution_results if r.get('error', False)])}")

        # 直接调用总结Agent
        result = workflow.summary_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")

        # 验证结果
        assert result.goto == "__end__", "总结完成后应该路由到__end__"
        assert result.update["workflow_status"] == WorkflowStatus.COMPLETED, "状态应该更新为已完成"
        assert "summary" in result.update, "应该包含总结内容"
        assert "quality_analysis" in result.update, "应该包含质量分析"

        # 验证质量分析反映部分失败
        quality_analysis = result.update["quality_analysis"]
        assert quality_analysis["successful_steps"] == 2, "成功步骤数应该正确"
        assert quality_analysis["failed_steps"] == 1, "失败步骤数应该正确"
        assert 40 <= quality_analysis["overall_score"] <= 80, "部分失败的评分应该在中等范围"

        print(f"总体评分: {quality_analysis['overall_score']}")
        print(f"成功率: {quality_analysis['success_rate']}")

        # 注意：使用真实StreamWriter时，消息会直接发送，无法在测试中验证
        print("✓ 流式消息已通过StreamWriter发送")

        print("✓ 部分失败总结测试通过")

    except Exception as e:
        print(f"✗ 部分失败总结测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_complete_failure_summary(workflow):
    """测试完全失败的总结场景"""
    try:
        writer = get_test_stream_writer()
        
        # 创建完全失败的状态
        task_plan = {
            "title": "失败的执行计划",
            "steps": [
                {"title": "步骤1", "description": "第一个步骤"},
                {"title": "步骤2", "description": "第二个步骤"}
            ]
        }

        execution_results = [
            {
                "step_index": 0,
                "step_title": "步骤1",
                "result": "执行失败：网络连接超时",
                "error": True,
                "execution_time": 30
            },
            {
                "step_index": 1,
                "step_title": "步骤2",
                "result": "执行失败：依赖步骤1的结果，无法继续",
                "error": True,
                "execution_time": 10
            }
        ]

        state = create_test_state(
            user_input="执行一个会失败的任务",
            intent_summary="执行一个会失败的任务",
            task_plan=task_plan,
            execution_results=execution_results
        )

        print(f"用户需求: {state['user_input']}")
        print(f"失败步骤: {len([r for r in execution_results if r.get('error', False)])}")

        # 直接调用总结Agent
        result = workflow.summary_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")

        # 验证结果
        assert result.goto == "__end__", "总结完成后应该路由到__end__"
        assert result.update["workflow_status"] == WorkflowStatus.COMPLETED, "状态应该更新为已完成"

        # 验证质量分析反映完全失败
        quality_analysis = result.update["quality_analysis"]
        assert quality_analysis["successful_steps"] == 0, "成功步骤数应该为0"
        assert quality_analysis["failed_steps"] == 2, "失败步骤数应该正确"
        assert quality_analysis["overall_score"] < 40, "完全失败的评分应该较低"

        print(f"总体评分: {quality_analysis['overall_score']}")

        # 注意：使用真实StreamWriter时，消息会直接发送，无法在测试中验证
        print("✓ 流式消息已通过StreamWriter发送")

        print("✓ 完全失败总结测试通过")

    except Exception as e:
        print(f"✗ 完全失败总结测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_complex_execution_summary(workflow):
    """测试复杂执行结果的总结场景"""
    try:
        writer = get_test_stream_writer()
        
        # 创建复杂执行结果的状态
        task_plan = {
            "title": "复杂品牌分析执行计划",
            "steps": [
                {"title": "多源数据收集", "description": "从多个来源收集数据"},
                {"title": "深度数据分析", "description": "进行深度分析"},
                {"title": "竞品对比分析", "description": "与竞品对比"},
                {"title": "趋势预测", "description": "预测未来趋势"},
                {"title": "综合报告生成", "description": "生成综合报告"}
            ]
        }

        execution_results = [
            {
                "step_index": 0,
                "step_title": "多源数据收集",
                "result": "成功从5个不同数据源收集了超过10000条记录，数据覆盖社交媒体、新闻媒体、用户评论等多个维度。数据质量评估：优秀。",
                "error": False,
                "execution_time": 300
            },
            {
                "step_index": 1,
                "step_title": "深度数据分析",
                "result": "完成了情感分析、主题建模、关键词提取等多项分析。发现了15个关键主题，情感分布为正面65%、中性25%、负面10%。",
                "error": False,
                "execution_time": 450
            },
            {
                "step_index": 2,
                "step_title": "竞品对比分析",
                "result": "与3个主要竞品进行了对比分析，在品牌认知度、用户满意度等维度进行了量化比较。",
                "error": False,
                "execution_time": 200
            },
            {
                "step_index": 3,
                "step_title": "趋势预测",
                "result": "基于历史数据和当前趋势，预测了未来3个月的发展趋势。模型准确率达到85%。",
                "error": False,
                "execution_time": 180
            },
            {
                "step_index": 4,
                "step_title": "综合报告生成",
                "result": "生成了包含图表、数据分析、结论建议的综合报告，总计50页，包含20个可视化图表。",
                "error": False,
                "execution_time": 120
            }
        ]

        state = create_test_state(
            user_input="进行全面的品牌舆情分析",
            intent_summary="进行全面的品牌舆情分析，包括多维度数据收集、深度分析、竞品对比和趋势预测",
            task_plan=task_plan,
            execution_results=execution_results
        )

        print(f"用户需求: {state['user_input']}")
        print(f"执行步骤数: {len(execution_results)}")
        print(f"全部成功: {all(not r.get('error', False) for r in execution_results)}")

        # 直接调用总结Agent
        result = workflow.summary_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")

        # 验证结果
        assert result.goto == "__end__", "总结完成后应该路由到__end__"
        assert "summary" in result.update, "应该包含总结内容"
        assert "quality_analysis" in result.update, "应该包含质量分析"

        # 验证高质量执行的分析结果
        quality_analysis = result.update["quality_analysis"]
        assert quality_analysis["successful_steps"] == 5, "所有步骤都应该成功"
        assert quality_analysis["failed_steps"] == 0, "失败步骤数应该为0"
        assert quality_analysis["overall_score"] >= 90, "高质量执行的评分应该很高"

        # 验证总结内容的丰富性
        summary = result.update["summary"]
        assert len(summary) > 500, "复杂执行的总结应该很详细"

        print(f"总结长度: {len(summary)} 字符")
        print(f"总体评分: {quality_analysis['overall_score']}")

        # 注意：使用真实StreamWriter时，消息会直接发送，无法在测试中验证
        print("✓ 流式消息已通过StreamWriter发送")

        print("✓ 复杂执行结果总结测试通过")

    except Exception as e:
        print(f"✗ 复杂执行结果总结测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_summary_agent_capabilities():
    """测试SummaryAgent的能力"""
    print("\n=== 测试SummaryAgent能力 ===")

    workflow = SpecificWorkflow()

    try:
        capabilities = workflow.summary_agent.get_capabilities()
        print("SummaryAgent能力:")
        for i, capability in enumerate(capabilities, 1):
            print(f"   {i}. {capability}")

        role_info = workflow.summary_agent.get_role_info()
        print(f"\n角色信息: {role_info['role_name']}")
        print(f"描述: {role_info.get('description', 'N/A')}")

    except Exception as e:
        print(f"获取能力失败: {e}")


if __name__ == "__main__":
    test_summary_node()
    test_summary_agent_capabilities()
