#!/usr/bin/env python3
"""
测试ExecutionPlan结构体功能
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))

# 直接导入execution_plan模块，避免依赖问题
import importlib.util
execution_plan_path = os.path.join(os.path.abspath('.'), 'src', 'models', 'execution_plan.py')
spec = importlib.util.spec_from_file_location("execution_plan", execution_plan_path)
execution_plan_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(execution_plan_module)

ExecutionPlan = execution_plan_module.ExecutionPlan
ExecutionStep = execution_plan_module.ExecutionStep
ExecutionTask = execution_plan_module.Task
TaskStatus = execution_plan_module.TaskStatus
TaskPriority = execution_plan_module.TaskPriority
ExecutionResult = execution_plan_module.ExecutionResult
ExecutionContext = execution_plan_module.ExecutionContext

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


def create_sample_task_plan():
    """创建示例任务计划字典"""
    return {
        "title": "京东外卖品牌舆情分析执行计划",
        "description": "基于MCP工具的品牌舆情分析，包含异步数据收集和报告生成",
        "steps": [
            {
                "title": "品牌数据收集阶段",
                "description": "调用品牌MCP Tool获取品牌事件数据",
                "tasks": [
                    {"title": "连接品牌MCP Tool", "status": "pending"},
                    {"title": "配置数据收集参数", "status": "pending"},
                    {"title": "启动异步数据收集", "status": "pending"},
                    {"title": "监控收集进度", "status": "pending"}
                ],
                "estimated_duration": "5-10分钟",
                "is_async": True,
                "tools_required": ["brand_mcp_tool", "async_monitor"]
            },
            {
                "title": "获取品牌事件图表DSL配置",
                "description": "获取品牌事件图表DSL配置，为报告生成做准备",
                "tasks": [
                    {"title": "分析品牌事件数据", "status": "pending"},
                    {"title": "选择图表类型", "status": "pending"},
                    {"title": "生成DSL配置", "status": "pending"},
                    {"title": "验证配置完整性", "status": "pending"}
                ],
                "estimated_duration": "2-3分钟",
                "depends_on": ["品牌数据收集阶段"],
                "tools_required": ["dsl_generator", "config_validator"]
            },
            {
                "title": "报告生成阶段",
                "description": "根据品牌事件图表DSL调用报表服务Tool生成完整的品牌舆情分析报告",
                "tasks": [
                    {"title": "调用报表服务", "status": "pending"},
                    {"title": "生成可视化图表", "status": "pending"},
                    {"title": "应用报告模板", "status": "pending"},
                    {"title": "生成多格式报告", "status": "pending"}
                ],
                "estimated_duration": "3-5分钟",
                "depends_on": ["获取品牌事件图表DSL配置"],
                "tools_required": ["report_service_tool", "chart_generator", "template_engine"]
            }
        ],
        "total_estimated_duration": "10-18分钟",
        "architecture": "事件驱动的异步架构",
        "notes": [
            "第一阶段是异步执行，需要等待MCP Server完成数据收集",
            "第二阶段依赖第一阶段的DSL输出结果",
            "整个流程采用事件驱动的异步架构"
        ]
    }


def test_execution_plan_creation():
    """测试ExecutionPlan创建"""
    print("🧪 测试ExecutionPlan创建")
    
    try:
        # 从字典创建ExecutionPlan
        task_plan_dict = create_sample_task_plan()
        execution_plan = ExecutionPlan.from_dict(task_plan_dict)
        
        # 验证基本属性
        assert execution_plan.title == "京东外卖品牌舆情分析执行计划"
        assert len(execution_plan.steps) == 3
        assert execution_plan.total_estimated_duration == "10-18分钟"
        assert execution_plan.architecture == "事件驱动的异步架构"
        assert len(execution_plan.notes) == 3
        
        print(f"✅ ExecutionPlan创建成功: {execution_plan.title}")
        print(f"   - 步骤数: {len(execution_plan.steps)}")
        print(f"   - 总任务数: {execution_plan.get_total_tasks()}")
        print(f"   - 架构: {execution_plan.architecture}")
        
        return True
        
    except Exception as e:
        print(f"❌ ExecutionPlan创建失败: {e}")
        return False


def test_execution_step_and_task_structure():
    """测试ExecutionStep和Task结构"""
    print("\n🧪 测试ExecutionStep和Task结构")
    
    try:
        task_plan_dict = create_sample_task_plan()
        execution_plan = ExecutionPlan.from_dict(task_plan_dict)
        
        # 验证第一个步骤
        first_step = execution_plan.steps[0]
        assert first_step.title == "品牌数据收集阶段"
        assert first_step.is_async == True
        assert len(first_step.tasks) == 4
        assert "brand_mcp_tool" in first_step.tools_required
        
        # 验证任务
        first_task = first_step.tasks[0]
        assert first_task.title == "连接品牌MCP Tool"
        assert first_task.status == TaskStatus.PENDING
        
        print(f"✅ 步骤结构验证成功")
        print(f"   - 第一步骤: {first_step.title}")
        print(f"   - 任务数: {len(first_step.tasks)}")
        print(f"   - 异步执行: {first_step.is_async}")
        print(f"   - 所需工具: {first_step.tools_required}")
        
        # 验证任务状态管理
        first_task.mark_in_progress()
        assert first_task.status == TaskStatus.IN_PROGRESS
        
        first_task.mark_completed()
        assert first_task.status == TaskStatus.COMPLETED
        
        print(f"✅ 任务状态管理验证成功")
        
        return True
        
    except Exception as e:
        print(f"❌ ExecutionStep和Task结构测试失败: {e}")
        return False


def test_execution_plan_statistics():
    """测试ExecutionPlan统计功能"""
    print("\n🧪 测试ExecutionPlan统计功能")
    
    try:
        task_plan_dict = create_sample_task_plan()
        execution_plan = ExecutionPlan.from_dict(task_plan_dict)
        
        # 初始统计
        total_tasks = execution_plan.get_total_tasks()
        completed_tasks = execution_plan.get_completed_tasks()
        failed_tasks = execution_plan.get_failed_tasks()
        progress = execution_plan.get_progress_percentage()
        
        print(f"📊 初始统计:")
        print(f"   - 总任务数: {total_tasks}")
        print(f"   - 已完成: {completed_tasks}")
        print(f"   - 失败任务: {failed_tasks}")
        print(f"   - 进度: {progress:.1f}%")
        
        assert total_tasks == 12  # 3个步骤，每个4个任务
        assert completed_tasks == 0
        assert failed_tasks == 0
        assert progress == 0.0
        
        # 模拟完成一些任务
        execution_plan.steps[0].tasks[0].mark_completed()
        execution_plan.steps[0].tasks[1].mark_completed()
        execution_plan.steps[1].tasks[0].mark_failed()
        
        # 重新计算统计
        completed_tasks = execution_plan.get_completed_tasks()
        failed_tasks = execution_plan.get_failed_tasks()
        progress = execution_plan.get_progress_percentage()
        
        print(f"📊 更新后统计:")
        print(f"   - 已完成: {completed_tasks}")
        print(f"   - 失败任务: {failed_tasks}")
        print(f"   - 进度: {progress:.1f}%")
        
        assert completed_tasks == 2
        assert failed_tasks == 1
        assert progress == (2 / 12) * 100
        
        print("✅ ExecutionPlan统计功能验证成功")
        
        return True
        
    except Exception as e:
        print(f"❌ ExecutionPlan统计功能测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 ExecutionPlan结构体测试套件")
    print("=" * 60)
    
    tests = [
        ("ExecutionPlan创建", test_execution_plan_creation),
        ("ExecutionStep和Task结构", test_execution_step_and_task_structure),
        ("ExecutionPlan统计功能", test_execution_plan_statistics)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 运行: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n📊 测试结果:")
    print("-" * 50)
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总结: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有ExecutionPlan结构体测试通过！")
        print("\n💡 ExecutionPlan特性:")
        print("✅ 结构化任务计划管理")
        print("✅ 层次化步骤和任务组织")
        print("✅ 任务状态跟踪和管理")
        print("✅ 执行进度统计")
        print("✅ 计划数据转换")
        print("✅ 依赖关系处理")
        print("✅ 工具需求管理")
    else:
        print("⚠️ 部分测试失败")
    
    return passed == len(results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
