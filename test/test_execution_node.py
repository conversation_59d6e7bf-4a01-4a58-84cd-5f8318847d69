#!/usr/bin/env python3
"""
测试执行节点的真实测试用例
支持不同分支的状态测试
"""

import sys
import os
import asyncio
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))

from src.core.workflow import SpecificWorkflow
from src.models.state import SpecificState, WorkflowStatus, create_initial_state
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.config import get_stream_writer

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


def create_test_state(
    user_input: str,
    task_plan: dict,
    execution_started: bool = True,
    execution_results: list = None,
    messages: list = None,
    workflow_status: WorkflowStatus = WorkflowStatus.EXECUTING
) -> SpecificState:
    """创建测试状态"""
    state = create_initial_state(
        session_id="test_session",
        user_input=user_input,
        user_id="test_user"
    )

    # 更新状态
    state["task_plan"] = task_plan
    state["execution_started"] = execution_started
    state["workflow_status"] = workflow_status
    state["plan_approved"] = True
    state["intent_clarified"] = True
    state["intent_approved"] = True

    if execution_results:
        state["execution_results"] = execution_results

    if messages:
        state["messages"] = messages

    return state


async def test_execution_node():
    """测试执行节点的各种场景"""
    print("=== 测试执行节点 ===")

    # 创建工作流实例
    workflow = SpecificWorkflow()

    # 测试场景1：正常执行单步骤计划
    print("\n1. 测试正常执行单步骤计划")
    await test_single_step_execution(workflow)

    # 测试场景2：执行多步骤计划
    print("\n2. 测试执行多步骤计划")
    await test_multi_step_execution(workflow)

    # 测试场景3：执行过程中遇到错误
    print("\n3. 测试执行过程中遇到错误")
    await test_execution_with_errors(workflow)

    print("\n=== 测试完成 ===")


async def test_single_step_execution(workflow):
    """测试单步骤执行的场景"""
    try:
        writer = get_test_stream_writer()
        
        # 创建单步骤计划的状态
        simple_plan = {
            "title": "简单品牌数据收集",
            "steps": [
                {
                    "title": "品牌数据收集",
                    "description": "收集京东外卖的基础品牌数据",
                    "tasks": [
                        {"title": "获取品牌基本信息"},
                        {"title": "收集近期新闻数据"}
                    ]
                }
            ]
        }

        state = create_test_state(
            user_input="收集京东外卖的品牌数据",
            task_plan=simple_plan
        )

        print(f"用户需求: {state['user_input']}")
        print(f"计划标题: {simple_plan['title']}")
        print(f"步骤数量: {len(simple_plan['steps'])}")

        # 直接调用执行Agent
        result = await workflow.execution_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")
        print(f"更新数据键: {list(result.update.keys())}")

        # 验证结果
        assert result.goto == "summary", "执行完成后应该路由到summary"
        assert "execution_results" in result.update, "应该包含执行结果"
        assert result.update["workflow_status"] == WorkflowStatus.SUMMARIZING, "状态应该更新为总结中"

        # 验证执行结果
        execution_results = result.update["execution_results"]
        assert len(execution_results) == 1, "应该有1个步骤的执行结果"
        assert "step_index" in execution_results[0], "执行结果应该包含步骤索引"
        assert "result" in execution_results[0], "执行结果应该包含结果内容"

        print(f"执行结果数量: {len(execution_results)}")
        print(f"第1步结果: {execution_results[0]['result'][:100]}...")

        # 注意：使用真实StreamWriter时，消息会直接发送，无法在测试中验证
        print("✓ 流式消息已通过StreamWriter发送")
        print("✓ 单步骤执行测试通过")

    except Exception as e:
        print(f"✗ 单步骤执行测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_multi_step_execution(workflow):
    """测试多步骤执行的场景"""
    try:
        writer = get_test_stream_writer()
        
        # 创建多步骤计划的状态
        complex_plan = {
            "title": "京东外卖品牌舆情分析执行计划",
            "steps": [
                {
                    "title": "数据收集阶段",
                    "description": "收集相关的品牌数据",
                    "tasks": [
                        {"title": "收集品牌基础信息"},
                        {"title": "收集用户评论数据"}
                    ]
                },
                {
                    "title": "数据分析阶段", 
                    "description": "分析收集到的数据",
                    "tasks": [
                        {"title": "情感分析"},
                        {"title": "热点话题识别"}
                    ]
                },
                {
                    "title": "报告生成阶段",
                    "description": "生成分析报告",
                    "tasks": [
                        {"title": "撰写分析报告"},
                        {"title": "制作数据图表"}
                    ]
                }
            ]
        }

        state = create_test_state(
            user_input="分析京东外卖品牌在近30天的舆情表现",
            task_plan=complex_plan
        )

        print(f"用户需求: {state['user_input']}")
        print(f"计划标题: {complex_plan['title']}")
        print(f"步骤数量: {len(complex_plan['steps'])}")

        # 直接调用执行Agent
        result = await workflow.execution_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")

        # 验证结果
        assert result.goto == "summary", "执行完成后应该路由到summary"
        assert "execution_results" in result.update, "应该包含执行结果"

        # 验证执行结果
        execution_results = result.update["execution_results"]
        assert len(execution_results) == 3, "应该有3个步骤的执行结果"

        for i, result_item in enumerate(execution_results):
            assert "step_index" in result_item, f"第{i+1}步应该包含步骤索引"
            assert "step_title" in result_item, f"第{i+1}步应该包含步骤标题"
            assert "result" in result_item, f"第{i+1}步应该包含结果内容"
            print(f"第{i+1}步: {result_item['step_title']} - {result_item['result'][:50]}...")

        # 注意：使用真实StreamWriter时，消息会直接发送，无法在测试中验证
        print("✓ 流式消息已通过StreamWriter发送")
        print("✓ 多步骤执行测试通过")

    except Exception as e:
        print(f"✗ 多步骤执行测试失败: {e}")
        import traceback
        traceback.print_exc()


async def test_execution_with_errors(workflow):
    """测试执行过程中遇到错误的场景"""
    try:
        writer = get_test_stream_writer()
        
        # 创建可能出错的计划状态
        error_prone_plan = {
            "title": "容易出错的执行计划",
            "steps": [
                {
                    "title": "正常步骤",
                    "description": "这个步骤应该正常执行",
                    "tasks": [{"title": "正常任务"}]
                },
                {
                    "title": "可能出错的步骤",
                    "description": "这个步骤可能会遇到问题",
                    "tasks": [{"title": "复杂任务"}]
                }
            ]
        }

        state = create_test_state(
            user_input="执行一个可能出错的任务",
            task_plan=error_prone_plan
        )

        print(f"用户需求: {state['user_input']}")
        print(f"计划标题: {error_prone_plan['title']}")

        # 直接调用执行Agent
        result = await workflow.execution_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")

        # 验证结果 - 即使有错误也应该完成执行
        assert result.goto == "summary", "即使有错误也应该路由到summary"
        assert "execution_results" in result.update, "应该包含执行结果"

        # 验证执行结果包含错误信息
        execution_results = result.update["execution_results"]
        assert len(execution_results) == 2, "应该有2个步骤的执行结果"

        # 检查是否有错误记录
        has_error = any(result_item.get("error", False) for result_item in execution_results)
        print(f"执行过程中是否有错误: {has_error}")

        for i, result_item in enumerate(execution_results):
            error_status = "❌ 失败" if result_item.get("error", False) else "✅ 成功"
            print(f"第{i+1}步: {result_item['step_title']} - {error_status}")

        print("✓ 执行错误处理测试通过")

    except Exception as e:
        print(f"✗ 执行错误处理测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_execution_agent_capabilities():
    """测试ExecutionAgent的能力"""
    print("\n=== 测试ExecutionAgent能力 ===")

    workflow = SpecificWorkflow()

    try:
        capabilities = workflow.execution_agent.get_capabilities()
        print("ExecutionAgent能力:")
        for i, capability in enumerate(capabilities, 1):
            print(f"   {i}. {capability}")

        role_info = workflow.execution_agent.get_role_info()
        print(f"\n角色信息: {role_info['role_name']}")
        print(f"描述: {role_info.get('description', 'N/A')}")

    except Exception as e:
        print(f"获取能力失败: {e}")


if __name__ == "__main__":
    asyncio.run(test_execution_node())
    test_execution_agent_capabilities()
