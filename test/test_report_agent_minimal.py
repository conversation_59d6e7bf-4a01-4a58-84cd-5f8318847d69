#!/usr/bin/env python3
"""
最简单的ReportAgent测试 - 就像您说的那样简单直接
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))

from src.core.workflow import SpecificWorkflow
from src.models.state import create_initial_state, WorkflowStatus

def get_test_stream_writer():
    """获取测试用的StreamWriter"""
    def test_writer(data):
        print(f"📡 {data}")
    return test_writer

def create_test_state(user_input, session_id="test_session"):
    """创建测试状态"""
    state = create_initial_state(
        session_id=session_id,
        user_input=user_input,
        user_id="test_user"
    )
    state["workflow_status"] = WorkflowStatus.REPORT
    return state

async def main():
    """主测试函数"""
    print("=== 简单测试ReportAgent ===")
    
    # 创建工作流和writer
    workflow = SpecificWorkflow()
    writer = get_test_stream_writer()
    
    # 创建测试状态
    state1 = create_test_state(user_input="帮我生成品牌舆情分析报告")
    
    print(f"输入: {state1['user_input']}")
    print(f"会话ID: {state1['session_id']}")
    print(f"状态: {state1['workflow_status']}")
    
    # 直接调用ReportAgent.execute - 就这么简单！
    print("\n🚀 开始执行ReportAgent...")
    result1 = await workflow.report_agent.execute(state1, writer)
    
    print(f"\n📊 执行结果:")
    print(f"路由目标: {result1.goto}")
    print(f"工作流状态: {result1.update.get('workflow_status')}")
    
    if result1.update.get('workflow_status') == WorkflowStatus.COMPLETED:
        print("✅ 成功!")
        
        # 显示结果
        final_report = result1.update.get('final_report', '')
        html_report = result1.update.get('html_report', '')
        
        print(f"📝 文本报告: {len(final_report)} 字符")
        print(f"📄 HTML报告: {len(html_report)} 字符")
        
        # 显示报告预览
        if final_report:
            print(f"\n📖 报告预览:")
            print(final_report[:300] + "..." if len(final_report) > 300 else final_report)
            
    elif result1.update.get('workflow_status') == WorkflowStatus.FAILED:
        print("❌ 失败!")
        error_info = result1.update.get('error_info', {})
        print(f"错误: {error_info.get('message', 'Unknown error')}")
    
    print("\n=== 测试完成 ===")

if __name__ == "__main__":
    asyncio.run(main())
