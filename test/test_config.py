#!/usr/bin/env python3
"""
测试配置文件 - 管理测试用的敏感信息
"""

import os
from typing import Optional

class TestConfig:
    """测试配置类"""
    
    # 从环境变量获取access_token
    ACCESS_TOKEN: Optional[str] = os.getenv('REPORT_ACCESS_TOKEN')
    
    # 报告服务配置
    REPORT_BASE_URL: str = "https://console-playground.fed.chehejia.com"
    UPLOAD_BASE_URL: str = "https://xuanji-backend-service-dev.inner.chj.cloud/api/v1/ois/upload"
    
    # 测试配置
    ENABLE_EXTERNAL_API_TEST: bool = bool(ACCESS_TOKEN)
    TEST_TIMEOUT: int = 60  # 秒
    
    @classmethod
    def get_access_token(cls) -> Optional[str]:
        """获取访问令牌"""
        return cls.ACCESS_TOKEN
    
    @classmethod
    def has_access_token(cls) -> bool:
        """检查是否有访问令牌"""
        return bool(cls.ACCESS_TOKEN)
    
    @classmethod
    def print_config(cls):
        """打印配置信息"""
        print("🔧 测试配置:")
        print(f"  报告服务URL: {cls.REPORT_BASE_URL}")
        print(f"  上传服务URL: {cls.UPLOAD_BASE_URL}")
        print(f"  访问令牌: {'已配置' if cls.has_access_token() else '未配置'}")
        print(f"  外部API测试: {'启用' if cls.ENABLE_EXTERNAL_API_TEST else '禁用'}")
        print(f"  测试超时: {cls.TEST_TIMEOUT}秒")
        
        if not cls.has_access_token():
            print("\n💡 要启用外部API测试，请设置环境变量:")
            print("   export REPORT_ACCESS_TOKEN='your_access_token_here'")


# 全局配置实例
config = TestConfig()
