#!/usr/bin/env python3
"""
测试监督节点的真实测试用例
支持不同分支的状态测试
"""

import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))

from src.core.workflow import SpecificWorkflow
from src.models.state import SpecificState, WorkflowStatus, create_initial_state
from langchain_core.messages import HumanMessage, AIMessage

# StreamWriter处理
def get_test_stream_writer():
    """获取测试用的StreamWriter"""
    def test_writer(data):
        print(f"📡 StreamWriter: {data}")
    return test_writer

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)


def create_test_state(
    user_input: str,
    workflow_status: WorkflowStatus = WorkflowStatus.INITIALIZING,
    intent_clarified: bool = False,
    intent_approved: bool = False,
    plan_approved: bool = False,
    execution_started: bool = False,
    messages: list = None,
    **kwargs
) -> SpecificState:
    """创建测试状态"""
    state = create_initial_state(
        session_id="test_session",
        user_input=user_input,
        user_id="test_user"
    )

    # 更新状态
    state["workflow_status"] = workflow_status
    state["intent_clarified"] = intent_clarified
    state["intent_approved"] = intent_approved
    state["plan_approved"] = plan_approved
    state["execution_started"] = execution_started

    # 添加其他参数
    for key, value in kwargs.items():
        state[key] = value

    if messages:
        state["messages"] = messages

    return state


def test_supervisor_node():
    """测试监督节点的各种场景"""
    print("=== 测试监督节点 ===")

    # 创建工作流实例
    workflow = SpecificWorkflow()

    # 测试场景1：初始状态路由
    print("\n1. 测试初始状态路由")
    test_initial_routing(workflow)

    # 测试场景2：意图澄清完成后路由
    print("\n2. 测试意图澄清完成后路由")
    test_intent_clarified_routing(workflow)

    # 测试场景3：计划审批完成后路由
    print("\n3. 测试计划审批完成后路由")
    test_plan_approved_routing(workflow)

    # 测试场景4：执行完成后路由
    print("\n4. 测试执行完成后路由")
    test_execution_completed_routing(workflow)

    # 测试场景5：错误状态处理
    print("\n5. 测试错误状态处理")
    test_error_state_routing(workflow)

    print("\n=== 测试完成 ===")


def test_initial_routing(workflow):
    """测试初始状态的路由"""
    try:
        writer = get_test_stream_writer()

        # 创建初始状态
        state = create_test_state(
            user_input="请分析京东外卖品牌舆情",
            workflow_status=WorkflowStatus.INITIALIZING
        )

        print(f"用户输入: {state['user_input']}")
        print(f"工作流状态: {state['workflow_status']}")
        print(f"意图澄清: {state['intent_clarified']}")

        # 直接调用监督Agent
        result = workflow.supervisor_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")

        # 验证结果
        assert result.goto == "intent_clarification", "初始状态应该路由到意图澄清"

        print("✓ 初始状态路由测试通过")

    except Exception as e:
        print(f"✗ 初始状态路由测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_intent_clarified_routing(workflow):
    """测试意图澄清完成后的路由"""
    try:
        writer = get_test_stream_writer()

        # 创建意图澄清完成的状态
        state = create_test_state(
            user_input="分析京东外卖品牌在近30天的舆情表现",
            workflow_status=WorkflowStatus.CLARIFYING_INTENT,
            intent_clarified=True,
            intent_approved=True,
            intent_summary="分析京东外卖品牌在近30天的舆情表现，包括用户情感分析、热点事件识别"
        )

        print(f"用户输入: {state['user_input']}")
        print(f"工作流状态: {state['workflow_status']}")
        print(f"意图澄清: {state['intent_clarified']}")
        print(f"意图审批: {state['intent_approved']}")

        # 直接调用监督Agent
        result = workflow.supervisor_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")

        # 验证结果
        assert result.goto == "planning", "意图澄清完成后应该路由到规划"

        print("✓ 意图澄清完成路由测试通过")

    except Exception as e:
        print(f"✗ 意图澄清完成路由测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_plan_approved_routing(workflow):
    """测试计划审批完成后的路由"""
    try:
        writer = get_test_stream_writer()

        # 创建计划审批完成的状态
        task_plan = {
            "title": "京东外卖品牌舆情分析执行计划",
            "steps": [
                {"title": "数据收集", "description": "收集相关数据"},
                {"title": "数据分析", "description": "分析数据"}
            ]
        }

        state = create_test_state(
            user_input="分析京东外卖品牌在近30天的舆情表现",
            workflow_status=WorkflowStatus.PLANNING,
            intent_clarified=True,
            intent_approved=True,
            plan_approved=True,
            execution_started=True,
            task_plan=task_plan
        )

        print(f"用户输入: {state['user_input']}")
        print(f"工作流状态: {state['workflow_status']}")
        print(f"计划审批: {state['plan_approved']}")
        print(f"执行开始: {state['execution_started']}")

        # 直接调用监督Agent
        result = workflow.supervisor_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")

        # 验证结果
        assert result.goto == "execution", "计划审批完成后应该路由到执行"

        print("✓ 计划审批完成路由测试通过")

    except Exception as e:
        print(f"✗ 计划审批完成路由测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_execution_completed_routing(workflow):
    """测试执行完成后的路由"""
    try:
        writer = get_test_stream_writer()

        # 创建执行完成的状态
        execution_results = [
            {
                "step_index": 0,
                "step_title": "数据收集",
                "result": "成功收集了相关数据",
                "error": False
            },
            {
                "step_index": 1,
                "step_title": "数据分析",
                "result": "完成了数据分析",
                "error": False
            }
        ]

        state = create_test_state(
            user_input="分析京东外卖品牌在近30天的舆情表现",
            workflow_status=WorkflowStatus.EXECUTING,
            intent_clarified=True,
            intent_approved=True,
            plan_approved=True,
            execution_started=True,
            execution_results=execution_results
        )

        print(f"用户输入: {state['user_input']}")
        print(f"工作流状态: {state['workflow_status']}")
        print(f"执行结果数量: {len(execution_results)}")

        # 直接调用监督Agent
        result = workflow.supervisor_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")

        # 验证结果
        assert result.goto == "summary", "执行完成后应该路由到总结"

        print("✓ 执行完成路由测试通过")

    except Exception as e:
        print(f"✗ 执行完成路由测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_error_state_routing(workflow):
    """测试错误状态的处理"""
    try:
        writer = get_test_stream_writer()

        # 创建错误状态
        state = create_test_state(
            user_input="分析京东外卖品牌舆情",
            workflow_status=WorkflowStatus.FAILED,
            error_info={"message": "执行过程中发生错误", "node": "execution"}
        )

        print(f"用户输入: {state['user_input']}")
        print(f"工作流状态: {state['workflow_status']}")
        print(f"错误信息: {state.get('error_info', {})}")

        # 直接调用监督Agent
        result = workflow.supervisor_agent.execute(state, writer)

        print(f"返回类型: {type(result)}")
        print(f"路由目标: {result.goto}")

        # 验证结果 - 错误状态应该结束流程
        assert result.goto == "__end__", "错误状态应该路由到结束"

        print("✓ 错误状态处理测试通过")

    except Exception as e:
        print(f"✗ 错误状态处理测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_supervisor_message_analysis():
    """测试监督Agent的消息分析功能"""
    print("\n=== 测试监督Agent消息分析 ===")

    workflow = SpecificWorkflow()

    # 测试不同类型的用户消息
    test_cases = [
        {
            "message": "你好",
            "expected_type": "greeting",
            "description": "问候消息"
        },
        {
            "message": "好的，我同意这个计划",
            "expected_type": "agreement",
            "description": "同意消息"
        },
        {
            "message": "请增加竞品分析的内容",
            "expected_type": "supplement",
            "description": "补充信息"
        },
        {
            "message": "这个分析不对，重新做",
            "expected_type": "rejection",
            "description": "拒绝消息"
        },
        {
            "message": "分析苹果公司iPhone15的市场表现",
            "expected_type": "detailed_description",
            "description": "详细描述"
        }
    ]

    for i, test_case in enumerate(test_cases, 1):
        try:
            writer = get_test_stream_writer()

            print(f"\n{i}. 测试{test_case['description']}")

            # 创建包含用户消息的状态
            messages = [HumanMessage(content=test_case["message"])]
            state = create_test_state(
                user_input="初始输入",
                workflow_status=WorkflowStatus.CLARIFYING_INTENT,
                messages=messages
            )

            print(f"   用户消息: {test_case['message']}")

            # 调用监督Agent进行消息分析
            result = workflow.supervisor_agent.execute(state, writer)

            print(f"   分析结果: 路由到 {result.goto}")
            print(f"   ✓ {test_case['description']}分析测试通过")

        except Exception as e:
            print(f"   ✗ {test_case['description']}分析测试失败: {e}")


def test_supervisor_agent_capabilities():
    """测试SupervisorAgent的能力"""
    print("\n=== 测试SupervisorAgent能力 ===")

    workflow = SpecificWorkflow()

    try:
        capabilities = workflow.supervisor_agent.get_capabilities()
        print("SupervisorAgent能力:")
        for i, capability in enumerate(capabilities, 1):
            print(f"   {i}. {capability}")

        role_info = workflow.supervisor_agent.get_role_info()
        print(f"\n角色信息: {role_info['role_name']}")
        print(f"描述: {role_info.get('description', 'N/A')}")

    except Exception as e:
        print(f"获取能力失败: {e}")


def test_complete_workflow_routing():
    """测试完整工作流的路由逻辑"""
    print("\n=== 测试完整工作流路由 ===")

    workflow = SpecificWorkflow()

    # 模拟完整的工作流状态转换
    workflow_states = [
        {
            "name": "初始状态",
            "state": create_test_state(
                user_input="分析品牌舆情",
                workflow_status=WorkflowStatus.INITIALIZING
            ),
            "expected_route": "intent_clarification"
        },
        {
            "name": "意图澄清中",
            "state": create_test_state(
                user_input="分析品牌舆情",
                workflow_status=WorkflowStatus.CLARIFYING_INTENT,
                intent_clarified=False
            ),
            "expected_route": "intent_clarification"
        },
        {
            "name": "意图已澄清",
            "state": create_test_state(
                user_input="分析品牌舆情",
                workflow_status=WorkflowStatus.CLARIFYING_INTENT,
                intent_clarified=True,
                intent_approved=True
            ),
            "expected_route": "planning"
        },
        {
            "name": "规划中",
            "state": create_test_state(
                user_input="分析品牌舆情",
                workflow_status=WorkflowStatus.PLANNING,
                intent_clarified=True,
                intent_approved=True,
                plan_approved=False
            ),
            "expected_route": "planning"
        },
        {
            "name": "计划已审批",
            "state": create_test_state(
                user_input="分析品牌舆情",
                workflow_status=WorkflowStatus.PLANNING,
                intent_clarified=True,
                intent_approved=True,
                plan_approved=True,
                execution_started=True
            ),
            "expected_route": "execution"
        },
        {
            "name": "执行中",
            "state": create_test_state(
                user_input="分析品牌舆情",
                workflow_status=WorkflowStatus.EXECUTING,
                execution_started=True
            ),
            "expected_route": "execution"
        },
        {
            "name": "执行完成",
            "state": create_test_state(
                user_input="分析品牌舆情",
                workflow_status=WorkflowStatus.EXECUTING,
                execution_results=[{"step_index": 0, "result": "完成"}]
            ),
            "expected_route": "summary"
        },
        {
            "name": "总结完成",
            "state": create_test_state(
                user_input="分析品牌舆情",
                workflow_status=WorkflowStatus.COMPLETED,
                summary="任务总结"
            ),
            "expected_route": "__end__"
        }
    ]

    print("完整工作流路由测试:")
    for i, test_case in enumerate(workflow_states, 1):
        try:
            writer = get_test_stream_writer()
            result = workflow.supervisor_agent.execute(test_case["state"], writer)
            actual_route = result.goto
            expected_route = test_case["expected_route"]

            status = "✓" if actual_route == expected_route else "✗"
            print(f"   {i}. {test_case['name']}: {actual_route} {status}")

            if actual_route != expected_route:
                print(f"      期望: {expected_route}, 实际: {actual_route}")

        except Exception as e:
            print(f"   {i}. {test_case['name']}: ✗ 错误 - {e}")


if __name__ == "__main__":
    test_supervisor_node()
    test_supervisor_message_analysis()
    test_supervisor_agent_capabilities()
    test_complete_workflow_routing()
