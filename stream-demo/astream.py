import asyncio
import time
from typing import TypedDict
from langgraph.graph import StateGraph, START, END
from langgraph.config import get_stream_writer  # 添加get_stream_writer导入


class State(TypedDict):
    topic: str
    joke: str


def refine_topic(state: State):
    return {"topic": state["topic"] + " and cats"}


def generate_joke(state: State):
    writer = get_stream_writer()  # 添加get_stream_writer()调用
    current_time = time.strftime("%Y-%m-%d %H:%M:%S")
    writer({"custom_key": f"[{current_time}] Generating custom data inside node"})
    
    # 模拟一些处理时间
    # 在异步函数中，我们不能直接使用time.sleep，而应该使用asyncio.sleep
    # 但这里writer需要立即响应，所以只记录时间不模拟延迟
    
    writer({"custom_key": f"[{current_time}] Generating custom data inside node22"})
    return {"joke": f"This is a joke about {state['topic']}"}


async def stream_graph_updates():
    # 测试不同的stream_mode
    modes = ["values", "updates", "custom", "messages", "debug"]
    
    for mode in modes:
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{current_time}] Testing mode: {mode}")
        
        async for chunk in graph.astream(
            {"topic": "ice cream"},
            stream_mode=mode,
        ):
            current_time = time.strftime("%Y-%m-%d %H:%M:%S")
            print(f"[{current_time}] {chunk}")
        
        print("-" * 40)


graph = (
    StateGraph(State)
    .add_node(refine_topic)
    .add_node(generate_joke)
    .add_edge(START, "refine_topic")
    .add_edge("refine_topic", "generate_joke")
    .add_edge("generate_joke", END)
    .compile()
)

if __name__ == "__main__":
    asyncio.run(stream_graph_updates())