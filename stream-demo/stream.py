import time
from time import sleep
from typing import TypedDict
from langgraph.graph import StateGraph, START, END
from langgraph.config import get_stream_writer

class State(TypedDict):
    topic: str
    joke: str


def refine_topic(state: State):
    return {"topic": state["topic"] + " and cats"}


def generate_joke(state: State):
    writer = get_stream_writer()
    current_time = time.strftime("%Y-%m-%d %H:%M:%S")
    writer({"custom_key": f"[{current_time}] Generating custom data inside node"})
    sleep(10)
    writer({"custom_key22": f"[{current_time}] Generating custom data inside node22"})
    return {"joke": f"This is a joke about {state['topic']}"}


graph = (
    StateGraph(State)
    .add_node(refine_topic)
    .add_node(generate_joke)
    .add_edge(START, "refine_topic")
    .add_edge("refine_topic", "generate_joke")
    .add_edge("generate_joke", END)
    .compile()
)
#
# print("values mode:")
# for chunk in graph.stream(
#     {"topic": "ice cream"},
#     stream_mode="values",
# ):
#     current_time = time.strftime("%Y-%m-%d %H:%M:%S")
#     print(f"[{current_time}] {chunk}")
#
# print("-" * 40)
# print("updates mode:")
# for chunk in graph.stream(
#     {"topic": "ice cream"},
#     stream_mode="updates",
# ):
#     current_time = time.strftime("%Y-%m-%d %H:%M:%S")
#     print(f"[{current_time}] {chunk}")


print("-" * 40)
print("custom mode:")
for chunk in graph.stream(
        {"topic": "ice cream"},
        stream_mode="custom",
):
    current_time = time.strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{current_time}] {chunk}")
#
#
# print("-" * 40)
# print("messages mode:")
# for chunk in graph.stream(
#         {"topic": "ice cream"},
#         stream_mode="messages",
# ):
#     current_time = time.strftime("%Y-%m-%d %H:%M:%S")
#     print(f"[{current_time}] {chunk}")
#
#
# print("-" * 40)
# print("debug mode:")
# for chunk in graph.stream(
#         {"topic": "ice cream"},
#         stream_mode="debug",
# ):
#     current_time = time.strftime("%Y-%m-%d %H:%M:%S")
#     print(f"[{current_time}] {chunk}")