#!/bin/bash

# SupervisorAgent统一分析功能测试脚本
# 使用CURL命令测试API端点

BASE_URL="http://localhost:8000"
USER_ID="curl_test_user"

echo "🚀 SupervisorAgent统一分析功能测试"
echo "=================================="
echo "Base URL: $BASE_URL"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_endpoint() {
    local name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    
    echo -e "${BLUE}📋 测试: $name${NC}"
    echo "URL: $method $url"
    
    if [ "$method" = "GET" ]; then
        response=$(curl -s -w "\n%{http_code}" -X GET "$url" \
            -H "Content-Type: application/json")
    else
        response=$(curl -s -w "\n%{http_code}" -X POST "$url" \
            -H "Content-Type: application/json" \
            -d "$data")
    fi
    
    # 分离响应体和状态码
    http_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | sed '$d')
    
    if [ "$http_code" = "200" ]; then
        echo -e "${GREEN}✅ 成功 (HTTP $http_code)${NC}"
        echo "响应: $(echo "$response_body" | jq -r '.response // .status // .message // .' 2>/dev/null || echo "$response_body")"
    else
        echo -e "${RED}❌ 失败 (HTTP $http_code)${NC}"
        echo "错误: $response_body"
    fi
    echo ""
    
    # 返回响应体供后续使用
    echo "$response_body"
}

# 1. 健康检查
echo -e "${YELLOW}=== 基础API测试 ===${NC}"
test_endpoint "健康检查" "GET" "$BASE_URL/health" > /tmp/health_response.json

# 2. 系统状态
test_endpoint "系统状态" "GET" "$BASE_URL/system/status" > /tmp/status_response.json

# 3. 获取工具列表
echo -e "${YELLOW}=== 工具和Agent信息 ===${NC}"
test_endpoint "获取工具列表" "GET" "$BASE_URL/system/tools" > /tmp/tools_response.json

# 4. 获取Agent信息
test_endpoint "获取Agent信息" "GET" "$BASE_URL/system/agents" > /tmp/agents_response.json

# 检查统一分析工具是否存在
echo -e "${BLUE}🔍 检查统一分析工具...${NC}"
if grep -q "unified_message_analysis" /tmp/tools_response.json; then
    echo -e "${GREEN}✅ 统一分析工具已注册${NC}"
else
    echo -e "${RED}❌ 统一分析工具未找到${NC}"
fi
echo ""

# 5. 统一分析功能测试
echo -e "${YELLOW}=== 统一分析功能测试 ===${NC}"

# 测试1: 问候消息 (greeting)
greeting_response=$(test_endpoint "问候消息 (greeting)" "POST" "$BASE_URL/chat" '{
  "message": "你好",
  "user_id": "'$USER_ID'_greeting"
}')

# 测试2: 分析请求 (supplement)
analysis_response=$(test_endpoint "分析请求 (supplement)" "POST" "$BASE_URL/chat" '{
  "message": "我想分析理想汽车的舆情",
  "user_id": "'$USER_ID'_analysis"
}')

# 提取session_id
SESSION_ID=$(echo "$analysis_response" | jq -r '.session_id' 2>/dev/null)
if [ "$SESSION_ID" != "null" ] && [ "$SESSION_ID" != "" ]; then
    echo -e "${GREEN}📝 Session ID: $SESSION_ID${NC}"
    echo ""
    
    # 测试3: 确认消息 (agreement)
    test_endpoint "确认消息 (agreement)" "POST" "$BASE_URL/chat" '{
      "message": "好的，确认",
      "session_id": "'$SESSION_ID'",
      "user_id": "'$USER_ID'_analysis"
    }' > /tmp/confirm_response.json
    
    # 测试4: 补充信息 (detailed_description)
    test_endpoint "补充信息 (detailed_description)" "POST" "$BASE_URL/chat" '{
      "message": "我想分析理想汽车在抖音平台近一个月的传播情况，重点关注用户评价和销量趋势",
      "session_id": "'$SESSION_ID'",
      "user_id": "'$USER_ID'_analysis"
    }' > /tmp/supplement_response.json
    
    # 测试5: 拒绝消息 (rejection)
    test_endpoint "拒绝消息 (rejection)" "POST" "$BASE_URL/chat" '{
      "message": "不对，重新来",
      "session_id": "'$SESSION_ID'",
      "user_id": "'$USER_ID'_analysis"
    }' > /tmp/rejection_response.json
    
    # 获取会话信息
    echo -e "${YELLOW}=== 会话状态查询 ===${NC}"
    test_endpoint "获取会话信息" "GET" "$BASE_URL/session/$SESSION_ID" > /tmp/session_info.json
    
    # 获取工作流状态
    test_endpoint "获取工作流状态" "GET" "$BASE_URL/session/$SESSION_ID/workflow" > /tmp/workflow_status.json
    
else
    echo -e "${RED}❌ 无法获取Session ID，跳过多轮对话测试${NC}"
fi

# 6. 测试结果分析
echo -e "${YELLOW}=== 测试结果分析 ===${NC}"

echo -e "${BLUE}📊 统一分析功能验证:${NC}"

# 检查问候消息处理
if grep -q "您好" /tmp/greeting_response.json 2>/dev/null; then
    echo -e "${GREEN}✅ 问候消息正确处理${NC}"
else
    echo -e "${RED}❌ 问候消息处理异常${NC}"
fi

# 检查分析请求处理
if grep -q "clarifying_intent\|initializing" /tmp/analysis_response.json 2>/dev/null; then
    echo -e "${GREEN}✅ 分析请求正确路由${NC}"
else
    echo -e "${RED}❌ 分析请求路由异常${NC}"
fi

# 检查多轮对话
if [ -f /tmp/confirm_response.json ] && [ -s /tmp/confirm_response.json ]; then
    echo -e "${GREEN}✅ 多轮对话功能正常${NC}"
else
    echo -e "${RED}❌ 多轮对话功能异常${NC}"
fi

echo ""
echo -e "${GREEN}🎉 测试完成！${NC}"
echo -e "${BLUE}💡 提示: 查看 /tmp/*_response.json 文件获取详细响应信息${NC}"

# 清理函数
cleanup() {
    echo ""
    echo -e "${YELLOW}🧹 清理临时文件...${NC}"
    rm -f /tmp/*_response.json /tmp/session_info.json /tmp/workflow_status.json
    echo -e "${GREEN}✅ 清理完成${NC}"
}

# 询问是否清理
echo ""
read -p "是否清理临时文件? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    cleanup
fi
