#!/usr/bin/env python3
"""
测试报告API功能
"""

import asyncio
import json


async def test_report_agent():
    """测试ReportAgent"""
    print("🧪 测试ReportAgent...")

    try:
        from src.agents.report import ReportAgent
        from langchain_openai import ChatOpenAI

        # 创建ReportAgent
        llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
        report_agent = ReportAgent(llm=llm)

        # 创建品牌分析DSL
        brand_dsl = report_agent.create_brand_analysis_dsl(
            brand_name="京东",
            event_id="test_event_001",
            analysis_data={
                "sentiment_score": 82,
                "mention_count": 1580,
                "positive_ratio": 0.68,
                "negative_ratio": 0.18,
                "neutral_ratio": 0.14
            }
        )
        print(f"📊 品牌分析DSL: {json.dumps(brand_dsl, indent=2, ensure_ascii=False)}")

        # 测试报告生成（注意：这会调用真实的外部API）
        print("\n🚀 测试报告生成...")
        try:
            result = await report_agent.generate_report(brand_dsl)
            if result["success"]:
                html_content = result["html_content"]
                print(f"✅ 报告生成成功!")
                print(f"📄 HTML内容长度: {len(html_content)} 字符")
                print(f"📋 内容类型: {result['content_type']}")
                print(f"🎯 消息: {result['message']}")
                # 只显示HTML的前200个字符作为预览
                print(f"📖 HTML预览: {html_content[:200]}...")
            else:
                print(f"❌ 报告生成失败: {result}")
        except Exception as e:
            print(f"❌ 报告生成失败: {e}")

    except Exception as e:
        print(f"❌ ReportAgent测试失败: {e}")


async def test_api_endpoints():
    """测试API端点"""
    print("\n🌐 测试API端点...")
    
    try:
        from src.api.main import SpecificAPI
        from src.api.schemas import ChatRequest
        
        # 初始化API
        api = SpecificAPI()
        
        # 创建ReportAgent来生成DSL
        from src.agents.report import ReportAgent
        from langchain_openai import ChatOpenAI

        llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
        report_agent = ReportAgent(llm=llm)

        # 创建包含report_dsl的请求
        brand_dsl = report_agent.create_brand_analysis_dsl("京东")
        
        request = ChatRequest(
            message="请分析京东的品牌舆情",
            session_id="test_report_001",
            user_id="test_user",
            report_dsl=brand_dsl
        )
        
        print(f"📤 发送请求: {request.message}")
        print(f"📋 包含DSL: {bool(request.report_dsl)}")
        
        # 调用API
        response = await api.chat(request)
        
        print(f"📥 收到响应:")
        print(f"  Session ID: {response.session_id}")
        print(f"  Status: {response.status}")
        print(f"  Response: {response.response[:100]}...")
        
        # 检查状态中是否包含report_dsl
        session_state = api.workflow.get_session_status("test_report_001")
        if session_state:
            has_dsl = bool(session_state.get("values", {}).get("report_dsl"))
            print(f"  State contains DSL: {has_dsl}")
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")


def test_curl_command():
    """生成测试用的curl命令"""
    print("\n📋 生成测试curl命令...")

    try:
        from src.agents.report import ReportAgent
        from langchain_openai import ChatOpenAI

        llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
        report_agent = ReportAgent(llm=llm)

        # 创建品牌分析DSL的curl命令
        brand_dsl = report_agent.create_brand_analysis_dsl("京东")

        brand_curl_command = f"""
curl -v -X POST https://console-playground.fed.chehejia.com/__ui/report \\
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \\
  -H "Content-Type: application/json" \\
  -d '{json.dumps(brand_dsl, ensure_ascii=False)}'
"""

        print("🏢 品牌分析curl命令:")
        print(brand_curl_command)

    except Exception as e:
        print(f"❌ 生成curl命令失败: {e}")


async def main():
    """主测试函数"""
    print("🎯 开始测试报告API功能\n")

    # 测试ReportAgent
    await test_report_agent()
    
    # 测试API端点
    await test_api_endpoints()
    
    # 生成curl命令
    test_curl_command()
    
    print("\n✅ 测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
