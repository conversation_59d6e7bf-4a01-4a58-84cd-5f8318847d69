1. 初始请求与意图澄清：
  - 璇玑通过POST请求到/chat-message接口，向品牌事件Agent发送用户请求(任务)
  - Agent分析用户意图，判断请求是否完整明确
  - 若需要澄清：Agent向用户提问，进行意图澄清（可能涉及多轮对话）
  - 若无需澄清：直接进入下一步
  - 意图澄清完成后，Agent响应接收请求，并通知用户将处理品牌事件报表请求
2. 调用MCP服务阶段：
  - Agent将用户请求转发给品牌MCPServer
  - Agent通知UI已开始调用MCP服务
  - Agent进入Human in Loop暂停状态，等待MCP处理完成
  - MCPServer后台异步执行DSL组装
3. MCP回调阶段：
  - MCP处理完成后通过POST请求到/chat-message接口回调Agent
  - 回调请求BODY中包含生成的DSL数据
  - Agent从暂停状态恢复
4. 报表生成阶段：
  - Agent通知UI已收到MCP数据，开始调用报表服务
  - Agent调用报表服务，传递DSL数据
  - 报表服务根据DSL生成HTML报表
  - 报表服务返回HTML结果给Agent
5. 结果返回阶段：
  - Agent通知UI报表已生成
  - Agent向UI发送最终包含HTML内容的报表结果
整个过程中，Agent实时与璇玑UI交互，及时通知用户任务的各个状态变化。