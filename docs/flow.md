
![SPD-B1-036(齿 拆解情况 轴系统耐久)输入 描述 轴齿面多处点蚀 15:39 2023年10月08日 ?上海市理想汽车上海试制 试验中心 拍摄人:曹秀娟 今日水印 -相机-](https://bj.bcebos.com/prod-public-open/cfe-global/images/T7TjbtvknokGMvxIW70cnCKVn96-20250604-jkx7c58y4wx4) 


```mermaid
sequenceDiagram
    participant UI as 璇玑UI
    participant Agent as 品牌事件Agent
    participant MCP as 品牌MCPServer
    participant Report as 报表服务

    Note over UI, Report: 品牌事件报表生成流程

    UI->>Agent: 发送用户请求(任务) <br/> POST /chat-message
    activate Agent
    
    Note right of Agent: 分析用户意图<br/>判断是否需要澄清
    
    alt 需要澄清
        Agent->>UI: 提出意图澄清问题 <br/> "您是想要查询[具体时间/品牌/维度]的报表吗？"
        
        Note right of Agent: Agent进入Human in Loop状态<br/>等待用户输入
        deactivate Agent
        
        UI->>Agent: 用户回复澄清信息
        activate Agent
        
        Note right of Agent: 判断是否需要进一步澄清
        
        loop 需要进一步澄清
            Agent->>UI: 提出更多澄清问题
            
            Note right of Agent: Agent再次进入Human in Loop状态
            deactivate Agent
            
            UI->>Agent: 用户提供额外信息
            activate Agent
        end
        
        Agent->>UI: 响应接收请求 <br/> "感谢确认，我将为您处理品牌事件报表请求"
    else 意图已明确
        Agent->>UI: 响应接收请求 <br/> "我将为您处理品牌事件报表请求"
    end
    
    Agent->>MCP: 转发明确后的用户请求(任务)
    activate MCP
    
    Agent->>UI: 更新状态 <br/> "正在调用MCP服务组装报表数据..."
    
    Note right of Agent: Agent进入暂停状态<br/>等待MCP回调
    deactivate Agent
    
    Note right of MCP: 异步执行组装报表需要的DSL
    
    MCP-->>Agent: 回调 POST /chat-message <br/> BODY包含DSL数据
    activate Agent
    
    Note left of Agent: Agent从暂停状态恢复
    
    Agent->>UI: 更新状态 <br/> "MCP数据已准备完成，正在调用报表服务..."
    deactivate MCP
    
    Agent->>Report: 发送DSL请求生成报表
    activate Report
    
    Note right of Report: 根据DSL生成HTML报表
    
    Report-->>Agent: 返回HTML报表
    deactivate Report
    
    Agent->>UI: 更新状态 <br/> "报表已生成，正在返回结果..."
    
    Agent->>UI: 发送最终报表结果 <br/> 包含HTML内容
    deactivate Agent
    
    Note over UI, Report: 流程结束
```