# 简化路由架构设计

## 🎯 设计理念

### 核心原则
1. **SupervisorAgent：简单路由分发器**
   - 只根据 `workflow_status` 做简单路由分发
   - 不做复杂的消息分析和业务逻辑判断
   - 支持多轮对话时快速路由到对应Agent

2. **各个Agent：自主路由逻辑**
   - 每个Agent处理自己的业务逻辑
   - 每个Agent决定自己的下一步路由
   - 使用Command返回路由决策

## 🔄 SupervisorAgent：智能路由分发器

### 职责
- 基于LLM分析用户消息类型（问候/任务/补充信息）
- 根据消息类型和workflow_status进行智能路由
- 保持路由逻辑简单清晰
- 支持多轮对话的状态驱动路由

### LLM路由决策
```python
def _llm_based_routing(self, state: SpecificState, session_id: str) -> Command:
    # 构建路由决策提示词（包含显式JSON结构声明）
    routing_prompt = self.build_routing_prompt(state)

    # 使用结构化输出获取完整的路由决策
    response = self.llm.with_structured_output(RouterDecisionWithAnalysis).invoke(messages)

    # 直接根据LLM返回的结构化结果构建Command
    update_data = {
        "reason": response.reason,
        "workflow_status": response.workflow_status
    }

    # 如果是问候消息，添加回复消息
    if response.message_analysis.message_type == "greeting" and response.response_message:
        update_data["messages"] = [HumanMessage(content=response.response_message)]

    return Command(goto=response.next, update=update_data)
```

### 路由规则
```python
# 1. 问候消息处理
if message_type == "greeting":
    return Command(goto="__end__")

# 2. 任务相关消息处理 - 根据workflow_status路由
route_map = {
    WorkflowStatus.INITIALIZING: "intent_clarification",
    WorkflowStatus.CLARIFYING_INTENT: "intent_clarification",
    WorkflowStatus.PLANNING: "planning",
    WorkflowStatus.EXECUTING: "execution",
    WorkflowStatus.SUMMARIZING: "summary"
}
```

### 结构化输出模型
```python
class RouterDecisionWithAnalysis(BaseModel):
    message_analysis: UserMessageAnalysis = Field(description="用户消息分析结果")
    next: Literal["intent_clarification", "planning", "execution", "summary", "__end__"] = Field(
        description="下一步路由决策"
    )
    reason: str = Field(description="路由决策理由")
    response_message: str = Field(default="", description="需要返回给用户的消息（仅问候时使用）")
    workflow_status: str = Field(description="更新后的工作流状态")
```

### 消息类型分析
- **greeting（问候）**：你好、您好、hi、hello、嗨等问候语
- **task（任务请求）**：用户提出的具体任务需求
- **supplement（补充信息）**：针对当前任务的补充、修改、确认等信息
- **agreement（确认同意）**：确认、同意、好的、可以等
- **rejection（拒绝否定）**：不对、不是、错误、重新等

### 显式JSON结构声明
提示词中明确声明期望的JSON输出格式：
```json
{
    "message_analysis": {
        "message_type": "greeting|task|supplement|agreement|rejection|...",
        "user_intent": "用户真实意图分析",
        "extracted_info": "从用户消息中提取的关键信息"
    },
    "next": "intent_clarification|planning|execution|summary|__end__",
    "reason": "路由决策理由",
    "response_message": "需要返回给用户的消息（仅问候时填写）",
    "workflow_status": "更新后的工作流状态"
}
```

## 🎯 IntentAnalysisAgent：自主路由逻辑

### 路由决策
```python
def execute(self, state: SpecificState) -> Command[
    Literal["intent_clarification", "planning", "__end__"]]:
```

### 路由逻辑

#### 1. 用户确认审批
```python
if intent_analysis.intent_type == "agreement":
    return Command(
        goto="planning",  # 直接进入计划阶段
        update={
            "intent_approved": True,
            "human_approval_required": False,
            "workflow_status": WorkflowStatus.PLANNING
        }
    )
```

#### 2. 意图清晰，需要审批
```python
if clarification_result.intent_clear:
    return Command(
        goto="__end__",  # 等待用户确认
        update={
            "intent_clarified": True,
            "human_approval_required": True,
            "workflow_status": WorkflowStatus.CLARIFYING_INTENT
        }
    )
```

#### 3. 需要进一步澄清
```python
else:
    return Command(
        goto="__end__",  # 等待用户回复
        update={
            "human_approval_required": True,
            "workflow_status": WorkflowStatus.CLARIFYING_INTENT,
            "clarification_round": clarification_round + 1
        }
    )
```

## 📋 PlanningAgent：自主路由逻辑

### 路由决策
```python
def execute(self, state: SpecificState) -> Command[
    Literal["planning", "execution", "__end__"]]:
```

### 路由逻辑

#### 1. 用户确认计划
```python
if intent_analysis.intent_type == "agreement":
    return Command(
        goto="execution",  # 直接进入执行阶段
        update={
            "plan_approved": True,
            "execution_started": True,
            "human_approval_required": False,
            "workflow_status": WorkflowStatus.EXECUTING
        }
    )
```

#### 2. 计划创建完成，需要审批
```python
return Command(
    goto="__end__",  # 等待用户确认
    update={
        "human_approval_required": True,
        "task_plan": execution_plan.to_dict(),
        "plan_approved": False,
        "workflow_status": WorkflowStatus.PLANNING
    }
)
```

## 🔄 完整工作流程

### 场景1：新用户请求
```
1. 用户："我想分析理想汽车的舆情"
   → workflow_status: INITIALIZING
   → Supervisor路由到: intent_clarification

2. IntentAnalysisAgent执行：
   → 分析意图（假设清晰）
   → 返回Command(goto="__end__", update={...})
   → 设置human_approval_required=True，等待用户确认

3. 用户："好的，确认"
   → workflow_status: CLARIFYING_INTENT
   → Supervisor路由到: intent_clarification

4. IntentAnalysisAgent处理确认：
   → 识别agreement类型
   → 返回Command(goto="planning", update={...})
   → 直接进入计划阶段

5. PlanningAgent执行：
   → 创建执行计划
   → 返回Command(goto="__end__", update={...})
   → 等待用户确认计划

6. 用户："确认计划"
   → workflow_status: PLANNING
   → Supervisor路由到: planning

7. PlanningAgent处理确认：
   → 识别agreement类型
   → 返回Command(goto="execution", update={...})
   → 直接进入执行阶段
```

### 场景2：用户补充信息
```
1. 用户："我想分析汽车"
   → IntentAnalysisAgent: 意图不清晰
   → Command(goto="__end__", 需要澄清)

2. 用户："我想分析理想汽车在抖音的传播情况"
   → workflow_status: CLARIFYING_INTENT
   → Supervisor路由到: intent_clarification
   → IntentAnalysisAgent: 识别为supplement，重新分析
   → Command(goto="__end__", 意图现在清晰，等待确认)
```

## ✅ 架构优势

### 1. 简单清晰
- SupervisorAgent只做简单的状态路由
- 各Agent专注于自己的业务逻辑
- 路由逻辑分散，易于理解和维护

### 2. 高度自主
- 每个Agent决定自己的下一步
- 减少了Agent之间的耦合
- 便于独立开发和测试

### 3. 多轮对话友好
- 基于workflow_status快速路由
- 支持复杂的多轮交互
- 状态管理清晰

### 4. 易于扩展
- 添加新Agent只需在Supervisor中添加路由规则
- 各Agent的路由逻辑独立，不影响其他Agent
- 便于功能迭代和优化

## 🔧 实现要点

### SupervisorAgent
```python
def _simple_status_routing(self, state: SpecificState, session_id: str) -> Command:
    # 检查问候消息
    if self._is_greeting_message(state):
        return Command(goto="__end__", update={...})
    
    # 简单状态路由
    route_map = {...}
    next_route = route_map.get(workflow_status, "intent_clarification")
    return Command(goto=next_route)
```

### Agent路由模式
```python
def execute(self, state: SpecificState) -> Command[Literal[...]]:
    # 处理业务逻辑
    result = self.process_business_logic(state)
    
    # 决定下一步路由
    if condition1:
        return Command(goto="next_agent", update={...})
    elif condition2:
        return Command(goto="__end__", update={...})
    else:
        return Command(goto="current_agent", update={...})
```

## 🆚 架构对比

### 传统复杂路由架构
- ❌ SupervisorAgent承担过于复杂的业务逻辑判断
- ❌ 各Agent只处理业务逻辑，无路由决策权
- ❌ 中心化路由，难以维护和扩展
- ❌ 高耦合度，修改一个Agent影响整体

### 新LLM智能路由架构
- ✅ SupervisorAgent基于LLM进行智能消息分析
- ✅ 各Agent自主处理业务逻辑和路由决策
- ✅ 简单清晰的路由规则，基于映射表
- ✅ 低耦合度，Agent独立开发和测试
- ✅ 智能的自然语言理解能力

## 🎯 关键改进

1. **智能消息分析**：基于LLM准确识别用户消息类型和意图
2. **上下文感知路由**：结合当前工作流状态和消息历史进行路由决策
3. **简单清晰规则**：基于映射表的简单路由规则，易于理解和维护
4. **自主决策**：Agent拥有完整的路由决策权
5. **状态驱动**：基于workflow_status的清晰状态转换
6. **结构化输出**：使用Pydantic模型确保输出格式的一致性
7. **扩展性强**：新增Agent和修改路由逻辑都很简单

## 🎉 架构优势

### 1. 智能化
- 基于LLM的自然语言理解
- 准确识别用户意图和消息类型
- 上下文感知的路由决策

### 2. 简单化
- 清晰的路由规则映射表
- 结构化的决策输出
- 易于理解和维护的代码

### 3. 模块化
- Agent独立的业务逻辑处理
- 分布式的路由决策
- 低耦合的系统设计

### 4. 可扩展
- 轻松添加新的消息类型
- 简单配置新的路由规则
- 灵活的Agent扩展机制

这种LLM智能路由架构既保持了系统的简单性，又提供了强大的智能分析能力，为复杂的多轮对话和任务处理提供了理想的基础框架。
