# Specific多Agent系统快速开始指南

## 🚀 快速启动

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd brand-specific-agent

# 安装uv包管理器
curl -LsSf https://astral.sh/uv/install.sh | sh

# 安装依赖
uv sync

# 设置环境变量
export OPENAI_API_KEY="your-openai-api-key"
export REDIS_URL="redis://localhost:6379"
```

### 2. 启动服务

```bash
# 启动Redis (如果本地没有)
docker run -d -p 6379:6379 redis:7-alpine

# 启动API服务
uv run python src/api/server.py
```

### 3. 测试API

```bash
# 发送聊天消息
curl -X POST "http://localhost:8000/v4/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "我想分析理想汽车的舆情",
    "session_id": "test_session_001"
  }'
```

## 📋 核心概念

### 1. 简化路由架构

```python
# SupervisorAgent: 简单路由分发
def _simple_status_routing(self, state):
    route_map = {
        WorkflowStatus.INITIALIZING: "intent_clarification",
        WorkflowStatus.CLARIFYING_INTENT: "intent_clarification",  
        WorkflowStatus.PLANNING: "planning",
        WorkflowStatus.EXECUTING: "execution"
    }
    return Command(goto=route_map[workflow_status])

# Agent: 自主路由决策
def execute(self, state) -> Command:
    if user_approved:
        return Command(goto="next_stage", update={...})
    else:
        return Command(goto="__end__", update={...})
```

### 2. 工作流状态

```python
class WorkflowStatus(Enum):
    INITIALIZING = "initializing"           # 初始化
    CLARIFYING_INTENT = "clarifying_intent" # 意图澄清
    PLANNING = "planning"                   # 任务规划
    EXECUTING = "executing"                 # 任务执行
    SUMMARIZING = "summarizing"             # 结果总结
    COMPLETED = "completed"                 # 完成
```

### 3. 多轮对话流程

```
用户请求 → 意图澄清 → 等待确认 → 任务规划 → 等待确认 → 任务执行 → 结果总结
```

## 🎯 使用示例

### 示例1: 基本对话流程

```python
# 1. 用户发起请求
POST /v4/chat
{
  "message": "我想分析理想汽车的舆情",
  "session_id": "session_001"
}

# 响应: 意图澄清
{
  "message": "我分析到您想要分析理想汽车的舆情。请确认这个理解是否正确？",
  "workflow_status": "clarifying_intent",
  "requires_human_input": true
}

# 2. 用户确认
POST /v4/chat
{
  "message": "确认",
  "session_id": "session_001"
}

# 响应: 任务规划
{
  "message": "根据您的需求，我制定了以下执行计划：\n1. 数据收集...\n请确认此计划。",
  "workflow_status": "planning",
  "requires_human_input": true
}

# 3. 用户确认计划
POST /v4/chat
{
  "message": "确认计划",
  "session_id": "session_001"
}

# 响应: 开始执行
{
  "message": "感谢确认！我将按照计划开始执行任务。",
  "workflow_status": "executing",
  "requires_human_input": false
}
```

### 示例2: 意图不清晰的处理

```python
# 1. 模糊请求
POST /v4/chat
{
  "message": "帮我分析一下",
  "session_id": "session_002"
}

# 响应: 澄清问题
{
  "message": "为了为您提供精准的分析服务，我需要了解：\n1. 您想分析哪个具体的品牌？\n2. 您希望分析什么类型的内容？",
  "workflow_status": "clarifying_intent",
  "requires_human_input": true
}

# 2. 用户补充信息
POST /v4/chat
{
  "message": "我想分析理想汽车在抖音平台的传播情况",
  "session_id": "session_002"
}

# 响应: 重新分析后确认
{
  "message": "现在我理解了您的需求：分析理想汽车在抖音平台的传播情况。请确认这个理解是否正确？",
  "workflow_status": "clarifying_intent", 
  "requires_human_input": true
}
```

## 🛠️ 自定义开发

### 1. 添加新Agent

```python
# 1. 创建Agent类
class CustomAgent(BaseAgent):
    def __init__(self, llm: BaseChatModel):
        super().__init__(
            llm=llm,
            role_name="CustomAgent",
            system_prompt="你是一个自定义Agent..."
        )
    
    def execute(self, state: SpecificState) -> Command[Literal["custom", "__end__"]]:
        # 处理业务逻辑
        result = self.process_custom_logic(state)
        
        # 自主路由决策
        if result.success:
            return Command(goto="__end__", update={"custom_result": result.data})
        else:
            return Command(goto="custom", update={"error": result.error})

# 2. 在工作流中注册
workflow.add_node("custom", self._custom_node)

# 3. 在Supervisor中添加路由
route_map[WorkflowStatus.CUSTOM] = "custom"
```

### 2. 添加新工具

```python
# 1. 创建工具
@tool
def custom_analysis_tool(query: str) -> str:
    """自定义分析工具"""
    # 实现工具逻辑
    return f"分析结果: {query}"

# 2. 注册工具
tool_registry.register_tool(
    name="custom_analysis",
    tool=custom_analysis_tool,
    allowed_agents=["CustomAgent"]
)

# 3. 在Agent中使用
class CustomAgent(BaseAgent):
    def __init__(self, llm: BaseChatModel):
        tools = tool_registry.get_tools_for_agent("CustomAgent")
        super().__init__(llm=llm, tools=tools, ...)
```

### 3. 自定义状态字段

```python
# 扩展状态模型
class ExtendedState(SpecificState):
    custom_field: Optional[str] = None
    custom_data: Optional[Dict[str, Any]] = None

# 在Agent中使用
def execute(self, state: ExtendedState) -> Command:
    return Command(
        goto="next_stage",
        update={"custom_field": "custom_value"}
    )
```

## 🔧 配置和部署

### 1. 环境配置

```yaml
# config.yaml
llm:
  provider: "openai"
  model: "gpt-4"
  temperature: 0.7
  max_tokens: 2000

redis:
  url: "redis://localhost:6379"
  db: 0

agents:
  intent_analysis:
    enabled: true
    tools: ["intent_analysis", "clarification_generation"]
  
  planning:
    enabled: true
    tools: ["task_planning", "plan_validation"]
```

### 2. Docker部署

```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .

RUN pip install uv
RUN uv sync

EXPOSE 8000
CMD ["uv", "run", "python", "src/api/server.py"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  specific-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
```

### 3. 生产部署

```bash
# 构建和部署
docker-compose up -d

# 健康检查
curl http://localhost:8000/health

# 查看日志
docker-compose logs -f specific-api
```

## 📊 监控和调试

### 1. 日志查看

```bash
# 查看Agent执行日志
tail -f logs/specific.log | grep "IntentAnalysisAgent"

# 查看错误日志
tail -f logs/specific.log | grep "ERROR"
```

### 2. 状态检查

```bash
# 获取会话状态
curl "http://localhost:8000/v4/session/session_001/status"

# 获取系统状态
curl "http://localhost:8000/v4/system/status"
```

### 3. 性能监控

```python
# 添加性能监控
from prometheus_client import start_http_server, Counter, Histogram

# 启动监控服务
start_http_server(8001)

# 查看指标
curl http://localhost:8001/metrics
```

## 🧪 测试

### 1. 单元测试

```bash
# 运行所有测试
uv run pytest

# 运行特定测试
uv run pytest test/test_agents.py::test_intent_analysis_agent

# 运行覆盖率测试
uv run pytest --cov=src
```

### 2. 集成测试

```bash
# 测试完整工作流
uv run python test/test_workflow_integration.py

# 测试API接口
uv run python test/test_api_endpoints.py
```

### 3. 性能测试

```bash
# 并发测试
uv run python test/test_performance.py

# 压力测试
ab -n 1000 -c 10 http://localhost:8000/v4/chat
```

## 🎯 最佳实践

### 1. Agent设计
- 保持单一职责原则
- 实现清晰的路由逻辑
- 添加详细的日志记录
- 处理异常情况

### 2. 状态管理
- 最小化状态字段
- 使用类型提示
- 避免状态污染
- 定期清理过期状态

### 3. 工具开发
- 确保工具幂等性
- 添加输入验证
- 实现错误处理
- 提供详细文档

### 4. 性能优化
- 使用连接池
- 实现缓存机制
- 优化LLM调用
- 监控关键指标

通过这个快速开始指南，你可以快速上手Specific多Agent系统，并根据具体需求进行定制开发。
