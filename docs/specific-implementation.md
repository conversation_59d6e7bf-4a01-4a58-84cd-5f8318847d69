# Specific系统实现指南

## 🚀 项目结构设计

```
Specific/
├── src/
│   ├── Specific/
│   │   ├── __init__.py
│   │   ├── core/                    # 核心框架
│   │   │   ├── __init__.py
│   │   │   ├── state.py            # 状态定义
│   │   │   ├── workflow.py         # 工作流构建
│   │   │   ├── tools.py            # 工具系统
│   │   │   └── config.py           # 配置管理
│   │   ├── agents/                 # Agent实现
│   │   │   ├── __init__.py
│   │   │   ├── base.py             # Agent基类
│   │   │   ├── intent_clarification.py
│   │   │   ├── planning.py
│   │   │   ├── execution.py
│   │   │   └── message.py
│   │   ├── tools/                  # 工具实现
│   │   │   ├── __init__.py
│   │   │   ├── planning/           # 规划工具
│   │   │   ├── execution/          # 执行工具
│   │   │   └── messaging/          # 消息工具
│   │   ├── models/                 # 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── state.py
│   │   │   ├── plan.py
│   │   │   ├── message.py
│   │   │   └── result.py
│   │   ├── services/               # 外部服务
│   │   │   ├── __init__.py
│   │   │   ├── llm.py
│   │   │   ├── storage.py
│   │   │   └── notification.py
│   │   └── api/                    # API接口
│   │       ├── __init__.py
│   │       ├── routes/
│   │       ├── middleware/
│   │       └── schemas/
│   └── tests/                      # 测试文件
├── docs/                           # 文档
├── config/                         # 配置文件
├── scripts/                        # 脚本文件
├── pyproject.toml
└── README.md
```

## 🔧 核心组件实现

### 1. 状态定义 (src/Specific/core/state.py)

```python
from typing import Dict, List, Optional, Any, TypedDict
from enum import Enum
from pydantic import BaseModel
from datetime import datetime

class WorkflowStatus(str, Enum):
    INITIALIZING = "initializing"
    CLARIFYING_INTENT = "clarifying_intent"
    PLANNING = "planning"
    CONFIRMING_PLAN = "confirming_plan"
    EXECUTING = "executing"
    SUMMARIZING = "summarizing"
    COMPLETED = "completed"
    FAILED = "failed"
    PAUSED = "paused"

class MessageType(str, Enum):
    USER_INPUT = "user_input"
    CLARIFICATION_REQUEST = "clarification_request"
    PLAN_CONFIRMATION = "plan_confirmation"
    STATUS_UPDATE = "status_update"
    RESULT_OUTPUT = "result_output"

class Message(BaseModel):
    id: str
    type: MessageType
    content: str
    timestamp: datetime
    metadata: Dict[str, Any] = {}

class PlanStep(BaseModel):
    id: str
    description: str
    tools: List[str]
    estimated_time: str
    dependencies: List[str] = []
    status: str = "pending"
    result: Optional[Dict[str, Any]] = None

class Plan(BaseModel):
    id: str
    title: str
    overview: str
    steps: List[PlanStep]
    total_estimated_time: str
    created_at: datetime

class SpecificState(TypedDict):
    # 会话信息
    session_id: str
    user_id: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    # 用户输入和对话
    user_input: str
    conversation_history: List[Message]
    
    # 意图澄清
    intent_clarified: bool
    clarified_intent: Optional[str]
    clarification_questions: List[str]
    user_responses: List[str]
    
    # 任务规划
    task_plan: Optional[Plan]
    plan_approved: bool
    current_step_index: int
    
    # 执行状态
    execution_results: List[Dict[str, Any]]
    current_execution_status: str
    
    # 消息和通信
    pending_messages: List[Message]
    user_feedback_required: bool
    
    # 系统状态
    workflow_status: WorkflowStatus
    error_info: Optional[Dict[str, Any]]
    
    # 扩展字段
    metadata: Dict[str, Any]
```

### 2. Agent基类 (src/Specific/agents/base.py)

```python
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from ..models.state import SpecificState
from ..core.tools import ToolRegistry

class BaseAgent(ABC):
    def __init__(self, name: str, description: str, tools: Optional[List[str]] = None):
        self.name = name
        self.description = description
        self.tools = tools or []
        self.tool_registry = ToolRegistry()
    
    @abstractmethod
    async def process(self, state: SpecificState, **kwargs) -> Dict[str, Any]:
        """处理状态并返回结果"""
        pass
    
    async def call_tool(self, tool_name: str, **kwargs) -> Any:
        """调用工具"""
        tool = self.tool_registry.get_tool(tool_name)
        if not tool:
            raise ValueError(f"Tool {tool_name} not found")
        
        if not tool.validate_input(**kwargs):
            raise ValueError(f"Invalid input for tool {tool_name}")
        
        return await tool.execute(**kwargs)
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return self.tools
    
    async def log_action(self, action: str, details: Dict[str, Any]):
        """记录Agent行为"""
        # 实现日志记录逻辑
        pass
```

### 3. 意图澄清Agent (src/Specific/agents/intent_clarification.py)

```python
from typing import Dict, Any, List
from .base import BaseAgent
from ..models.state import SpecificState, Message, MessageType
from ..services.llm import LLMService

class IntentClarificationAgent(BaseAgent):
    def __init__(self, llm_service: LLMService):
        super().__init__(
            name="intent_clarification",
            description="负责澄清用户意图",
            tools=["analyze_intent", "generate_questions"]
        )
        self.llm_service = llm_service
        self.max_clarification_rounds = 3
    
    async def process(self, state: SpecificState, **kwargs) -> Dict[str, Any]:
        """分析用户意图并生成澄清问题"""
        user_input = state["user_input"]
        conversation_history = state["conversation_history"]
        
        # 分析意图
        analysis_result = await self._analyze_intent(user_input, conversation_history)
        
        if analysis_result["intent_clear"]:
            return {
                "intent_clarified": True,
                "clarified_intent": analysis_result["intent"],
                "user_feedback_required": False
            }
        else:
            # 生成澄清问题
            questions = analysis_result["clarification_questions"]
            
            return {
                "intent_clarified": False,
                "clarification_questions": questions,
                "user_feedback_required": True,
                "pending_messages": [
                    Message(
                        id=f"clarification_{len(conversation_history)}",
                        type=MessageType.CLARIFICATION_REQUEST,
                        content=self._format_clarification_message(questions),
                        timestamp=datetime.now()
                    )
                ]
            }
    
    async def _analyze_intent(self, user_input: str, history: List[Message]) -> Dict[str, Any]:
        """使用LLM分析用户意图"""
        prompt = self._build_intent_analysis_prompt(user_input, history)
        response = await self.llm_service.generate(prompt)
        return self._parse_intent_analysis_response(response)
    
    def _build_intent_analysis_prompt(self, user_input: str, history: List[Message]) -> str:
        """构建意图分析提示词"""
        # 实现提示词构建逻辑
        pass
    
    def _format_clarification_message(self, questions: List[str]) -> str:
        """格式化澄清消息"""
        if len(questions) == 1:
            return f"为了更好地为您服务，我需要确认：{questions[0]}"
        else:
            formatted_questions = "\n".join([f"{i+1}. {q}" for i, q in enumerate(questions)])
            return f"为了更好地为您服务，我需要确认以下几个问题：\n{formatted_questions}"
```

### 4. 规划Agent (src/Specific/agents/planning.py)

```python
from typing import Dict, Any, List
from .base import BaseAgent
from ..models.state import SpecificState, Plan, PlanStep
from ..services.llm import LLMService
from datetime import datetime
import uuid

class PlanningAgent(BaseAgent):
    def __init__(self, llm_service: LLMService):
        super().__init__(
            name="planning",
            description="负责任务规划和分解",
            tools=["create_plan", "validate_plan", "estimate_time"]
        )
        self.llm_service = llm_service
    
    async def process(self, state: SpecificState, **kwargs) -> Dict[str, Any]:
        """创建任务执行计划"""
        clarified_intent = state["clarified_intent"]
        user_requirements = state.get("user_requirements", {})
        
        # 创建计划
        plan = await self._create_plan(clarified_intent, user_requirements)
        
        # 验证计划
        validation_result = await self._validate_plan(plan)
        
        if validation_result["valid"]:
            return {
                "task_plan": plan,
                "user_feedback_required": True,
                "pending_messages": [
                    Message(
                        id=f"plan_confirmation_{plan.id}",
                        type=MessageType.PLAN_CONFIRMATION,
                        content=self._format_plan_confirmation_message(plan),
                        timestamp=datetime.now()
                    )
                ]
            }
        else:
            # 计划验证失败，需要重新规划
            return {
                "error_info": {
                    "type": "planning_failed",
                    "message": validation_result["error_message"]
                }
            }
    
    async def _create_plan(self, task_description: str, requirements: Dict[str, Any]) -> Plan:
        """创建执行计划"""
        prompt = self._build_planning_prompt(task_description, requirements)
        response = await self.llm_service.generate(prompt)
        plan_data = self._parse_planning_response(response)
        
        # 构建Plan对象
        steps = [
            PlanStep(
                id=str(uuid.uuid4()),
                description=step["description"],
                tools=step["tools"],
                estimated_time=step["estimated_time"],
                dependencies=step.get("dependencies", [])
            )
            for step in plan_data["steps"]
        ]
        
        return Plan(
            id=str(uuid.uuid4()),
            title=plan_data["title"],
            overview=plan_data["overview"],
            steps=steps,
            total_estimated_time=plan_data["total_estimated_time"],
            created_at=datetime.now()
        )
    
    def _build_planning_prompt(self, task_description: str, requirements: Dict[str, Any]) -> str:
        """构建规划提示词"""
        available_tools = self.tool_registry.get_all_tools()
        
        prompt = f"""
        # 任务规划请求
        
        ## 任务描述
        {task_description}
        
        ## 用户要求
        {requirements}
        
        ## 可用工具
        {self._format_available_tools(available_tools)}
        
        请创建一个详细的执行计划，包括：
        1. 计划标题和概述
        2. 分阶段的执行步骤
        3. 每个步骤的工具选择
        4. 时间估算
        5. 步骤依赖关系
        
        输出格式为JSON。
        """
        return prompt
```

### 5. 工作流构建 (src/Specific/core/workflow.py)

```python
from langgraph import StateGraph, END
from typing import Dict, Any
from .state import SpecificState, WorkflowStatus
from ..agents import (
    IntentClarificationAgent,
    PlanningAgent,
    ExecutionAgent,
    MessageAgent
)

class SpecificWorkflow:
    def __init__(self, agents: Dict[str, Any]):
        self.agents = agents
        self.workflow = self._build_workflow()
    
    def _build_workflow(self) -> StateGraph:
        """构建LangGraph工作流"""
        workflow = StateGraph(SpecificState)
        
        # 添加节点
        workflow.add_node("intent_clarification", self._intent_clarification_node)
        workflow.add_node("planning", self._planning_node)
        workflow.add_node("plan_confirmation", self._plan_confirmation_node)
        workflow.add_node("execution", self._execution_node)
        workflow.add_node("summary", self._summary_node)
        
        # 设置入口点
        workflow.set_entry_point("intent_clarification")
        
        # 添加条件边
        workflow.add_conditional_edges(
            "intent_clarification",
            self._should_clarify_intent,
            {
                "clarify": "intent_clarification",
                "plan": "planning"
            }
        )
        
        workflow.add_conditional_edges(
            "planning",
            self._should_confirm_plan,
            {
                "confirm": "plan_confirmation",
                "replan": "planning"
            }
        )
        
        workflow.add_conditional_edges(
            "plan_confirmation",
            self._plan_confirmed,
            {
                "approved": "execution",
                "rejected": "planning"
            }
        )
        
        workflow.add_conditional_edges(
            "execution",
            self._has_more_steps,
            {
                "continue": "execution",
                "summary": "summary"
            }
        )
        
        workflow.add_edge("summary", END)
        
        return workflow.compile()
    
    async def _intent_clarification_node(self, state: SpecificState) -> SpecificState:
        """意图澄清节点"""
        agent = self.agents["intent_clarification"]
        result = await agent.process(state)
        
        # 更新状态
        state.update(result)
        state["workflow_status"] = WorkflowStatus.CLARIFYING_INTENT
        
        return state
    
    async def _planning_node(self, state: SpecificState) -> SpecificState:
        """规划节点"""
        agent = self.agents["planning"]
        result = await agent.process(state)
        
        state.update(result)
        state["workflow_status"] = WorkflowStatus.PLANNING
        
        return state
    
    def _should_clarify_intent(self, state: SpecificState) -> str:
        """判断是否需要澄清意图"""
        if state["intent_clarified"]:
            return "plan"
        return "clarify"
    
    def _should_confirm_plan(self, state: SpecificState) -> str:
        """判断是否需要确认计划"""
        if state.get("task_plan"):
            return "confirm"
        return "replan"
    
    def _plan_confirmed(self, state: SpecificState) -> str:
        """判断计划是否被确认"""
        if state["plan_approved"]:
            return "approved"
        return "rejected"
    
    def _has_more_steps(self, state: SpecificState) -> str:
        """判断是否还有更多步骤"""
        plan = state["task_plan"]
        current_index = state["current_step_index"]
        
        if current_index < len(plan.steps):
            return "continue"
        return "summary"
```

## 🧪 测试策略

### 1. 单元测试示例

```python
import pytest
from unittest.mock import Mock, AsyncMock
from Specific.agents.intent_clarification import IntentClarificationAgent
from Specific.models.state import SpecificState, Message

@pytest.fixture
def mock_llm_service():
    service = Mock()
    service.generate = AsyncMock()
    return service

@pytest.fixture
def intent_agent(mock_llm_service):
    return IntentClarificationAgent(mock_llm_service)

@pytest.mark.asyncio
async def test_intent_clarification_clear_intent(intent_agent, mock_llm_service):
    # 模拟LLM返回明确意图
    mock_llm_service.generate.return_value = {
        "intent_clear": True,
        "intent": "查询2025年京东外卖市场分析"
    }
    
    state = SpecificState(
        session_id="test_session",
        user_input="我想了解京东外卖的情况",
        conversation_history=[],
        intent_clarified=False
    )
    
    result = await intent_agent.process(state)
    
    assert result["intent_clarified"] is True
    assert result["clarified_intent"] == "查询2025年京东外卖市场分析"
    assert result["user_feedback_required"] is False

@pytest.mark.asyncio
async def test_intent_clarification_needs_clarification(intent_agent, mock_llm_service):
    # 模拟LLM返回需要澄清
    mock_llm_service.generate.return_value = {
        "intent_clear": False,
        "clarification_questions": ["您想了解哪个时间段的数据？", "您关注哪些具体指标？"]
    }
    
    state = SpecificState(
        session_id="test_session",
        user_input="我想了解外卖数据",
        conversation_history=[],
        intent_clarified=False
    )
    
    result = await intent_agent.process(state)
    
    assert result["intent_clarified"] is False
    assert len(result["clarification_questions"]) == 2
    assert result["user_feedback_required"] is True
    assert len(result["pending_messages"]) == 1
```

### 2. 集成测试示例

```python
@pytest.mark.asyncio
async def test_full_workflow_execution():
    # 创建工作流
    workflow = create_Specific_workflow()
    
    # 初始状态
    initial_state = SpecificState(
        session_id="integration_test",
        user_input="分析2025年京东外卖与美团的竞争情况",
        conversation_history=[],
        intent_clarified=False
    )
    
    # 执行工作流
    final_state = await workflow.ainvoke(initial_state)
    
    # 验证最终状态
    assert final_state["workflow_status"] == WorkflowStatus.COMPLETED
    assert final_state["intent_clarified"] is True
    assert final_state["task_plan"] is not None
    assert len(final_state["execution_results"]) > 0
```
