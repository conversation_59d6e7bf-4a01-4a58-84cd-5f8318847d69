# API接口文档 V4

## 概述

V4版本的API接口支持新的请求参数格式，同时保持向后兼容性。新接口增加了任务管理、沙箱环境、事件回调和扩展字段等功能。

## 请求格式

### ChatRequest 参数

```json
{
    "message": "",              // 用户发送的消息内容 (必填)
    "session_id": "",           // 会话ID (可选)
    "task_id": "",              // 任务ID (可选)
    "sandbox_id": "",           // 沙箱ID (可选)
    "event_webhook": "https://xuanji-dev.chehejia.com", // 上传事件回调地址 (可选)
    "extensions": {             // 扩展字段，字典类型 (可选)
        "tokens": [{            // 服务token列表
            "service_id": "",   // 服务ID（接入IdaaS）
            "access_token": ""  // 服务access_token
        }],
        "additional_fields": {} // 其他自定义扩展字段
    },
    "report_dsl": {             // 报表DSL数据结构 (可选)
        "01": {
            "type": "section",
            "title": "事件基本信息",
            "description": "",
            "content": [...]
        }
    },
    // 向后兼容字段
    "user_id": "",              // 用户ID (可选，向后兼容)
    "metadata": {}              // 额外元数据 (可选，向后兼容)
}
```

### 字段说明

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `message` | string | 是 | 用户发送的消息内容 |
| `session_id` | string | 否 | 会话ID，用于多轮对话 |
| `task_id` | string | 否 | 任务ID，用于任务跟踪 |
| `sandbox_id` | string | 否 | 沙箱ID，用于隔离环境 |
| `event_webhook` | string | 否 | 事件回调地址，用于异步通知 |
| `extensions` | object | 否 | 扩展字段对象 |
| `extensions.tokens` | array | 否 | 服务token列表，用于IdaaS集成 |
| `extensions.additional_fields` | object | 否 | 其他自定义扩展字段 |
| `report_dsl` | object | 否 | 报表DSL数据结构，用于生成报告 |
| `user_id` | string | 否 | 用户ID（向后兼容） |
| `metadata` | object | 否 | 额外元数据（向后兼容） |

## 响应格式

### ChatResponse 参数

```json
{
    "session_id": "",           // 会话ID
    "response": "",             // 系统响应内容
    "status": "",               // 当前工作流状态
    "requires_feedback": false, // 是否需要用户反馈
    "task_id": "",              // 任务ID（来自请求）
    "sandbox_id": "",           // 沙箱ID（来自请求）
    "event_webhook": "",        // 事件回调地址（来自请求）
    "extensions": {},           // 扩展字段（来自请求）
    "metadata": {               // 响应元数据
        "updated_at": "",       // 更新时间
        "intent_clarified": false,
        "intent_approved": false,
        "plan_approved": false,
        "message_count": 1
    }
}
```

## 使用示例

### 1. 基本请求（向后兼容）

```python
request = ChatRequest(
    message="你好",
    session_id="session_001",
    user_id="user_123"
)
```

### 2. 完整新格式请求

```python
from api.schemas import ChatRequest, ServiceToken, Extensions

# 创建服务token
service_token = ServiceToken(
    service_id="service_001",
    access_token="token_12345"
)

# 创建扩展字段
extensions = Extensions(
    tokens=[service_token],
    additional_fields={
        "custom_field": "custom_value",
        "nested_field": {"key": "value"}
    }
)

# 创建请求
request = ChatRequest(
    message="分析京东的市场情况",
    session_id="session_001",
    task_id="task_12345",
    sandbox_id="sandbox_67890",
    event_webhook="https://example.com/webhook",
    extensions=extensions
)
```

### 3. 任务跟踪请求

```python
request = ChatRequest(
    message="继续执行任务",
    session_id="session_001",
    task_id="task_12345"  # 保持相同的task_id进行跟踪
)
```

## 工作流状态

| 状态值 | 说明 |
|--------|------|
| `initializing` | 初始化中 |
| `clarifying_intent` | 意图澄清中 |
| `planning` | 规划中 |
| `confirming_plan` | 计划确认中 |
| `executing` | 执行中 |
| `summarizing` | 总结中 |
| `completed` | 已完成 |
| `failed` | 失败 |
| `waiting_for_human` | 等待人工干预 |

## 特性

### 1. 流式处理
- 使用 `astream` 进行实时流式处理
- 支持实时监听工作流执行进展
- 更快的响应速度

### 2. Agent自主路由
- 各个Agent返回Command进行自主路由
- 简化的节点实现，直接返回Agent结果
- 更清晰的架构设计

### 3. 向后兼容
- 完全支持V3版本的请求格式
- 新字段为可选，不影响现有集成
- 平滑升级路径

### 4. 扩展性
- 灵活的扩展字段支持
- 支持IdaaS服务集成
- 自定义字段支持

## 错误处理

错误响应格式保持一致，新增字段会包含在错误响应中：

```json
{
    "session_id": "error",
    "response": "抱歉，处理您的请求时发生了错误：...",
    "status": "error",
    "requires_feedback": false,
    "task_id": "task_12345",
    "sandbox_id": "sandbox_67890",
    "event_webhook": "https://example.com/webhook",
    "extensions": {...},
    "metadata": {"error": "..."}
}
```

## 测试

运行测试脚本验证接口：

```bash
python test_new_api_interface.py
```

测试覆盖：
- ✅ 新格式请求处理
- ✅ 扩展字段序列化
- ✅ 向后兼容性
- ✅ 多轮对话支持
- ✅ 流式处理功能

## 报告生成API

### 1. 基于Session生成报告

```bash
POST /report/generate?session_id={session_id}&access_token={token}
```

**说明**：基于指定session中的report_dsl数据生成HTML格式报告

**参数**：
- `session_id`: 会话ID（必填）
- `access_token`: 访问令牌（可选）

**响应**：
```json
{
    "success": true,
    "session_id": "session_001",
    "html_report": "<html><head>...</head><body>...</body></html>",
    "final_report": "# 品牌舆情分析报告...",
    "report_dsl": {...},
    "summary_data": {
        "ai_summary": "【整体情况】权威背书强势...",
        "key_metrics": [
            {"label": "统计时间", "value": "截止5月16日16:00"},
            {"label": "内容量", "value": 14069}
        ],
        "viewpoints": [
            {
                "viewpoint": "权威背书",
                "explain": "央视新闻和明星的参与...",
                "positive": "朱广权点赞理想MEGA...",
                "negative": "理想汽车与央视合作被质疑..."
            }
        ],
        "charts_info": [
            {"type": "pie", "title": "观点分类"}
        ]
    },
    "upload_result": {
        "success": true,
        "upload_result": {
            "url": "https://s3.amazonaws.com/bucket/report-session_001-20240116-143022.html",
            "key": "report-session_001-20240116-143022.html",
            "bucket": "xuanji-reports"
        },
        "filename": "report-session_001-20240116-143022.html",
        "message": "HTML报告上传成功"
    },
    "messages": [...],
    "message": "报告生成成功"
}
```

### 2. 直接使用DSL生成报告

```bash
POST /report/generate-with-dsl
Content-Type: application/json

{
    "report_dsl": {
        "01": {
            "type": "section",
            "title": "品牌舆情分析概览",
            "description": "京东品牌舆情监测分析报告",
            "content": [...]
        }
    },
    "access_token": "your_access_token"
}
```

**响应**：
```json
{
    "success": true,
    "html_report": "<html>...</html>",
    "final_report": "# 品牌舆情分析报告...",
    "report_dsl": {...},
    "summary_data": {
        "ai_summary": "【整体情况】权威背书强势...",
        "key_metrics": [...],
        "viewpoints": [...],
        "charts_info": [...]
    },
    "upload_result": {
        "success": true,
        "upload_result": {
            "url": "https://s3.amazonaws.com/bucket/report.html",
            "key": "report.html"
        },
        "filename": "report.html",
        "message": "HTML报告上传成功"
    },
    "messages": [...],
    "message": "报告生成成功"
}
```

### 3. 报表DSL格式示例

```json
{
    "01": {
        "type": "section",
        "title": "品牌舆情分析概览",
        "description": "京东品牌舆情监测分析报告",
        "content": [
            {
                "type": "descriptions",
                "data": [
                    {"id": 1, "label": "品牌名称", "value": "京东"},
                    {"id": 2, "label": "舆情评分", "value": "82分"}
                ]
            }
        ]
    },
    "02": {
        "type": "section",
        "title": "情感分析分布",
        "description": "品牌相关内容的情感倾向分析",
        "content": [
            {
                "type": "chart",
                "chart_type": "pie",
                "data": [
                    {"label": "正面", "value": 68, "color": "#52c41a"},
                    {"label": "负面", "value": 18, "color": "#ff4d4f"},
                    {"label": "中性", "value": 14, "color": "#faad14"}
                ]
            }
        ]
    }
}
```

### 4. 外部报告服务

报告生成通过调用外部服务实现：

```bash
curl -X POST https://console-playground.fed.chehejia.com/__ui/report \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"01": {"type": "section", "title": "事件基本信息", ...}}'
```

**特点**：
- 🎨 返回完整的HTML格式报表
- 📊 支持图表、表格、指标等多种可视化组件
- 🔧 基于DSL数据结构灵活配置
- 🚀 高性能报告渲染引擎
- 🤖 智能解析DSL中的AI总结和关键数据
- 📈 自动提取关键指标、观点分析和图表信息
- ☁️ 自动上传HTML报告到S3云存储
- 🔗 提供可访问的报告链接

### 5. DSL智能解析功能

ReportAgent会自动解析DSL中的结构化数据：

#### AI总结提取
```json
{
    "section2": {
        "title": "AI总结",
        "description": "【整体情况】\\n权威背书强势...",
        "content": []
    }
}
```

#### 关键指标提取
```json
{
    "content": [
        {
            "type": "descriptions",
            "data": [
                {"label": "统计时间", "value": "截止5月16日16:00"},
                {"label": "内容量", "value": 14069}
            ]
        }
    ]
}
```

#### 观点分析提取
```json
{
    "content": [
        {
            "type": "table",
            "data": [
                {
                    "viewpoint": "权威背书",
                    "explain": "央视新闻和明星的参与...",
                    "positive": "朱广权点赞理想MEGA...",
                    "negative": "理想汽车与央视合作被质疑..."
                }
            ]
        }
    ]
}
```

#### 解析结果格式
```json
{
    "summary_data": {
        "ai_summary": "完整的AI总结文本",
        "key_metrics": [
            {"label": "指标名称", "value": "指标值"}
        ],
        "viewpoints": [
            {
                "viewpoint": "观点名称",
                "explain": "观点解释",
                "positive": "正向内容",
                "negative": "负向内容"
            }
        ],
        "charts_info": [
            {"type": "图表类型", "title": "图表标题"}
        ]
    }
}
```

### 6. S3云存储上传功能

报告生成完成后，HTML文件会自动上传到S3云存储：

#### 上传流程
1. **生成HTML报告** - 调用外部报告服务获取HTML内容
2. **创建临时文件** - 将HTML内容写入临时文件
3. **上传到S3** - 调用上传接口将文件存储到云端
4. **返回访问链接** - 提供可访问的报告URL
5. **清理临时文件** - 删除本地临时文件

#### 上传接口
```bash
POST https://xuanji-backend-service-dev.inner.chj.cloud/api/v1/ois/upload
Content-Type: multipart/form-data

file: report.html (HTML文件)
```

#### 文件命名规则
```
report-{session_id}-{timestamp}.html
例如: report-session_001-20240116-143022.html
```

#### 上传结果格式
```json
{
    "upload_result": {
        "success": true,
        "upload_result": {
            "url": "https://s3.amazonaws.com/bucket/report.html",
            "key": "report.html",
            "bucket": "xuanji-reports",
            "size": 15420,
            "contentType": "text/html"
        },
        "filename": "report-session_001-20240116-143022.html",
        "message": "HTML报告上传成功"
    }
}
```

#### 错误处理
- 上传失败时，仍会返回HTML内容供手动下载
- 提供详细的错误信息和重试建议
- 自动清理临时文件，避免磁盘空间占用

#### 安全性
- 支持文件类型验证（仅允许HTML文件）
- 自动生成唯一文件名，避免冲突
- 临时文件使用安全的临时目录

## 注意事项

1. **向后兼容性**：V4版本完全兼容V3的请求格式
2. **扩展字段**：`extensions`字段提供了灵活的扩展能力
3. **任务跟踪**：通过`task_id`可以跟踪任务执行状态
4. **沙箱隔离**：`sandbox_id`提供环境隔离能力
5. **事件回调**：`event_webhook`支持异步事件通知
6. **IdaaS集成**：通过`tokens`字段支持服务认证
7. **报告生成**：`report_dsl`字段支持自定义报告格式，调用外部报告服务生成专业HTML报告
