[project]
name = "specific-agent"
version = "0.1.0"
description = "A modular multi-agent system built with LangGraph using planning-execution pattern"
authors = [
    {name = "zhuxiaobing", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "langgraph>=0.2.0",
    "langchain>=0.3.0",
    "langchain-openai>=0.2.0",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "pydantic>=2.5.0",
    "httpx>=0.25.0",
    "python-multipart>=0.0.6",
    "python-dotenv>=1.0.0",
    "structlog>=23.2.0",
    "rich>=13.7.0",
    "langgraph-checkpoint-sqlite>=2.0.10",
    "langgraph-checkpoint-postgres>=2.0.21",
    "agentops-event-sdk-python>=0.2.0",
    "langchain-mcp-adapters>=0.1.7",
    "PyJWT>=2.10.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.7.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/specific"]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]



[[tool.uv.index]]
name = "private-pypi"
url = "https://artifactory.ep.chehejia.com/artifactory/api/pypi/licloud-pypi-l5-release-local/simple"

[tool.uv.sources]
agentops-event-sdk-python = { index = "private-pypi" }
