#!/usr/bin/env python3
"""
简单测试流式API
"""

import asyncio
import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_simple():
    """简单测试"""
    print("🚀 开始简单测试...")
    
    try:
        from api.main import SpecificAPI
        from api.schemas import ChatRequest
        
        print("✅ 导入成功")
        
        # 初始化API
        api = SpecificAPI()
        print("✅ API初始化成功")
        
        # 创建简单请求
        request = ChatRequest(
            message="你好",
            session_id="test_001",
            user_id="test_user"
        )
        
        print(f"📤 发送请求: {request.message}")
        
        # 调用API
        response = await api.chat(request)
        
        print(f"📥 收到响应: {response.response}")
        print("✅ 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_simple())
