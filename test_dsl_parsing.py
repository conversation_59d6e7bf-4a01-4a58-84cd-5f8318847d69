#!/usr/bin/env python3
"""
测试DSL解析功能
"""

import asyncio
import json
from src.agents.report import ReportAgent
from langchain_openai import ChatOpenAI


# 示例DSL数据（基于您提供的格式）
SAMPLE_DSL = {
    "section1": {
        "type": "section",
        "title": "基本信息",
        "description": "",
        "content": [
            {
                "type": "descriptions",
                "data": [
                    {
                        "id": 1,
                        "label": "统计时间",
                        "value": "截止5月16日16:00"
                    },
                    {
                        "id": 2,
                        "label": "内容量",
                        "value": 14069
                    },
                    {
                        "id": 3,
                        "label": "口碑指数",
                        "value": "80.15%"
                    },
                    {
                        "id": 4,
                        "label": "正面情感比例",
                        "value": "80.40%"
                    },
                    {
                        "id": 5,
                        "label": "负面情感比例",
                        "value": "0.25%"
                    }
                ]
            }
        ]
    },
    "section2": {
        "type": "section",
        "title": "AI总结",
        "description": "【整体情况】\\n权威背书强势，理想汽车通过央视新闻和明星（朱广权、岳云鹏）的参与获得了强有力的品牌背书，展现了技术实力和产品优势。\\n【核心亮点】\\n权威背书强势：央视新闻为理想汽车技术实力提供有力背书，明星认可进一步增强品牌公信力。\\n趣味互动受好评：直播中的幽默互动和车载语音助手的表现获得网友广泛好评。\\n家庭定位明确：理想汽车在家庭用车方面的优势得到用户认可。\\n【风险预警】\\n预算管理争议：李想严格审批预算的做法引发部分争议，需要平衡节约与企业形象的关系。",
        "content": []
    },
    "section3": {
        "type": "section",
        "title": "观点分类",
        "description": "",
        "content": [
            {
                "type": "pie",
                "option": {
                    "series": [
                        {
                            "data": [
                                {
                                    "value": 67.0,
                                    "name": "权威背书 67%"
                                },
                                {
                                    "value": 21.0,
                                    "name": "趣味互动 21%"
                                },
                                {
                                    "value": 5.0,
                                    "name": "预算管理争议 5%"
                                },
                                {
                                    "value": 4.0,
                                    "name": "家庭需求 4%"
                                }
                            ]
                        }
                    ]
                }
            },
            {
                "type": "table",
                "title": "观点详情",
                "description": "各观点详细信息",
                "columns": [
                    {
                        "prop": "viewpoint",
                        "label": "观点"
                    },
                    {
                        "prop": "explain",
                        "label": "解释说明"
                    },
                    {
                        "prop": "positive",
                        "label": "正向"
                    },
                    {
                        "prop": "negative",
                        "label": "负向"
                    }
                ],
                "data": [
                    {
                        "id": 1,
                        "viewpoint": "权威背书",
                        "explain": "央视新闻和明星（如岳云鹏、朱广权）的参与和认可能为理想汽车提供了权威背书，增强了品牌公信力，并展示其技术领先性。",
                        "positive": "朱广权点赞理想MEGA的\"公路高铁级\"静谧性\\n岳云鹏称车内安静得像魔法\\n央视新闻为理想汽车技术实力提供有力背书",
                        "negative": "理想汽车与央视合作被质疑噱头大于价值"
                    },
                    {
                        "id": 2,
                        "viewpoint": "趣味互动",
                        "explain": "直播中的幽默互动（如理想同学的接梗能力、岳云鹏和朱广权的表现）受到网友广泛好评，认为形式生动有趣且展现了科技魅力。",
                        "positive": "李想、朱广权、岳云鹏，这阵容太豪华！\\n车载语音助手今晚报位出道！\\n直播期间车载语音助手直接封神！",
                        "negative": ""
                    }
                ]
            }
        ]
    }
}


async def test_dsl_parsing():
    """测试DSL解析功能"""
    print("🧪 测试DSL解析功能...")
    
    try:
        # 创建ReportAgent
        llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
        report_agent = ReportAgent(llm=llm)
        
        print(f"📋 原始DSL数据:")
        print(json.dumps(SAMPLE_DSL, indent=2, ensure_ascii=False))
        
        # 解析DSL
        print(f"\n🔍 解析DSL中的AI总结和关键数据...")
        summary_data = report_agent.parse_dsl_summary(SAMPLE_DSL)
        
        print(f"\n📊 解析结果:")
        print(f"AI总结长度: {len(summary_data['ai_summary'])} 字符")
        print(f"关键指标数量: {len(summary_data['key_metrics'])}")
        print(f"观点数量: {len(summary_data['viewpoints'])}")
        print(f"图表数量: {len(summary_data['charts_info'])}")
        
        print(f"\n🤖 AI总结内容:")
        print(summary_data['ai_summary'])
        
        print(f"\n📈 关键指标:")
        for metric in summary_data['key_metrics']:
            print(f"  - {metric['label']}: {metric['value']}")
        
        print(f"\n💭 观点分析:")
        for viewpoint in summary_data['viewpoints']:
            print(f"  - {viewpoint['viewpoint']}: {viewpoint['explain'][:50]}...")
        
        # 生成文本总结
        print(f"\n📝 生成结构化文本总结...")
        text_summary = report_agent.generate_text_summary(summary_data)
        
        print(f"\n📄 文本总结:")
        print(text_summary)
        
        print(f"\n✅ DSL解析测试完成!")
        
    except Exception as e:
        print(f"❌ DSL解析测试失败: {e}")


async def test_full_report_generation():
    """测试完整的报告生成流程"""
    print("\n🚀 测试完整报告生成流程...")
    
    try:
        # 创建ReportAgent
        llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
        report_agent = ReportAgent(llm=llm)
        
        # 模拟state数据
        state_data = {
            "session_id": "test_dsl_parsing_001",
            "report_dsl": SAMPLE_DSL,
            "extensions": {
                "tokens": [{"access_token": "test_token"}]
            }
        }
        
        # 创建writer来收集消息
        messages = []
        def writer(data):
            messages.append(data)
            print(f"📢 Agent消息: {data.get('agent_message', data)}")
        
        print(f"📤 开始执行ReportAgent...")
        
        # 注意：这会尝试调用真实的外部API
        # 如果没有有效的access_token，可能会失败
        try:
            result = await report_agent.execute(state_data, writer)
            
            print(f"\n✅ 报告生成完成!")
            print(f"状态: {result.update.get('workflow_status')}")
            print(f"HTML报告长度: {len(result.update.get('html_report', ''))}")
            print(f"文本报告长度: {len(result.update.get('final_report', ''))}")
            
            # 显示解析后的数据
            summary_data = result.update.get('summary_data', {})
            if summary_data:
                print(f"\n📊 解析数据统计:")
                print(f"  AI总结: {len(summary_data.get('ai_summary', ''))} 字符")
                print(f"  关键指标: {len(summary_data.get('key_metrics', []))} 个")
                print(f"  观点分析: {len(summary_data.get('viewpoints', []))} 个")
            
        except Exception as e:
            print(f"⚠️ 外部API调用失败（预期）: {e}")
            print("这是正常的，因为需要有效的access_token和网络连接")
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")


async def main():
    """主测试函数"""
    print("🎯 开始测试DSL解析功能\n")
    
    # 测试DSL解析
    await test_dsl_parsing()
    
    # 测试完整流程
    await test_full_report_generation()
    
    print("\n✅ 所有测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
