#!/usr/bin/env python3
"""
测试新的report_dsl_status和report_dsl_message字段功能
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))
sys.path.insert(0, os.path.join(os.path.abspath('.'), 'src'))

from src.core.workflow import SpecificWorkflow
from src.models.state import create_initial_state, WorkflowStatus
from src.api.schemas import ChatRequest
from src.api.main import SpecificAPI


def get_test_stream_writer():
    """获取测试用的StreamWriter"""
    def test_writer(data):
        print(f"📡 {data}")
    return test_writer


async def test_report_dsl_success():
    """测试SUCCESS状态的DSL处理"""
    print("=== 测试SUCCESS状态的DSL处理 ===")
    
    workflow = SpecificWorkflow()
    writer = get_test_stream_writer()
    
    # 创建成功状态的测试数据
    state = create_initial_state(
        session_id="test_success_001",
        user_input="生成品牌舆情分析报告",
        user_id="test_user",
        report_dsl_data={"section1": {"title": "测试报告", "content": []}},
        report_dsl_status="SUCCESS",
        report_dsl_message=None
    )
    state["workflow_status"] = WorkflowStatus.REPORT
    
    print(f"输入: {state['user_input']}")
    print(f"DSL状态: {state['report_dsl_status']}")
    print(f"DSL消息: {state['report_dsl_message']}")
    
    try:
        # 直接调用ReportAgent的execute方法
        result = await workflow.report_agent.execute(state, writer)
        
        print(f"路由目标: {result.goto}")
        print(f"工作流状态: {result.update.get('workflow_status')}")
        
        if result.update.get('workflow_status') != WorkflowStatus.FAILED:
            print("✅ SUCCESS状态测试通过")
        else:
            print("❌ SUCCESS状态测试失败")
            error_info = result.update.get('error_info', {})
            print(f"错误信息: {error_info.get('message', 'Unknown error')}")
            
    except Exception as e:
        print(f"⚠️ 外部API调用失败（预期）: {e}")
        print("这是正常的，因为需要有效的access_token和网络连接")


async def test_report_dsl_failed():
    """测试FAILED状态的DSL处理"""
    print("\n=== 测试FAILED状态的DSL处理 ===")
    
    workflow = SpecificWorkflow()
    writer = get_test_stream_writer()
    
    # 创建失败状态的测试数据
    state = create_initial_state(
        session_id="test_failed_001",
        user_input="生成品牌舆情分析报告",
        user_id="test_user",
        report_dsl_data=None,  # 没有DSL数据
        report_dsl_status="FAILED",
        report_dsl_message="DSL生成失败：数据源连接超时"
    )
    state["workflow_status"] = WorkflowStatus.REPORT
    
    print(f"输入: {state['user_input']}")
    print(f"DSL状态: {state['report_dsl_status']}")
    print(f"DSL消息: {state['report_dsl_message']}")
    
    # 直接调用ReportAgent的execute方法
    result = await workflow.report_agent.execute(state, writer)
    
    print(f"路由目标: {result.goto}")
    print(f"工作流状态: {result.update.get('workflow_status')}")
    
    if result.update.get('workflow_status') == WorkflowStatus.FAILED:
        print("✅ FAILED状态测试通过")
        error_info = result.update.get('error_info', {})
        print(f"错误信息: {error_info.get('message', 'Unknown error')}")
    else:
        print("❌ FAILED状态测试失败")


async def test_api_request_with_new_fields():
    """测试API请求中的新字段"""
    print("\n=== 测试API请求中的新字段 ===")
    
    # 测试成功状态的请求
    print("\n1. 测试SUCCESS状态的请求")
    request_success = ChatRequest(
        message="请分析京东的品牌舆情",
        session_id="test_api_success_001",
        user_id="test_user",
        report_dsl_data={"section1": {"title": "京东品牌分析", "content": []}},
        report_dsl_status="SUCCESS",
        report_dsl_message=None
    )
    
    print(f"消息: {request_success.message}")
    print(f"DSL状态: {request_success.report_dsl_status}")
    print(f"DSL消息: {request_success.report_dsl_message}")
    print(f"DSL数据: {bool(request_success.report_dsl_data)}")
    
    # 测试失败状态的请求
    print("\n2. 测试FAILED状态的请求")
    request_failed = ChatRequest(
        message="请分析京东的品牌舆情",
        session_id="test_api_failed_001",
        user_id="test_user",
        report_dsl_data=None,
        report_dsl_status="FAILED",
        report_dsl_message="数据源连接失败，无法生成DSL"
    )
    
    print(f"消息: {request_failed.message}")
    print(f"DSL状态: {request_failed.report_dsl_status}")
    print(f"DSL消息: {request_failed.report_dsl_message}")
    print(f"DSL数据: {bool(request_failed.report_dsl_data)}")
    
    print("✅ API请求字段测试通过")


async def test_no_dsl_data():
    """测试没有DSL数据的情况（应该使用默认DSL）"""
    print("\n=== 测试没有DSL数据的情况 ===")
    
    workflow = SpecificWorkflow()
    writer = get_test_stream_writer()
    
    # 创建没有DSL数据的测试状态
    state = create_initial_state(
        session_id="test_no_dsl_001",
        user_input="生成品牌舆情分析报告",
        user_id="test_user",
        report_dsl_data=None,
        report_dsl_status=None,  # 没有状态
        report_dsl_message=None
    )
    state["workflow_status"] = WorkflowStatus.REPORT
    
    print(f"输入: {state['user_input']}")
    print(f"DSL状态: {state['report_dsl_status']}")
    print(f"DSL数据: {state['report_dsl_data']}")
    
    try:
        # 直接调用ReportAgent的execute方法
        result = await workflow.report_agent.execute(state, writer)
        
        print(f"路由目标: {result.goto}")
        print(f"工作流状态: {result.update.get('workflow_status')}")
        
        if result.update.get('workflow_status') != WorkflowStatus.FAILED:
            print("✅ 无DSL数据默认处理测试通过")
        else:
            print("❌ 无DSL数据默认处理测试失败")
            error_info = result.update.get('error_info', {})
            print(f"错误信息: {error_info.get('message', 'Unknown error')}")
            
    except Exception as e:
        print(f"⚠️ 外部API调用失败（预期）: {e}")
        print("这是正常的，因为需要有效的access_token和网络连接")


async def main():
    """运行所有测试"""
    print("🚀 开始测试新的report_dsl字段功能...")
    
    await test_report_dsl_success()
    await test_report_dsl_failed()
    await test_api_request_with_new_fields()
    await test_no_dsl_data()
    
    print("\n🎉 所有测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
