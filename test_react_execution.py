#!/usr/bin/env python3
"""
测试使用React Agent的ExecutionAgent
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


async def test_react_execution_agent():
    """测试React ExecutionAgent"""
    print("🚀 测试React ExecutionAgent...")
    
    try:
        from src.agents.execution import ExecutionAgent
        from src.models.state import WorkflowStatus
        
        # 创建模拟LLM
        class MockLLM:
            async def ainvoke(self, messages):
                class MockResponse:
                    content = """### 步骤1执行报告

**执行内容**：分析京东外卖品牌数据收集需求

**工具使用**：使用了文件系统工具搜索相关数据文件，检查了/tmp目录下的数据源

**执行过程**：
1. 使用文件系统工具扫描可用数据源
2. 分析数据结构和格式
3. 确定数据收集策略
4. 验证数据质量和完整性

**执行结果**：成功识别了主要数据源，包括用户评论、订单数据、竞品对比数据。通过工具验证了数据的可访问性。

**下一步准备**：为数据分析阶段准备了清洁的数据集，建立了数据处理流程"""
                return MockResponse()
            
            def bind_tools(self, tools):
                return self
        
        # 测试不同配置
        configs = [
            ("启用MCP React Agent", True),
            ("禁用MCP (普通LLM)", False)
        ]
        
        for config_name, enable_mcp in configs:
            print(f"\n📋 测试配置: {config_name}")
            
            # 创建ExecutionAgent
            mock_llm = MockLLM()
            agent = ExecutionAgent(llm=mock_llm, enable_mcp=enable_mcp)
            
            print(f"  ✅ Agent创建成功")
            print(f"  🔧 MCP启用: {agent.enable_mcp}")
            
            # 获取能力
            capabilities = agent.get_capabilities()
            print(f"  💪 Agent能力: {capabilities}")
            
            # 测试单步执行
            print(f"  🎯 测试单步执行...")
            try:
                step_result = await agent.execute_step(
                    user_input="请分析京东外卖品牌表现",
                    plan_title="京东外卖品牌分析",
                    current_step=0,
                    step_details={
                        "title": "数据收集",
                        "description": "收集京东外卖相关数据和用户反馈"
                    }
                )
                
                print(f"  ✅ 单步执行成功")
                print(f"  📝 结果预览: {step_result[:100]}...")
                
            except Exception as step_error:
                print(f"  ❌ 单步执行失败: {step_error}")
            
            # 创建测试状态
            test_state = {
                "session_id": f"test_react_{enable_mcp}",
                "user_input": "请分析京东外卖品牌表现",
                "task_plan": {
                    "title": "京东外卖品牌分析",
                    "steps": [
                        {
                            "title": "数据收集",
                            "description": "收集京东外卖相关数据"
                        },
                        {
                            "title": "数据分析",
                            "description": "分析收集到的数据"
                        }
                    ]
                },
                "workflow_status": WorkflowStatus.EXECUTING,
                "messages": []
            }
            
            print(f"  📝 测试计划: {test_state['task_plan']['title']}")
            print(f"  📊 步骤数量: {len(test_state['task_plan']['steps'])}")
            
            # 执行完整任务
            try:
                result = agent.execute(test_state)
                
                print(f"  ✅ 完整执行成功，路由到: {result.goto}")
                
                # 检查结果
                if "execution_results" in result.update:
                    results = result.update["execution_results"]
                    print(f"  📈 执行统计:")
                    print(f"    - 总步骤: {len(results)}")
                    print(f"    - 成功步骤: {len([r for r in results if not r.get('error', False)])}")
                
                if "execution_report" in result.update:
                    report = result.update["execution_report"]
                    print(f"  📝 生成了执行报告 ({len(report)} 字符)")
                
            except Exception as e:
                print(f"  ❌ 完整执行失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_mcp_availability():
    """测试MCP可用性"""
    print("\n🔧 测试MCP可用性...")
    
    try:
        from langchain_mcp_adapters.client import MultiServerMCPClient
        from langgraph.prebuilt import create_react_agent
        print("  ✅ MCP适配器可用")
        
        # 测试简单配置
        server_configs = {
            "filesystem": {
                "command": "npx",
                "args": ["-y", "@modelcontextprotocol/server-filesystem", "/tmp"],
                "transport": "stdio",
            }
        }
        
        print("  📡 测试MCP客户端初始化...")
        client = MultiServerMCPClient(server_configs)
        print("  ✅ MCP客户端初始化成功")
        
        # 尝试获取工具
        try:
            tools = await client.get_tools()
            print(f"  🔧 获取到 {len(tools)} 个工具")
            
            if tools:
                print("  📋 工具示例:")
                for i, tool in enumerate(tools[:2]):  # 只显示前2个
                    print(f"    {i+1}. {tool.name}: {tool.description[:40]}...")
                
                # 测试create_react_agent
                from langchain_openai import ChatOpenAI
                if os.getenv("OPENAI_API_KEY"):
                    print("  🤖 测试create_react_agent...")
                    llm = ChatOpenAI(model="gpt-4o-mini")
                    react_agent = create_react_agent(llm, tools)
                    print("  ✅ React Agent创建成功")
                else:
                    print("  ⚠️ 未设置OPENAI_API_KEY，跳过React Agent测试")
                    
        except Exception as tool_error:
            print(f"  ⚠️ 获取工具失败: {tool_error}")
        
        return True
        
    except ImportError:
        print("  ⚠️ MCP适配器未安装")
        print("  💡 运行: pip install langchain-mcp-adapters")
        return False
    except Exception as e:
        print(f"  ❌ MCP测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("🧪 React ExecutionAgent测试")
    print("=" * 50)
    
    # 检查环境
    print("🔍 检查环境...")
    
    # 检查MCP可用性
    mcp_available = True
    try:
        from langchain_mcp_adapters.client import MultiServerMCPClient
        print("✅ MCP适配器可用")
    except ImportError:
        print("❌ MCP适配器不可用")
        mcp_available = False
    
    # 检查API密钥
    if os.getenv("OPENAI_API_KEY"):
        print("✅ OpenAI API密钥已设置")
    else:
        print("⚠️ OpenAI API密钥未设置，将使用模拟LLM")
    
    print()
    
    # 运行测试
    tests = [
        ("React ExecutionAgent", test_react_execution_agent),
    ]
    
    if mcp_available:
        tests.append(("MCP可用性", test_mcp_availability))
    
    results = []
    for test_name, test_func in tests:
        print(f"🧪 运行测试: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
        print()
    
    # 汇总结果
    print("📊 测试结果:")
    passed = 0
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总结: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！React ExecutionAgent工作正常。")
    else:
        print("⚠️ 部分测试失败，请检查配置。")
    
    print("\n💡 使用提示:")
    print("1. 安装MCP适配器: pip install langchain-mcp-adapters")
    print("2. ExecutionAgent现在使用React Agent处理每个步骤")
    print("3. 自动选择和使用MCP工具执行任务")
    print("4. 提供更详细的工具使用报告")


if __name__ == "__main__":
    asyncio.run(main())
