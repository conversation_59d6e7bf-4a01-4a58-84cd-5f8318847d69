{"info": {"_postman_id": "unified-analysis-tests-v4", "name": "SupervisorAgent V4 API测试", "description": "测试V4版本的新API接口格式，包括任务管理、沙箱环境、事件回调和扩展字段功能", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. 健康检查", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "response": []}, {"name": "2. 系统状态", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/system/status", "host": ["{{base_url}}"], "path": ["system", "status"]}}, "response": []}, {"name": "3. 问候消息测试 (向后兼容)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"你好\",\n  \"user_id\": \"{{user_id}}\"\n}"}, "url": {"raw": "{{base_url}}/chat", "host": ["{{base_url}}"], "path": ["chat"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has required fields\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('session_id');", "    pm.expect(responseJson).to.have.property('response');", "    pm.expect(responseJson).to.have.property('status');", "    pm.expect(responseJson).to.have.property('task_id');", "    pm.expect(responseJson).to.have.property('sandbox_id');", "});", "", "pm.test(\"Greeting message handled correctly\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.response).to.include('您好');", "});"], "type": "text/javascript"}}], "response": []}, {"name": "4. 完整新格式请求测试", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"我想分析京东的市场情况\",\n  \"session_id\": \"{{session_id}}\",\n  \"task_id\": \"{{task_id}}\",\n  \"sandbox_id\": \"{{sandbox_id}}\",\n  \"event_webhook\": \"{{event_webhook}}\",\n  \"extensions\": {\n    \"tokens\": [{\n      \"service_id\": \"{{service_id}}\",\n      \"access_token\": \"{{access_token}}\"\n    }],\n    \"additional_fields\": {\n      \"custom_field_1\": \"custom_value_1\",\n      \"custom_field_2\": {\n        \"nested\": \"value\"\n      }\n    }\n  },\n  \"user_id\": \"{{user_id}}\"\n}"}, "url": {"raw": "{{base_url}}/chat", "host": ["{{base_url}}"], "path": ["chat"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// 提取session_id并设置为环境变量", "if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    if (responseJson.session_id) {", "        pm.environment.set(\"session_id\", responseJson.session_id);", "        console.log(\"Session ID saved:\", responseJson.session_id);", "    }", "}", "", "pm.test(\"New API format fields present\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.task_id).to.eql(pm.environment.get('task_id'));", "    pm.expect(responseJson.sandbox_id).to.eql(pm.environment.get('sandbox_id'));", "    pm.expect(responseJson.event_webhook).to.eql(pm.environment.get('event_webhook'));", "    pm.expect(responseJson.extensions).to.be.an('object');", "});", "", "pm.test(\"Extensions field structure correct\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.extensions.tokens).to.be.an('array');", "    pm.expect(responseJson.extensions.tokens[0].service_id).to.eql(pm.environment.get('service_id'));", "    pm.expect(responseJson.extensions.additional_fields).to.be.an('object');", "});"], "type": "text/javascript"}}], "response": []}, {"name": "5. 澄清信息补充测试", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"我想分析京东外卖在2025年与美团、饿了么的竞争态势，重点关注市场份额和用户增长\",\n  \"session_id\": \"{{session_id}}\",\n  \"task_id\": \"{{task_id}}\",\n  \"user_id\": \"{{user_id}}\"\n}"}, "url": {"raw": "{{base_url}}/chat", "host": ["{{base_url}}"], "path": ["chat"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Session continuity maintained\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.session_id).to.eql(pm.environment.get('session_id'));", "    pm.expect(responseJson.task_id).to.eql(pm.environment.get('task_id'));", "});"], "type": "text/javascript"}}], "response": []}, {"name": "6. 确认计划测试", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"好的，请按照这个计划执行\",\n  \"session_id\": \"{{session_id}}\",\n  \"task_id\": \"{{task_id}}\",\n  \"user_id\": \"{{user_id}}\"\n}"}, "url": {"raw": "{{base_url}}/chat", "host": ["{{base_url}}"], "path": ["chat"]}}, "response": []}, {"name": "7. 任务跟踪测试 (仅task_id)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"查看任务进度\",\n  \"task_id\": \"{{task_id}}\",\n  \"user_id\": \"{{user_id}}\"\n}"}, "url": {"raw": "{{base_url}}/chat", "host": ["{{base_url}}"], "path": ["chat"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Task ID preserved\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.task_id).to.eql(pm.environment.get('task_id'));", "});"], "type": "text/javascript"}}], "response": []}, {"name": "8. 获取会话信息", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/session/{{session_id}}", "host": ["{{base_url}}"], "path": ["session", "{{session_id}}"]}}, "response": []}, {"name": "9. 获取工作流状态", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/session/{{session_id}}/workflow", "host": ["{{base_url}}"], "path": ["session", "{{session_id}}", "workflow"]}}, "response": []}, {"name": "10. 获取Agent信息", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/system/agents", "host": ["{{base_url}}"], "path": ["system", "agents"]}}, "response": []}], "variable": [{"key": "base_url", "value": "http://localhost:8000"}]}