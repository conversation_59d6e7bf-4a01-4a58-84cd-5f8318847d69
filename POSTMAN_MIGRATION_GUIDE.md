# Postman文件迁移指南

## 概述

本文档说明从V3版本的Postman文件迁移到V4版本的变化和改进。

## 文件对比

### 旧版本文件 (V3)
- `SupervisorAgent_Test_Environment.postman_environment.json`
- `SupervisorAgent_Unified_Analysis_Tests.postman_collection.json`

### 新版本文件 (V4)
- `SupervisorAgent_Test_Environment_V4.postman_environment.json`
- `SupervisorAgent_V4_API_Tests.postman_collection.json`

## 主要变化

### 1. 环境变量新增

| 变量名 | V3 | V4 | 说明 |
|--------|----|----|------|
| `base_url` | ✅ | ✅ | API服务器地址 |
| `session_id` | ✅ | ✅ | 会话ID |
| `user_id` | ✅ | ✅ | 用户ID |
| `task_id` | ❌ | ✅ | **新增** 任务ID |
| `sandbox_id` | ❌ | ✅ | **新增** 沙箱ID |
| `event_webhook` | ❌ | ✅ | **新增** 事件回调地址 |
| `service_id` | ❌ | ✅ | **新增** 服务ID |
| `access_token` | ❌ | ✅ | **新增** 访问令牌 |

### 2. 请求格式变化

#### V3 请求格式 (向后兼容)
```json
{
  "message": "你好",
  "user_id": "postman_test_user"
}
```

#### V4 完整请求格式
```json
{
  "message": "我想分析京东的市场情况",
  "session_id": "{{session_id}}",
  "task_id": "{{task_id}}",
  "sandbox_id": "{{sandbox_id}}",
  "event_webhook": "{{event_webhook}}",
  "extensions": {
    "tokens": [{
      "service_id": "{{service_id}}",
      "access_token": "{{access_token}}"
    }],
    "additional_fields": {
      "custom_field_1": "custom_value_1",
      "custom_field_2": {
        "nested": "value"
      }
    }
  },
  "user_id": "{{user_id}}"
}
```

### 3. 响应格式变化

#### V3 响应格式
```json
{
  "session_id": "...",
  "response": "...",
  "status": "...",
  "requires_feedback": false,
  "metadata": {...}
}
```

#### V4 响应格式
```json
{
  "session_id": "...",
  "response": "...",
  "status": "...",
  "requires_feedback": false,
  "task_id": "...",           // 新增
  "sandbox_id": "...",        // 新增
  "event_webhook": "...",     // 新增
  "extensions": {...},        // 新增
  "metadata": {...}
}
```

### 4. 测试用例变化

| 测试用例 | V3 | V4 | 变化说明 |
|----------|----|----|----------|
| 健康检查 | ✅ | ✅ | 保持不变 |
| 系统状态 | ✅ | ✅ | 保持不变 |
| 获取工具列表 | ✅ | ❌ | 移除（简化） |
| 获取Agent信息 | ✅ | ✅ | 保持不变 |
| 问候消息测试 | ✅ | ✅ | **增强** 验证新字段 |
| 分析请求测试 | ✅ | ✅ | **重命名** 为"完整新格式请求测试" |
| 确认消息测试 | ✅ | ✅ | **重命名** 为"确认计划测试" |
| 补充信息测试 | ✅ | ✅ | **重命名** 为"澄清信息补充测试" |
| 拒绝消息测试 | ✅ | ❌ | 移除（简化） |
| 任务跟踪测试 | ❌ | ✅ | **新增** |
| 获取会话信息 | ✅ | ✅ | 保持不变 |
| 获取工作流状态 | ✅ | ✅ | 保持不变 |

## 迁移步骤

### 1. 备份旧文件
```bash
cp SupervisorAgent_Test_Environment.postman_environment.json SupervisorAgent_Test_Environment_V3_backup.json
cp SupervisorAgent_Unified_Analysis_Tests.postman_collection.json SupervisorAgent_Unified_Analysis_Tests_V3_backup.json
```

### 2. 导入新文件
1. 在Postman中导入新的环境文件：`SupervisorAgent_Test_Environment_V4.postman_environment.json`
2. 导入新的集合文件：`SupervisorAgent_V4_API_Tests.postman_collection.json`

### 3. 更新环境变量
根据实际环境调整以下变量：
- `base_url` - API服务器地址
- `event_webhook` - 实际的回调地址
- `service_id` - 实际的服务ID
- `access_token` - 实际的访问令牌

### 4. 验证测试
运行新的测试集合，确保所有测试通过。

## 新功能测试

### 1. 扩展字段测试
验证`extensions`字段的正确处理：
- `tokens`数组包含服务认证信息
- `additional_fields`支持自定义字段

### 2. 任务跟踪测试
验证`task_id`字段的持久性：
- 同一任务的多次请求保持相同的`task_id`
- 响应中正确返回`task_id`

### 3. 沙箱环境测试
验证`sandbox_id`字段的隔离性：
- 不同沙箱环境的请求互不影响
- 响应中正确返回`sandbox_id`

### 4. 事件回调测试
验证`event_webhook`字段的配置：
- 请求中包含回调地址
- 响应中正确返回回调地址

## 向后兼容性

V4版本完全支持V3格式的请求：

### 兼容性测试
```json
{
  "message": "你好",
  "user_id": "postman_test_user"
}
```

### 预期响应
```json
{
  "session_id": "...",
  "response": "...",
  "status": "...",
  "requires_feedback": false,
  "task_id": null,           // 新字段为null
  "sandbox_id": null,        // 新字段为null
  "event_webhook": null,     // 新字段为null
  "extensions": null,        // 新字段为null
  "metadata": {...}
}
```

## 测试验证增强

### 新增验证项
1. **新字段存在性验证**
   ```javascript
   pm.expect(responseJson).to.have.property('task_id');
   pm.expect(responseJson).to.have.property('sandbox_id');
   pm.expect(responseJson).to.have.property('event_webhook');
   pm.expect(responseJson).to.have.property('extensions');
   ```

2. **扩展字段结构验证**
   ```javascript
   pm.expect(responseJson.extensions.tokens).to.be.an('array');
   pm.expect(responseJson.extensions.additional_fields).to.be.an('object');
   ```

3. **数据一致性验证**
   ```javascript
   pm.expect(responseJson.task_id).to.eql(pm.environment.get('task_id'));
   pm.expect(responseJson.sandbox_id).to.eql(pm.environment.get('sandbox_id'));
   ```

## 故障排除

### 常见迁移问题

1. **环境变量缺失**
   - 确保所有新增的环境变量都已设置
   - 检查变量名称拼写是否正确

2. **请求格式错误**
   - 验证JSON格式是否正确
   - 确认扩展字段结构符合规范

3. **响应验证失败**
   - 检查API版本是否为V4
   - 确认服务器支持新的响应格式

### 调试建议

1. 先运行向后兼容性测试，确保基本功能正常
2. 逐步测试新功能，定位具体问题
3. 使用Postman Console查看详细的请求和响应日志
4. 对比V3和V4的响应差异，确认迁移正确性

## 总结

V4版本的Postman文件提供了：
- ✅ 完整的新API格式支持
- ✅ 向后兼容性保证
- ✅ 增强的测试验证
- ✅ 更丰富的功能测试
- ✅ 清晰的迁移路径

通过本迁移指南，可以平滑地从V3版本升级到V4版本，享受新功能的同时保持现有集成的稳定性。
