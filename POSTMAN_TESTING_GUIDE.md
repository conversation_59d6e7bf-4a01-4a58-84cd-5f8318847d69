# Postman测试指南

## 概述

本项目提供了两套Postman测试文件，用于测试V4版本的API接口：

1. **SupervisorAgent_Test_Environment_V4.postman_environment.json** - 测试环境配置
2. **SupervisorAgent_V4_API_Tests.postman_collection.json** - API测试集合

## 文件说明

### 环境文件 (Environment)

包含以下预配置变量：

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `base_url` | `http://localhost:8000` | API服务器地址 |
| `session_id` | `` | 会话ID（测试时自动设置） |
| `user_id` | `postman_test_user` | 用户ID |
| `task_id` | `postman_task_001` | 任务ID |
| `sandbox_id` | `postman_sandbox_001` | 沙箱ID |
| `event_webhook` | `https://xuanji-dev.chehejia.com/webhook` | 事件回调地址 |
| `service_id` | `postman_service_001` | 服务ID |
| `access_token` | `postman_token_12345` | 访问令牌 |

### 测试集合 (Collection)

包含以下测试用例：

1. **健康检查** - 验证API服务状态
2. **系统状态** - 获取系统运行状态
3. **问候消息测试 (向后兼容)** - 测试旧格式请求
4. **完整新格式请求测试** - 测试包含所有新字段的请求
5. **澄清信息补充测试** - 测试多轮对话
6. **确认计划测试** - 测试计划确认流程
7. **任务跟踪测试** - 测试仅使用task_id的请求
8. **获取会话信息** - 测试会话状态查询
9. **获取工作流状态** - 测试工作流状态查询
10. **获取Agent信息** - 测试Agent信息查询

## 使用步骤

### 1. 导入文件

1. 打开Postman
2. 点击 "Import" 按钮
3. 选择并导入两个JSON文件：
   - `SupervisorAgent_Test_Environment_V4.postman_environment.json`
   - `SupervisorAgent_V4_API_Tests.postman_collection.json`

### 2. 设置环境

1. 在Postman右上角选择 "SupervisorAgent测试环境 V4"
2. 根据需要修改环境变量（如API服务器地址）

### 3. 启动API服务

确保API服务正在运行：

```bash
cd /path/to/brand-specific-agent
python -m uvicorn src.api.app:app --host 0.0.0.0 --port 8000
```

### 4. 运行测试

#### 单个测试
- 选择任意测试用例
- 点击 "Send" 按钮
- 查看响应结果

#### 批量测试
- 选择整个集合
- 点击 "Run" 按钮
- 选择要运行的测试用例
- 点击 "Run SupervisorAgent V4 API测试"

## 测试重点

### 新API格式验证

**完整新格式请求测试** 验证以下内容：

```json
{
  "message": "我想分析京东的市场情况",
  "session_id": "{{session_id}}",
  "task_id": "{{task_id}}",
  "sandbox_id": "{{sandbox_id}}",
  "event_webhook": "{{event_webhook}}",
  "extensions": {
    "tokens": [{
      "service_id": "{{service_id}}",
      "access_token": "{{access_token}}"
    }],
    "additional_fields": {
      "custom_field_1": "custom_value_1",
      "custom_field_2": {
        "nested": "value"
      }
    }
  },
  "user_id": "{{user_id}}"
}
```

### 响应格式验证

测试验证响应包含以下字段：

- `session_id` - 会话ID
- `response` - 系统响应
- `status` - 工作流状态
- `requires_feedback` - 是否需要反馈
- `task_id` - 任务ID（新增）
- `sandbox_id` - 沙箱ID（新增）
- `event_webhook` - 事件回调地址（新增）
- `extensions` - 扩展字段（新增）
- `metadata` - 元数据

### 向后兼容性验证

**问候消息测试** 使用旧格式请求：

```json
{
  "message": "你好",
  "user_id": "{{user_id}}"
}
```

验证新字段在响应中为 `null` 或空值。

## 自动化测试

每个测试用例都包含自动化验证脚本：

- **状态码验证** - 确保返回200状态码
- **字段存在性验证** - 确保必需字段存在
- **数据一致性验证** - 确保请求和响应数据一致
- **会话连续性验证** - 确保多轮对话session_id一致

## 故障排除

### 常见问题

1. **连接失败**
   - 检查API服务是否启动
   - 确认base_url设置正确

2. **认证失败**
   - 检查access_token是否有效
   - 确认service_id配置正确

3. **测试失败**
   - 查看响应内容和错误信息
   - 检查环境变量设置
   - 确认API版本兼容性

### 调试技巧

1. 使用Postman Console查看详细日志
2. 检查Pre-request Script和Tests脚本
3. 验证环境变量值是否正确设置
4. 对比请求和响应的JSON格式

## 扩展测试

可以基于现有测试创建更多测试用例：

1. **错误处理测试** - 测试无效请求格式
2. **边界值测试** - 测试极限参数值
3. **并发测试** - 测试多个并发请求
4. **性能测试** - 测试响应时间和吞吐量
