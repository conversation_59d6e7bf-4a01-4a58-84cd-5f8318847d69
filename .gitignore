
*.egg-info
*.pyc

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
venv/
env/
ENV/
.env

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Jupyter Notebook
.ipynb_checkpoints

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/

# Logs
*.log
logs/

# Local development
.env.local
.env.development.local
.env.test.local
.env.production.local

# Dependencies
node_modules/

# LangGraph specific
.langgraph/

# Temporary files
tmp/
temp/

.langgraph_api
