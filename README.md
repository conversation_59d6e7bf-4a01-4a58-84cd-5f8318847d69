# Brand-Specific Agent - 多智能体舆情分析系统

## 🎯 项目概述

Brand-Specific Agent 是一个基于 LangGraph 构建的多智能体舆情分析系统，专门为品牌舆情监控和分析而设计。系统采用状态驱动的工作流架构，通过多个专业化智能体协作完成复杂的舆情分析任务。

## 🏗️ 系统架构

### 核心组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API Gateway   │────│  Workflow Core  │────│  Agent Network  │
│                 │    │                 │    │                 │
│ • REST API      │    │ • LangGraph     │    │ • 5个专业Agent  │
│ • Session Mgmt  │    │ • State Mgmt    │    │ • 工具集成      │
│ • Event Stream  │    │ • Route Logic   │    │ • MCP连接       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 智能体架构

系统包含5个专业化智能体，每个都有明确的职责分工：

1. **SupervisorAgent** - 协调路由智能体
2. **IntentAnalysisAgent** - 意图分析智能体
3. **PlanningAgent** - 计划制定智能体
4. **ExecutionAgent** - 任务执行智能体
5. **ReportAgent** - 报告生成智能体

## 🔄 工作流程

### 状态驱动的工作流

```mermaid
graph TD
    A[用户输入] --> B[Supervisor路由]
    B --> C[意图分析]
    C --> D{意图是否清晰?}
    D -->|否| E[澄清问题]
    E --> F[等待用户回复]
    F --> C
    D -->|是| G[制定计划]
    G --> H{计划是否确认?}
    H -->|否| I[修改计划]
    I --> G
    H -->|是| J[执行任务]
    J --> K[生成报告]
    K --> L[完成]
```

### 工作流状态

系统使用以下状态来控制流程：

- `INITIALIZING` - 初始化状态
- `CLARIFYING_INTENT` - 意图澄清阶段
- `PLANNING` - 计划制定阶段
- `CONFIRMING_PLAN` - 计划确认阶段
- `EXECUTING` - 任务执行阶段
- `SUMMARIZING` - 总结阶段
- `REPORT` - 报告生成阶段
- `COMPLETED` - 完成状态
- `FAILED` - 失败状态

## 🤖 智能体详解

### 1. SupervisorAgent (协调路由)

**职责**: 统一消息分析和智能路由决策

**核心功能**:
- 分析用户消息类型和意图
- 基于业务状态进行路由决策
- 协调各智能体间的工作流转


### 2. IntentAnalysisAgent (意图分析)

**职责**: 理解和澄清用户需求

**核心功能**:
- 判断用户需求是否足够清晰
- 生成针对性澄清问题
- 分析用户回复意图（同意/补充/拒绝）
- 结构化需求信息

**澄清逻辑**:
```python
# 必需信息检查
required_info = {
    "analysis_object": "具体品牌/产品/人物",
    "analysis_dimension": "声量/情感/传播/口碑等",
    "time_range": "时间范围（可选，默认近一个月）",
    "platform_range": "平台范围（可选，默认全平台）"
}
```

### 3. PlanningAgent (计划制定)

**职责**: 制定详细的执行计划

**核心功能**:
- 基于澄清后的需求生成执行计划
- 将复杂任务分解为具体步骤
- 支持计划修改和优化
- 提供降级计划机制

**计划结构**:
```python
class ExecutionPlan:
    title: str                    # 计划标题
    steps: List[PlanStep]         # 执行步骤列表

class PlanStep:
    title: str                    # 步骤标题
    description: str              # 步骤描述
    skip_execute: bool = False    # 是否跳过执行
```

### 4. ExecutionAgent (任务执行)

**职责**: 执行具体的分析任务

**核心功能**:
- 遍历执行计划中的每个步骤
- 集成MCP (Model Context Protocol) 工具
- 动态生成JWT访问令牌
- 详细的执行日志和调试信息

**MCP集成**:
```python
# 动态JWT令牌生成
def create_access_token(user_id: str) -> str:
    payload = {
        "sub": user_id,
        "exp": datetime.utcnow() + timedelta(minutes=2880),  # 48小时
        "iat": datetime.utcnow(),
        "type": "access"
    }
    return jwt.encode(payload, "brand_event", algorithm="HS256")

# MCP工具调用
server_configs = {
    "brand_event": {
        "transport": "streamable_http",
        "url": "http://************:5003/mcp/marketing",
        "headers": {
            "sign": create_access_token(user_id)  # 动态令牌
        }
    }
}
```

### 5. ReportAgent (报告生成)

**职责**: 生成最终分析报告

**核心功能**:
- 整合执行结果
- 生成结构化报告
- 支持多种输出格式
- 报告质量验证

## 🛠️ 技术栈

### 核心框架
- **LangGraph**: 工作流编排和状态管理
- **LangChain**: LLM集成和工具链
- **FastAPI**: REST API服务
- **Pydantic**: 数据验证和序列化

### 智能体技术
- **BaseAgent**: 统一的智能体基类
- **Structured Output**: 结构化LLM输出
- **Tool Integration**: 工具集成框架
- **MCP Protocol**: 模型上下文协议

### 状态管理
- **SpecificState**: 业务状态模型
- **MessagesState**: 消息状态继承
- **Persistent Storage**: 状态持久化

## 🚀 快速开始

### 环境要求
```bash
Python >= 3.9
LangGraph >= 0.2.0
LangChain >= 0.3.0
FastAPI >= 0.100.0
```

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动服务
```bash
# 启动API服务
python -m uvicorn src.api.main:app --host 0.0.0.0 --port 8000

# 或使用开发模式
python -m uvicorn src.api.main:app --reload
```

### API使用示例

#### 1. 发起对话
```bash
curl -X POST "http://localhost:8000/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "我想分析理想汽车L9的舆情情况",
    "user_id": "user123",
    "task_id": "task456"
  }'
```

#### 2. 继续对话
```bash
curl -X POST "http://localhost:8000/chat" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "existing_session_id",
    "message": "同意这个分析计划",
    "user_id": "user123"
  }'
```

#### 3. 查询会话状态
```bash
curl "http://localhost:8000/sessions/{session_id}"
```

## 📊 系统特性

### 1. 状态驱动架构
- 基于业务状态的确定性路由
- 完整的状态生命周期管理
- 状态持久化和恢复

### 2. 多轮交互支持
- 智能意图分析和澄清
- 上下文感知的对话管理
- 用户确认和反馈机制

### 3. 流式处理
- 实时状态更新推送
- 智能体消息流式传输
- 异步任务执行

### 4. 工具集成
- MCP协议标准化工具接入
- 动态认证和权限管理
- 丰富的调试和监控信息

### 5. 可扩展性
- 模块化智能体设计
- 插件式工具扩展
- 灵活的配置管理


## 🔍 典型使用场景

### 场景1: 品牌舆情监控
```
用户: "我想了解理想汽车L9最近的舆情情况"
系统: 意图分析 → 确认需求 → 制定计划 → 执行分析 → 生成报告
```

### 场景2: 竞品对比分析
```
用户: "对比理想MAGA和腾势D9的消费者偏好变化"
系统: 澄清时间范围 → 确认分析维度 → 执行对比分析 → 输出趋势报告
```

### 场景3: 多轮澄清对话
```
用户: "帮我分析一下舆情"
系统: "请告诉我您想分析哪个具体的品牌、产品、人物或事件？"
用户: "理想汽车的口碑情况"
系统: 确认需求 → 制定计划 → 执行分析
```

**注**: 本系统专为品牌舆情分析场景设计，采用多智能体协作架构，提供完整的从需求澄清到报告生成的端到端解决方案。