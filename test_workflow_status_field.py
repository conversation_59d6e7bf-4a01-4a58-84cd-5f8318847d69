#!/usr/bin/env python3
"""
测试ChatRequest中新增的workflow_status字段
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_workflow_status_field():
    """测试workflow_status字段"""
    print("🧪 测试ChatRequest中的workflow_status字段...")
    
    try:
        from api.schemas import ChatRequest
        from src.models.state import WorkflowStatus
        
        # 1. 测试不传workflow_status（默认行为）
        print("\n1. 测试默认行为（不传workflow_status）...")
        request1 = ChatRequest(
            message="你好",
            session_id="test_001"
        )
        print(f"✅ 默认请求创建成功")
        print(f"   workflow_status: {request1.workflow_status}")
        
        # 2. 测试传入具体的workflow_status
        print("\n2. 测试传入具体的workflow_status...")
        request2 = ChatRequest(
            message="继续执行任务",
            session_id="test_002",
            workflow_status=WorkflowStatus.EXECUTING
        )
        print(f"✅ 带workflow_status的请求创建成功")
        print(f"   workflow_status: {request2.workflow_status}")
        print(f"   workflow_status.value: {request2.workflow_status.value}")
        
        # 3. 测试不同的workflow_status值
        print("\n3. 测试不同的workflow_status值...")
        test_statuses = [
            WorkflowStatus.CLARIFYING_INTENT,
            WorkflowStatus.PLANNING,
            WorkflowStatus.EXECUTING,
            WorkflowStatus.REPORT,
            WorkflowStatus.COMPLETED
        ]
        
        for status in test_statuses:
            request = ChatRequest(
                message=f"测试{status.value}状态",
                session_id=f"test_{status.value}",
                workflow_status=status
            )
            print(f"   ✅ {status.value}: {request.workflow_status}")
        
        # 4. 测试JSON序列化
        print("\n4. 测试JSON序列化...")
        request3 = ChatRequest(
            message="分析京东的市场情况",
            session_id="test_003",
            workflow_status=WorkflowStatus.PLANNING,
            task_id="task_123",
            user_id="user_456"
        )
        
        request_dict = request3.dict()
        print(f"✅ JSON序列化成功")
        print(f"   workflow_status在dict中: {request_dict.get('workflow_status')}")
        
        # 5. 测试create_initial_state函数
        print("\n5. 测试create_initial_state函数...")
        from src.models.state import create_initial_state
        
        # 不传workflow_status
        state1 = create_initial_state(
            session_id="test_state_001",
            user_input="你好"
        )
        print(f"✅ 默认状态创建成功: {state1['workflow_status']}")
        
        # 传入workflow_status
        state2 = create_initial_state(
            session_id="test_state_002",
            user_input="继续执行",
            workflow_status=WorkflowStatus.EXECUTING
        )
        print(f"✅ 自定义状态创建成功: {state2['workflow_status']}")
        
        print("\n🎉 所有测试通过！workflow_status字段功能正常")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_workflow_status_field()
