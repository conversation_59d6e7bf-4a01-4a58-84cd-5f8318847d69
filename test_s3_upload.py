#!/usr/bin/env python3
"""
测试S3上传功能
"""

import asyncio
import json
from src.agents.report import ReportAgent
from langchain_openai import ChatOpenAI


# 示例HTML内容
SAMPLE_HTML = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>品牌舆情分析报告</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #1890ff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .title {
            font-size: 28px;
            color: #1890ff;
            margin: 0;
        }
        .subtitle {
            font-size: 16px;
            color: #666;
            margin: 10px 0 0 0;
        }
        .section {
            margin-bottom: 30px;
        }
        .section-title {
            font-size: 20px;
            color: #333;
            border-left: 4px solid #1890ff;
            padding-left: 15px;
            margin-bottom: 15px;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            text-align: center;
            border: 1px solid #e9ecef;
        }
        .metric-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #1890ff;
        }
        .summary {
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .summary-title {
            font-size: 18px;
            color: #389e0d;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .summary-content {
            line-height: 1.6;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">品牌舆情分析报告</h1>
            <p class="subtitle">基于AI智能分析的综合舆情监测报告</p>
        </div>
        
        <div class="section">
            <h2 class="section-title">关键指标</h2>
            <div class="metrics">
                <div class="metric-card">
                    <div class="metric-label">统计时间</div>
                    <div class="metric-value">截止5月16日16:00</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">内容量</div>
                    <div class="metric-value">14,069</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">口碑指数</div>
                    <div class="metric-value">80.15%</div>
                </div>
                <div class="metric-card">
                    <div class="metric-label">正面情感比例</div>
                    <div class="metric-value">80.40%</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2 class="section-title">AI智能总结</h2>
            <div class="summary">
                <div class="summary-title">整体情况</div>
                <div class="summary-content">
                    权威背书强势，理想汽车通过央视新闻和明星（朱广权、岳云鹏）的参与获得了强有力的品牌背书，展现了技术实力和产品优势。
                </div>
            </div>
            <div class="summary">
                <div class="summary-title">核心亮点</div>
                <div class="summary-content">
                    权威背书强势：央视新闻为理想汽车技术实力提供有力背书，明星认可进一步增强品牌公信力。<br>
                    趣味互动受好评：直播中的幽默互动和车载语音助手的表现获得网友广泛好评。<br>
                    家庭定位明确：理想汽车在家庭用车方面的优势得到用户认可。
                </div>
            </div>
        </div>
    </div>
</body>
</html>
"""


async def test_s3_upload():
    """测试S3上传功能"""
    print("🧪 测试S3上传功能...")
    
    try:
        # 创建ReportAgent
        llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
        report_agent = ReportAgent(llm=llm)
        
        print(f"📄 HTML内容长度: {len(SAMPLE_HTML)} 字符")
        
        # 测试上传功能
        print(f"\n🚀 开始上传HTML到S3...")
        upload_result = await report_agent.upload_html_to_s3(
            html_content=SAMPLE_HTML,
            filename="test-report-sample.html"
        )
        
        print(f"\n📊 上传结果:")
        print(json.dumps(upload_result, indent=2, ensure_ascii=False))
        
        if upload_result["success"]:
            print(f"\n✅ 上传成功!")
            upload_info = upload_result.get("upload_result", {})
            print(f"📁 文件名: {upload_result.get('filename')}")
            print(f"🔗 访问链接: {upload_info.get('url', 'N/A')}")
            print(f"📋 上传信息: {upload_info}")
        else:
            print(f"\n❌ 上传失败: {upload_result.get('message')}")
            print(f"🔍 错误详情: {upload_result.get('error')}")
        
    except Exception as e:
        print(f"❌ S3上传测试失败: {e}")


async def test_full_workflow_with_upload():
    """测试完整的报告生成和上传流程"""
    print("\n🚀 测试完整的报告生成和上传流程...")
    
    try:
        # 创建ReportAgent
        llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
        report_agent = ReportAgent(llm=llm)
        
        # 创建示例DSL
        sample_dsl = report_agent.create_brand_analysis_dsl("京东")
        
        # 模拟state数据
        state_data = {
            "session_id": "test_s3_upload_001",
            "report_dsl_data": sample_dsl,
            "report_dsl_status": "SUCCESS",
            "report_dsl_message": None,
            "extensions": {
                "tokens": [{"access_token": "test_token"}]
            }
        }
        
        # 创建writer来收集消息
        messages = []
        def writer(data):
            messages.append(data)
            print(f"📢 Agent消息: {data.get('agent_message', data)}")
        
        print(f"📤 开始执行完整的报告生成流程...")
        
        try:
            # 执行ReportAgent（包含报告生成和S3上传）
            result = await report_agent.execute(state_data, writer)
            
            print(f"\n✅ 完整流程执行完成!")
            print(f"状态: {result.update.get('workflow_status')}")
            
            # 检查上传结果
            upload_result = result.update.get('upload_result', {})
            if upload_result.get('success'):
                print(f"\n📁 S3上传成功:")
                upload_info = upload_result.get('upload_result', {})
                print(f"  文件名: {upload_result.get('filename')}")
                print(f"  访问链接: {upload_info.get('url', 'N/A')}")
            else:
                print(f"\n⚠️ S3上传失败: {upload_result.get('message', 'Unknown error')}")
            
            # 显示其他结果
            print(f"\nHTML报告长度: {len(result.update.get('html_report', ''))}")
            print(f"文本报告长度: {len(result.update.get('final_report', ''))}")
            
            # 显示解析后的数据
            summary_data = result.update.get('summary_data', {})
            if summary_data:
                print(f"\n📊 解析数据统计:")
                print(f"  AI总结: {len(summary_data.get('ai_summary', ''))} 字符")
                print(f"  关键指标: {len(summary_data.get('key_metrics', []))} 个")
                print(f"  观点分析: {len(summary_data.get('viewpoints', []))} 个")
            
        except Exception as e:
            print(f"⚠️ 外部API调用失败（预期）: {e}")
            print("这是正常的，因为需要有效的access_token和网络连接")
        
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")


async def main():
    """主测试函数"""
    print("🎯 开始测试S3上传功能\n")
    
    # 测试S3上传
    await test_s3_upload()
    
    # 测试完整流程
    await test_full_workflow_with_upload()
    
    print("\n✅ 所有测试完成!")


if __name__ == "__main__":
    asyncio.run(main())
