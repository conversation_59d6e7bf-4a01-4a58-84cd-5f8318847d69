{"info": {"_postman_id": "unified-analysis-tests", "name": "SupervisorAgent统一分析测试", "description": "测试SupervisorAgent的统一用户消息分析和路由决策功能", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "1. 健康检查", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}}, "response": []}, {"name": "2. 系统状态", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/system/status", "host": ["{{base_url}}"], "path": ["system", "status"]}}, "response": []}, {"name": "3. 获取工具列表", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/system/tools", "host": ["{{base_url}}"], "path": ["system", "tools"]}}, "response": []}, {"name": "4. 获取Agent信息", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/system/agents", "host": ["{{base_url}}"], "path": ["system", "agents"]}}, "response": []}, {"name": "5. 问候消息测试 (greeting)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"你好\",\n  \"user_id\": \"postman_test_greeting\"\n}"}, "url": {"raw": "{{base_url}}/chat", "host": ["{{base_url}}"], "path": ["chat"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has required fields\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson).to.have.property('session_id');", "    pm.expect(responseJson).to.have.property('response');", "    pm.expect(responseJson).to.have.property('status');", "});", "", "pm.test(\"Greeting message handled correctly\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.response).to.include('您好');", "});"], "type": "text/javascript"}}], "response": []}, {"name": "6. 分析请求测试 (supplement)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"我想分析理想汽车的舆情\",\n  \"user_id\": \"postman_test_analysis\"\n}"}, "url": {"raw": "{{base_url}}/chat", "host": ["{{base_url}}"], "path": ["chat"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "// 提取session_id并设置为环境变量", "if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    if (responseJson.session_id) {", "        pm.environment.set(\"session_id\", responseJson.session_id);", "        console.log(\"Session ID saved:\", responseJson.session_id);", "    }", "}", "", "pm.test(\"Analysis request routed correctly\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.status).to.be.oneOf(['clarifying_intent', 'initializing']);", "    pm.expect(responseJson.requires_feedback).to.be.a('boolean');", "});"], "type": "text/javascript"}}], "response": []}, {"name": "7. 确认消息测试 (agreement)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"好的，确认\",\n  \"session_id\": \"{{session_id}}\",\n  \"user_id\": \"postman_test_analysis\"\n}"}, "url": {"raw": "{{base_url}}/chat", "host": ["{{base_url}}"], "path": ["chat"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Agreement message processed\", function () {", "    const responseJson = pm.response.json();", "    pm.expect(responseJson.session_id).to.eql(pm.environment.get('session_id'));", "});"], "type": "text/javascript"}}], "response": []}, {"name": "8. 补充信息测试 (detailed_description)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"我想分析理想汽车在抖音平台近一个月的传播情况，重点关注用户评价和销量趋势\",\n  \"session_id\": \"{{session_id}}\",\n  \"user_id\": \"postman_test_analysis\"\n}"}, "url": {"raw": "{{base_url}}/chat", "host": ["{{base_url}}"], "path": ["chat"]}}, "response": []}, {"name": "9. 拒绝消息测试 (rejection)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"message\": \"不对，重新来\",\n  \"session_id\": \"{{session_id}}\",\n  \"user_id\": \"postman_test_analysis\"\n}"}, "url": {"raw": "{{base_url}}/chat", "host": ["{{base_url}}"], "path": ["chat"]}}, "response": []}, {"name": "10. 获取会话信息", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/session/{{session_id}}", "host": ["{{base_url}}"], "path": ["session", "{{session_id}}"]}}, "response": []}, {"name": "11. 获取工作流状态", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/session/{{session_id}}/workflow", "host": ["{{base_url}}"], "path": ["session", "{{session_id}}", "workflow"]}}, "response": []}], "variable": [{"key": "base_url", "value": "http://localhost:8000"}]}