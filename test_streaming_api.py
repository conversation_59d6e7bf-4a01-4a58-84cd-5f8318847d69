#!/usr/bin/env python3
"""
测试流式API的简单脚本
"""

import asyncio
import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from api.main import SpecificAPI
from api.schemas import ChatRequest

async def test_streaming_api():
    """测试流式API"""
    print("🚀 测试流式API...")
    
    try:
        # 初始化API
        api = SpecificAPI()
        
        # 创建测试请求
        request = ChatRequest(
            message="你好，我想分析一下京东的市场情况",
            session_id="test_stream_001",
            user_id="test_user"
        )
        
        print(f"📤 发送请求: {request.message}")
        
        # 调用流式API
        response = await api.chat(request)
        
        print(f"📥 收到响应:")
        print(f"  Session ID: {response.session_id}")
        print(f"  Status: {response.status}")
        print(f"  Requires Feedback: {response.requires_feedback}")
        print(f"  Response: {response.response}")
        print(f"  Metadata: {response.metadata}")
        
        # 测试后续对话
        print("\n🔄 测试后续对话...")
        follow_up_request = ChatRequest(
            message="请详细分析一下京东在电商领域的竞争优势",
            session_id="test_stream_001",  # 使用相同的session_id
            user_id="test_user"
        )
        
        print(f"📤 发送后续请求: {follow_up_request.message}")
        
        follow_up_response = await api.chat(follow_up_request)
        
        print(f"📥 收到后续响应:")
        print(f"  Session ID: {follow_up_response.session_id}")
        print(f"  Status: {follow_up_response.status}")
        print(f"  Requires Feedback: {follow_up_response.requires_feedback}")
        print(f"  Response: {follow_up_response.response}")
        
        print("\n✅ 流式API测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_streaming_api())
