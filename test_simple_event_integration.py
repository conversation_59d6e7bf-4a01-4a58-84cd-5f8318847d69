#!/usr/bin/env python3
"""
测试简化的事件处理器集成
"""

import asyncio
import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

async def test_simple_event_integration():
    """测试简化的事件处理器集成"""
    print("🚀 测试简化的事件处理器集成...")
    
    try:
        from api.main import SpecificAPI
        from api.schemas import ChatRequest
        
        # 初始化API
        api = SpecificAPI()
        
        # 创建包含事件回调的请求
        request = ChatRequest(
            message="你好",
            session_id="test_simple_001",
            task_id="task_12345",
            sandbox_id="sandbox_67890",
            event_webhook="https://xuanji-dev.chehejia.com/webhook",
            user_id="test_user"
        )
        
        print("📤 发送包含事件回调的请求:")
        print(f"  Message: {request.message}")
        print(f"  Event Webhook: {request.event_webhook}")
        
        # 调用API，这应该会在_generate_simple_response中触发事件发送
        response = await api.chat(request)
        
        print(f"\n📥 收到响应:")
        print(f"  Session ID: {response.session_id}")
        print(f"  Status: {response.status}")
        print(f"  Response: {response.response}")
        print(f"  Event Webhook: {response.event_webhook}")
        
        print("\n✅ 简化事件处理器集成测试完成!")
        print("💡 注意：助手消息事件应该已经发送到配置的webhook地址")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_get_context_function():
    """测试get_context函数"""
    print("\n🔧 测试get_context函数...")
    
    try:
        from event.manus_event_handler import get_context
        
        # 测试字典类型的state
        dict_state = {
            "session_id": "test_session_001",
            "task_id": "test_task_001",
            "sandbox_id": "test_sandbox_001",
            "event_webhook": "https://test.webhook.com",
            "extensions": {"test": "value"}
        }
        
        print("📤 测试字典类型的state:")
        context = get_context(dict_state)
        print(f"  提取的context: {context}")
        
        # 测试对象类型的state（模拟SpecificState）
        class MockState:
            def __init__(self):
                self.session_id = "test_session_002"
                self.task_id = "test_task_002"
                self.sandbox_id = "test_sandbox_002"
                self.event_webhook = "https://test2.webhook.com"
                self.extensions = {"test2": "value2"}
        
        obj_state = MockState()
        
        print("📤 测试对象类型的state:")
        context = get_context(obj_state)
        print(f"  提取的context: {context}")
        
        print("✅ get_context函数测试完成!")
        
    except Exception as e:
        print(f"❌ get_context测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_event_handler_directly():
    """直接测试事件处理器"""
    print("\n🔧 直接测试事件处理器...")
    
    try:
        from event.manus_event_handler import event_handler
        
        # 创建测试状态（字典格式）
        test_state = {
            "session_id": "test_direct_001",
            "task_id": "task_direct_001",
            "sandbox_id": "sandbox_direct_001",
            "event_webhook": "https://xuanji-dev.chehejia.com/webhook",
            "extensions": {"test": "value"}
        }
        
        print("📤 发送测试事件:")
        
        # 测试发送助手消息
        event_handler.send_agent_message(
            state=test_state,
            content="这是一条测试助手消息"
        )
        print("  ✅ 助手消息事件已发送")
        
        # 测试发送实时状态
        event_handler.send_live_status(
            text="测试实时状态更新",
            state=test_state
        )
        print("  ✅ 实时状态事件已发送")
        
        print("✅ 事件处理器直接测试完成!")
        
    except Exception as e:
        print(f"❌ 直接测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_get_context_function()
    test_event_handler_directly()
    asyncio.run(test_simple_event_integration())
